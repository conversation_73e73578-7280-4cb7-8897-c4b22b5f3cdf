import { useCallback, useState, useEffect } from "react";
import clsx from "clsx";
import { CSSTransition } from "react-transition-group";
import { Icon } from "@hg/launcher-share";
import { SidebarConfig, SidebarType } from "@/pages/SocialMedia/hooks";
import { Arrow } from "./Arrow";

function useImagePreload(url?: string) {
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    if (!url) return;
    const img = new Image();
    img.src = url;
    img.onload = () => setLoaded(true);
    img.onerror = () => setLoaded(false);
  }, [url]);

  return loaded;
}

export const SidebarItem: React.FC<SidebarConfig["data"]> = (props) => {
  const { media, display_type, jump_url, pic, sidebar_labels } = props || {};
  const [isOpen, setIsOpen] = useState(false);

  const handleJump = useCallback((jump_url?: string) => {
    if (jump_url) window.open(jump_url, "_blank");
  }, []);

  useImagePreload(pic?.url);

  const isJumpType = display_type === SidebarType.DisplayType_DEFAULT;

  return (
    <div
      className="sidebar-item"
      onClick={() => handleJump(jump_url)}
      onPointerEnter={() => {
        if (isJumpType) return;
        setIsOpen(true);
      }}
      onPointerLeave={() => {
        if (isJumpType) return;
        setIsOpen(false);
      }}
    >
      <div className={clsx("icon-item", isOpen && "highlight-status")}>
        <Icon name={media!}></Icon>
      </div>

      {(pic?.url || !!sidebar_labels?.length) && (
        <CSSTransition
          in={isOpen}
          timeout={300}
          classNames="fade"
          unmountOnExit
          mountOnEnter
        >
          <div className="sub-container">
            <div className="sub-content">
              {pic?.url && (
                <div className="img-container">
                  <img src={pic?.url} alt="image" className="img" />
                  <span className="img-description">{pic?.description}</span>
                </div>
              )}
              {!!sidebar_labels?.length && (
                <div className="label-container">
                  {sidebar_labels.map((label) => (
                    <div
                      className="label-item"
                      key={label.content}
                      onClick={() => handleJump(label?.jump_url)}
                    >
                      <span>{label.content}</span>
                      {label?.jump_url && <Arrow />}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CSSTransition>
      )}
    </div>
  );
};
