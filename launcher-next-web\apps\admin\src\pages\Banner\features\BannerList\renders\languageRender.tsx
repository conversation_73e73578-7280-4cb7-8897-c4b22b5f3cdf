import React from "react";
import { Tag } from "antd";
import { Language } from "@/data";
import { useLangListMap } from "@/hooks/useLangList";

export const languageRender = (value: Language[]) => {
  const languageList = value || [];
  const languageMap = useLangListMap();

  return languageList.map((item) => {
    return (
      <Tag key={item} color="geekblue">
        {languageMap[item]}
      </Tag>
    );
  });
};
