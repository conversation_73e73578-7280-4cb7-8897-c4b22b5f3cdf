import {
  BannerConfig,
  BannerState,
} from "@/pages/Banner/hooks/useBanner/types";
import { Dropdown, Space, Typography } from "antd";
import { CancelPublish, Copy, Edit, Remove } from "./features";
import { Publish } from "@/pages/Banner/features/Publish";

export function actionRender(_: any, bannerConfig: BannerConfig) {
  const showPublishButton = [
    BannerState.Unpublished,
    // BannerState.AutoOffLine,
    // BannerState.ManualOffLine,
  ].includes(bannerConfig.state!);
  const showCancelPublishButton = [
    BannerState.Timing,
    BannerState.Published,
  ].includes(bannerConfig.state!);
  const showEditButton =
    [BannerState.AutoOffLine, BannerState.ManualOffLine].includes(
      bannerConfig.state!,
    ) === false;
  return (
    <Space align="center">
      {showEditButton && <Edit bannerConfig={bannerConfig} />}

      {showPublishButton && (
        <Publish
          bannersToPublish={[bannerConfig]}
          trigger={<Typography.Link type="success">发布</Typography.Link>}
        />
      )}
      {showCancelPublishButton && <CancelPublish bannerConfig={bannerConfig} />}
      <Dropdown
        menu={{
          items: [
            { key: "copy", label: <Copy bannerConfig={bannerConfig} /> },
            {
              key: "remove",
              label: <Remove bannerConfig={bannerConfig} />,
              danger: true,
            },
          ],
        }}
      >
        <Typography.Link style={{ whiteSpace: "nowrap" }}>...</Typography.Link>
      </Dropdown>
    </Space>
  );
}
