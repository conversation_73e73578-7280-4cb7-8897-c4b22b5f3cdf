import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { GetLauncherReportRequest } from "./types";

export function useSync() {
  const key = `${URL_PREFIX}/sync`;
  const { trigger, isMutating } = useSWRMutation(key, sync);
  return { trigger, isMutating };
}

async function sync(
  url: string,
  { arg }: { arg: GetLauncherReportRequest },
): Promise<any> {
  return fetcher(url, arg);
}
