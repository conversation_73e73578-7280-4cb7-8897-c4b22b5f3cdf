import { tsToDateTimeRender } from "@/features/renders";
import {
  SidebarConfig,
  SidebarState,
} from "@/pages/SocialMedia/hooks/useSidebar";
import { TableColumnsType } from "antd";
import {
  actionRender,
  stateRender,
  languageRender,
  sidebarIconRender,
  sidebarTypeRender,
} from "../renders";
import { useLangFiltersList } from "@/hooks/useLangList";
import { MediaConfig, Media } from "@/data/media";

export function useColumns() {
  const columns: TableColumnsType<SidebarConfig> = [
    { dataIndex: "图标", title: "图标", width: 80, render: sidebarIconRender },
    {
      dataIndex: ["data", "media"],
      title: "社媒",
      width: 120,
      ellipsis: true,
      render: (val) => MediaConfig[val as keyof typeof Media].label || "",
    },
    {
      dataIndex: "languages",
      title: "应用语种",
      render: languageRender,
      filters: useLangFiltersList(),
    },
    {
      dataIndex: ["data", "display_type"],
      title: "类型",
      render: sidebarType<PERSON><PERSON>,
    },
    {
      dataIndex: "state",
      title: "状态",
      render: stateRender,
      filters: [
        {
          text: "未发布",
          value: SidebarState.Unpublished,
        },
        {
          text: "已发布",
          value: SidebarState.Published,
        },
        {
          text: "下线",
          value: SidebarState.OffLine,
        },
      ],
    },
    { key: "action", title: "操作", render: actionRender },
  ];
  return columns;
}
