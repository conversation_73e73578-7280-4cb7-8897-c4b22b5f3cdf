import {
  ModalForm,
  ProFormGroup,
  ProFormList,
  ProFormText,
} from "@ant-design/pro-components";
import { But<PERSON> } from "antd";

export function TabSetting() {
  return (
    <ModalForm trigger={<Button>页签配置</Button>}>
      <ProFormList name="tabs" max={4}>
        <ProFormGroup>
          <ProFormText name="name" label="tab 名称" />
        </ProFormGroup>
      </ProFormList>
    </ModalForm>
  );
}
