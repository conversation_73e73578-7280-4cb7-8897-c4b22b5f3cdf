import { Flex, Modal, Select, Space, Tabs, message, Typography } from "antd";
import React, {
  useState,
  useCallback,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { cloneDeep } from "lodash";
import { Preview, Sort } from "./features";
import {
  type BannerConfig,
  AnnouncementConfig,
  AnnouncementConfigOrder,
  AnnouncementState,
  usePublish,
  usePublishedData,
} from "../../hooks/useAnnouncement";
import { useSetBatchSelect } from "../../hooks/useBatchSelect";
import { Language } from "@/data";
import { compareOrder } from "@/utils/compareOrder";
import { useAppCode, useLangList, useLangListMap } from "@/hooks";
import {
  useAnnounceTab,
  AnnouncementTabConfig,
} from "@/pages/AnnounceTab/hooks";

interface Props {
  trigger: JSX.Element;
  dataToPublish?: AnnouncementConfig[];
}

function getInitalLanguage(dataToPublish: AnnouncementConfig[]) {
  return (dataToPublish?.[0]?.language as Language) || Language.ZhCN;
}

export function Publish({ trigger, dataToPublish }: Props) {
  const [language, setLanguage] = useState<Language>(Language.ZhCN);
  const [activeTabList, setActiveTabList] = useState<AnnouncementTabConfig[]>(
    [],
  );
  const [activeTab, setActiveTab] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  // const [data, setData] = useState<Record<string, AnnouncementConfig[]>>({});
  const [allData, setAllData] = useState<Record<string, AnnouncementConfig[]>>(
    {},
  );

  const initRef = useRef(false);
  const curOrderRef = useRef<any>();

  const { tabs } = useAnnounceTab();
  const tabsLanguageMap = tabs?.reduce(
    (prev, cur) => {
      const tab_id = cur.id;
      prev[tab_id!] = cur.language || "";
      return prev;
    },
    {} as Record<string, string>,
  );

  const appcode = useAppCode();
  const { orders } = usePublishedData();
  const publish = usePublish();
  const langList = useLangList();
  const langTabsList = langList.map((item) => ({ ...item, key: item.value }));
  const langListMap = useLangListMap();
  const setBatchSelect = useSetBatchSelect();

  const getInitialAllData = useCallback(
    (data: AnnouncementConfigOrder[]) => {
      const currentData: Record<string, AnnouncementConfig[]> = {};
      tabs?.forEach((tab) => {
        const targetAnnouncements = data?.filter(
          (ele) => ele.tab_id === tab.id,
        )[0]?.announcement_configs;
        if (targetAnnouncements) {
          currentData[tab.id!] = targetAnnouncements;
        }
      });
      curOrderRef.current = cloneDeep(currentData);
      if (!dataToPublish) {
        return currentData;
      }
      // 过滤语言不匹配和状态不可发布的列
      dataToPublish
        ?.filter((item) => {
          return item?.state === AnnouncementState.Unpublished;
        })
        ?.forEach((ele) => {
          currentData[ele.tab_id!] = [ele, ...(currentData[ele.tab_id!] || [])];
        });
      return currentData;
    },
    [tabs, dataToPublish],
  );

  const updateTabs = (tabList: AnnouncementTabConfig[]) => {
    setActiveTab(tabList?.[0]?.id || "");
    setActiveTabList(tabList);
  };

  const handleLanguageChange = useCallback(
    (value: Language) => {
      setLanguage(value);

      const tabList = tabs?.filter((item) => item.language === value) || [];
      updateTabs(tabList);
    },
    [tabs],
  );

  useEffect(() => {
    if (initRef.current && tabs) {
      setAllData(getInitialAllData(orders || []));
      const tabList = tabs?.filter((item) => item.language === language) || [];
      updateTabs(tabList);
      initRef.current = false;
    }
  }, [initRef.current, orders, tabs, language]);

  return (
    <div>
      {React.cloneElement(trigger, {
        onClick: () => {
          const initalLanguage = getInitalLanguage(dataToPublish || []);
          setLanguage(initalLanguage);
          setIsOpen(true);
          initRef.current = true;
        },
      })}
      <Modal
        width={1200}
        open={isOpen}
        onCancel={() => setIsOpen(false)}
        onOk={async () => {
          try {
            const compareRes = compareOrder(curOrderRef.current, allData);
            const updateLangTextNode = compareRes.map((tag_id, index) => {
              const lang = tabsLanguageMap?.[tag_id];
              const langText = langListMap[lang as Language];
              return (
                <Typography.Link key={tag_id}>
                  {langText}
                  {index < compareRes.length - 1 ? "、" : ""}
                </Typography.Link>
              );
            });
            Modal.confirm({
              title: "重试补丁",
              content: updateLangTextNode.length ? (
                <Typography.Paragraph>
                  本次操作将涉及 {updateLangTextNode} 的内容变更，是否确认执行？
                </Typography.Paragraph>
              ) : (
                <span>本次操作无内容变更，是否确认执行？</span>
              ),
              onOk: async () => {
                const params = {
                  appcode: appcode,
                  orders: Object.keys(allData)?.map((tab_id) => ({
                    tab_id,
                    language: tabsLanguageMap?.[tab_id],
                    publish_ids:
                      dataToPublish
                        ?.filter((ele) => ele.tab_id === tab_id)
                        .map((ele) => ele.id!) || [],
                    order_ids: allData[tab_id]?.map((ele) => ele.id!),
                  })),
                };
                try {
                  await publish(params);
                  message.success("执行成功");
                  setBatchSelect([]);
                  setIsOpen(false);
                } catch (e: any) {
                  message.error(e.message || "请求错误");
                  return Promise.reject();
                }
              },
              onCancel() {},
            });
          } catch (error) {
            console.error(error);
            message.error("发布失败");
          }
        }}
        okText="发布"
        cancelText="取消"
        title={
          <Space>
            设置排序
            {/* <Select
              // disabled={!!dataToPublish?.length}
              style={{ width: 200 }}
              options={langList}
              value={language}
              onChange={handleLanguageChange}
            /> */}
          </Space>
        }
      >
        <Tabs
          items={langTabsList}
          activeKey={language}
          onChange={(val) => handleLanguageChange(val as Language)}
        ></Tabs>
        <Flex justify="space-around">
          <Tabs
            items={activeTabList?.map((tab) => ({
              key: tab.id!,
              label: tab.name!,
              children: (
                <Sort
                  announcementList={allData[tab.id!] || []}
                  setAnnouncementList={(value) =>
                    setAllData((prev) => {
                      const newData = cloneDeep(prev);
                      newData[tab.id!] = value;
                      return newData;
                    })
                  }
                  language={language}
                />
              ),
            }))}
            activeKey={activeTab}
            onChange={setActiveTab}
          ></Tabs>

          <Preview
            announcement={allData}
            activeKey={activeTab}
            language={language}
            onChange={setActiveTab}
          />
        </Flex>
      </Modal>
    </div>
  );
}
