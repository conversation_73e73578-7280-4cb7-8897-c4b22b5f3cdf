import { But<PERSON>, Space, Typography } from "antd";
import { HgLauncherBriefConfig } from "@/pages/LauncherManage/types";

export function actionRender(
  value: any,
  record: HgLauncherBriefConfig,
  callback: (record: HgLauncherBriefConfig, type: string) => void,
) {
  return (
    <Space>
      <Button type="link" onClick={() => callback(record, "publishManage")}>
        发布管理
      </Button>
      <Button type="link" onClick={() => callback(record, "edit")}>
        编辑
      </Button>
    </Space>
  );
}
