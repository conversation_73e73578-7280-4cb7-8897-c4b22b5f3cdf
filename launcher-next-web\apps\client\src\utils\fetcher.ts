// import { message } from "antd";

const HOST = __SERVER_URL
const PREFIX = ""

interface ErrResp {
    code: number
    msg: string
    reason: string
    message?: string
}

export async function fetcher<T>(
    path: string,
    data?: Record<string, any>,
    options?: RequestInit & {
        method?: "POST" | "GET"
        type?: "normal" | "permission"
        hideErrorMessage?: boolean
        skipAxiosErrorHandler?: boolean
        withoutToken?: boolean
    }
) {
    try {
        const isGet =
            options?.method &&
            ["GET", "HEAD"].includes(options.method.toUpperCase())
        if (isGet && data && Object.keys(data).length) {
            Object.entries(data).forEach(([key, val]) => {
                if (val === undefined) {
                    Reflect.deleteProperty(data, key)
                }
            })
            const queryString = new URLSearchParams(data).toString()
            path += `?${queryString}`
        }
        const response = await fetch(HOST + PREFIX + path, {
            method: options?.method || "POST",
            headers: {
                "content-type": "application/json",
            },
            body: isGet ? undefined : JSON.stringify(data),
        })

        if (!response.ok) {
            const res = await response.json().catch(() => "")
            throw res
        }

        const res = (await response.json()) as T | ErrResp
        if (res && typeof res === "object" && "code" in res && res.code !== 0) {
            throw res
        }
        return res as T
    } catch (error) {
        console.error("fetcher error", error)
        if (options?.skipAxiosErrorHandler || options?.hideErrorMessage) {
            throw error
        }
        throw error
    }
}
