import { DownOutlined, UpOutlined } from "@ant-design/icons";
import type { FormInstance } from "antd";
import { Button, Col, Divider, Flex, Form, Row, Space } from "antd";
import { forwardRef, useImperativeHandle, useEffect, useState } from "react";
import styles from "./style.module.less";
import { values } from "lodash";

export interface SearchFormItem {
  /** 表单字段名 */
  name: string;
  /** 表单标签 */
  label: string;
  /** 表单控件 */
  children: React.ReactNode;
  /** 初始值 */
  initialValue?: any;
  /** 栅格宽度 */
  span?: number;
  /** 值转换器，用于转换表单值为API参数 */
  converter?: (value: any) => Record<string, any>;
}

export interface SearchFormProps {
  /** 表单项配置 */
  items: (SearchFormItem | undefined)[];
  /** 初始值 */
  initialValues?: Record<string, any>;
  /** 搜索回调 */
  onSearch?: (values: Record<string, any>) => void;
}

export interface SearchFormRef {
  form: FormInstance;
  getSeacrhParams: () => Record<string, any>;
}

export const SearchForm = forwardRef<SearchFormRef, SearchFormProps>(
  (props, ref) => {
    const { items, initialValues, onSearch } = props;
    const [form] = Form.useForm();

    useEffect(() => {
      if (initialValues && Object.keys(initialValues).length > 0) {
        form.setFieldsValue(initialValues);
      }
    }, [initialValues]);

    useImperativeHandle(
      ref,
      () => ({
        form,
        getSeacrhParams: () => convertValues(form.getFieldsValue()),
      }),
      [form],
    );

    const convertValues = (values: Record<string, any>) => {
      const result = { ...values };
      items.forEach((item) => {
        if (!item || !item.converter || result[item.name] === undefined) {
          return;
        }

        const converted = item.converter(values[item.name]);
        Object.assign(result, converted);

        if (!Object.prototype.hasOwnProperty.call(converted, item.name)) {
          delete result[item.name];
        }
      });
      return result;
    };

    return (
      <div className="search-form">
        <Form
          form={form}
          layout="inline"
          // labelCol={{ span: 8 }}
          // wrapperCol={{ span: 16 }}
          onFinish={(values) => onSearch?.(convertValues(values))}
        >
          <Flex justify="space-between" gap={20} style={{ width: "100%" }}>
            <Row gutter={[24, 24]} style={{ width: "100%" }}>
              {items.map((item, index) => {
                if (!item) {
                  return null;
                }

                return (
                  <Col span={item.span || 8} key={index}>
                    <Form.Item
                      label={item.label}
                      name={item.name}
                      initialValue={item.initialValue}
                    >
                      {item.children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Flex wrap="nowrap">
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    查询
                  </Button>
                  <Button
                    htmlType="button"
                    onClick={() => {
                      form.resetFields();
                      setTimeout(() => {
                        onSearch?.({});
                      }, 0);
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Flex>
          </Flex>
        </Form>
      </div>
    );
  },
);

SearchForm.displayName = "SearchForm";
