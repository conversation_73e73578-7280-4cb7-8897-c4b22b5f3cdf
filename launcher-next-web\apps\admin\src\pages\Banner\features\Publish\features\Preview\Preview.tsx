import { Carousel } from "@/features";
import { BannerConfig, BannerState } from "@/pages/Banner/hooks";
import { Checkbox, Space, Typography } from "antd";
import { useState } from "react";

interface Props {
  banners: BannerConfig[];
}

export function Preview({ banners }: Props) {
  const [showTimingBanners, setTimingBanners] = useState(true);
  const showBanners = banners.filter((banner) =>
    showTimingBanners ? true : banner.state === BannerState.Published,
  );
  return (
    <Space direction="vertical">
      <Typography.Text>效果预览：</Typography.Text>
      <div
        style={{
          marginTop: 10,
          position: "relative",
          overflow: "hidden",
          width: 560,
          height: 316,
        }}
      >
        {showBanners?.length !== 0 && (
          <Carousel
            images={showBanners.map((banner) => banner.data?.url || "")}
          ></Carousel>
        )}
      </div>
      <div>
        <Checkbox
          checked={showTimingBanners}
          onChange={(e) => setTimingBanners(e.target.checked)}
        >
          显示定时中内容
        </Checkbox>
      </div>
    </Space>
  );
}
