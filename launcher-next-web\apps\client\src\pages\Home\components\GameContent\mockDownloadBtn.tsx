import { eventBus } from "@/utils/eventBus"
import {
    downloaded_game_list_rsp,
    downloaded_game_list_notify,
    downloaded_game_list_notify1,
    downloaded_game_list_notify2,
    downloaded_game_list_notify3,
    downloaded_game_list_notify4,
    downloaded_game_list_notify5,
} from "@/utils/mockData"

export const MockDownloadBtn = () => {
    const handleDownload2 = () => {
        console.log("handleDownload2")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_notify1
            )
        }, 1000)
    }

    const handleDownload = () => {
        console.log("handleDownload")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_notify
            )
        }, 1000)
    }

    const handleAddDownload = () => {
        console.log("handleAddDownload")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_notify2
            )
        }, 1000)
    }

    const handlePause = () => {
        console.log("handlePause")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_notify3
            )
        }, 1000)
    }

    const handleCheck = () => {
        console.log("handleCheck")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_notify4
            )
        }, 1000)
    }

    const handleDownloadDone = () => {
        console.log("handleDownloadDone")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_notify5
            )
        }, 1000)
    }

    const handleRemove = () => {
        console.log("handleRemove")
        setTimeout(() => {
            eventBus.emit(
                "downloaded_game_list_notify",
                downloaded_game_list_rsp
            )
        }, 1000)
    }

    const btnClassName = "rounded-xl p-2 text-white bg-black/50"

    return (
        <div className="flex align-center absolute top-[60px] right-[20px] ">
            <div>
                <button className={btnClassName} onClick={handleDownload2}>
                    rm42 增加进度
                </button>
                <button className={btnClassName} onClick={handleDownload}>
                    下载
                </button>
                <button className={btnClassName} onClick={handleAddDownload}>
                    增加进度
                </button>
                <button className={btnClassName} onClick={handlePause}>
                    暂停
                </button>
                <button className={btnClassName} onClick={handleCheck}>
                    校验中
                </button>
                <button className={btnClassName} onClick={handleDownloadDone}>
                    下载完成
                </button>
                <button className={btnClassName} onClick={handleRemove}>
                    移除
                </button>
            </div>
            {/* <div>
                <button>主按钮测试-去下载</button>
            </div> */}
        </div>
    )
}
