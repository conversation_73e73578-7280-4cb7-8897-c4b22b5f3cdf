{"name": "launcher-next", "version": "0.0.1", "description": "聚合启动器 b 端 c 端", "scripts": {"dev": "pnpm run --parallel dev", "build": "pnpm run --parallel build", "dev:admin": "pnpm -C apps/admin run dev", "build:admin": "pnpm -C apps/admin build", "dev:client": "pnpm -C apps/client dev", "build:client": "pnpm -C apps/client build", "publish:client": "pnpm run -C apps/client publish", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint ./apps --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint ./apps --ext .js,.jsx,.ts,.tsx --fix", "lint-staged": "lint-staged", "postinstall": "husky install"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@hg/eslint-config": "^1.0.1", "husky": "^8.0.3", "lint-staged": "^13.1.0", "prettier": "^3.0.3", "ts-node": "^10.9.1", "typescript": "^5.1.3"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write", "pnpm run lint:fix"]}, "packageManager": "pnpm@8.15.4"}