import React, { useEffect, useLayoutEffect, useRef, useState } from "react"
import clsx from "clsx"
import { motion } from "framer-motion"

interface Tab {
    id: string
    title: string
}

interface TabsProps {
    tabs: Tab[]
    value?: number
    fullRadius?: boolean
    onChange: (tabIndex: number) => void
}

export const Tabs: React.FC<TabsProps> = ({
    tabs,
    value,
    fullRadius,
    onChange,
}) => {
    const containerRef = useRef<HTMLDivElement>(null)
    const tabRefs = useRef<(HTMLSpanElement | null)[]>([])
    const [tabsStyle, setTabsStyle] = useState<{ x: number; width: number }>({
        x: 18,
        width: 0,
    })

    const firstRenderRef = useRef(true)
    const [hasAnimation, setHasAnimation] = useState(false)

    useEffect(() => {
        if (firstRenderRef.current) {
            setHasAnimation(true)
            firstRenderRef.current = false
            return
        }
    }, [])

    useLayoutEffect(() => {
        const containerEl = containerRef.current
        const activeTabEl = tabRefs.current[value || 0]
        if (!containerEl || !activeTabEl) return

        const containerRect = containerEl.getBoundingClientRect()
        const tabRect = activeTabEl.getBoundingClientRect()
        const x = tabRect.left - containerRect.left
        const width = tabRect.width

        setTabsStyle({
            x,
            width,
        })
    }, [value, tabs.length])

    return (
        <div
            className={clsx(
                "relative bg-[rgb(24,23,26)]/90 backdrop-blur-[4px]",
                fullRadius ? "rounded-t-[16px]" : "rounded-tr-[16px]"
            )}
        >
            <div
                ref={containerRef}
                className="flex px-[9px] py-[10px] overflow-hidden"
            >
                {tabs.map((tab, index) => (
                    <div
                        key={tab.id}
                        className={clsx(
                            "px-[12px] h-[20px]",
                            "font-bold text-[13px] font-sans cursor-pointer",
                            value === index
                                ? "text-white"
                                : "text-white/35 hover:text-white"
                        )}
                        style={{
                            transition: "color 0.3s ease",
                        }}
                        onClick={() => onChange(index)}
                    >
                        <span
                            ref={(ref) => (tabRefs.current[index] = ref)}
                            className="inline-block truncate max-w-[180px] leading-[20px]"
                        >
                            {tab.title}
                        </span>
                    </div>
                ))}
            </div>
            <div
                className={clsx(
                    "absolute bottom-[5px] h-[3px] bg-primary",
                    hasAnimation && "transition-all duration-300"
                )}
                style={{
                    transform: `translateX(${tabsStyle.x}px)`,
                    width: tabsStyle.width,
                    // transition: "width 0.3s ease 0.05s,transform 0.3s ease",
                }}
            />
        </div>
    )
}
