import { Flex, Space, Typography } from "antd";
import { HgLauncherBriefConfig } from "@/pages/LauncherManage/types";

const { Text, Link } = Typography;

export function overviewRender(value: any, record: HgLauncherBriefConfig) {
  const publish_overview = record.publish_overview;
  const { abnormal_num, in_task_num, wait_for_publish_num, online_num } =
    publish_overview || {};
  const config = [
    {
      value: abnormal_num,
      color: "#ff4d4f",
      label: "状态异常",
    },
    {
      value: in_task_num,
      color: "#1677ff",
      label: "进行中",
    },
    {
      value: wait_for_publish_num,
      color: "#faad14",
      label: "待发布",
    },
    {
      value: online_num,
      color: "#52c41a",
      label: "发布完成",
    },
  ];
  return (
    <Space>
      {config.map((item, index) => {
        return (
          item.value !== undefined && (
            <Flex gap={5} align="baseline" key={item.label}>
              <span style={{ color: item.color, fontSize: "18px" }}>
                {item.value}
              </span>
              <span style={{ color: "rgba(0,0,0,0.45)", fontSize: "12px" }}>
                {item.label}
              </span>
            </Flex>
          )
        );
      })}
    </Space>
  );
}
