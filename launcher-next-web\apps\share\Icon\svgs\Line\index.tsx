import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M24 13.737c0-4.022-4.05-7.313-9-7.313s-9 3.29-9 7.313c0 3.6 3.206 6.637 7.537 7.2.282.056.704.197.788.45.084.225.056.59.028.815 0 0-.112.647-.14.76-.029.225-.17.9.787.478.956-.394 5.175-3.066 7.06-5.231 1.321-1.407 1.94-2.87 1.94-4.472Zm-12.178 2.137a.182.182 0 0 1-.169.17H9.122c-.056 0-.085-.03-.113-.057a.213.213 0 0 1-.056-.113v-3.91c0-.083.085-.168.169-.168h.619c.084 0 .168.085.168.169v3.122h1.716c.084 0 .169.084.169.169l.028.618Zm1.519 0a.182.182 0 0 1-.17.17h-.618a.182.182 0 0 1-.169-.17v-3.91c0-.083.085-.168.17-.168h.618c.084 0 .169.085.169.169v3.91Zm4.359 0a.182.182 0 0 1-.169.17h-.675c-.028 0-.028-.03-.056-.057L15 13.568v2.334a.182.182 0 0 1-.169.17h-.618a.182.182 0 0 1-.17-.17v-3.937c0-.084.085-.169.17-.169h.618l.028.028 1.8 2.42v-2.335c0-.085.085-.17.17-.17h.702c.085 0 .169.085.169.17v3.965Zm3.488-3.29a.181.181 0 0 1-.17.168h-1.715v.675h1.716c.084 0 .169.085.169.17v.618a.182.182 0 0 1-.17.169h-1.715v.675h1.716c.084 0 .169.084.169.168v.62a.181.181 0 0 1-.17.168h-2.53c-.057 0-.085-.028-.113-.056a.213.213 0 0 1-.056-.113v-3.881c0-.056.028-.084.056-.113a.213.213 0 0 1 .113-.056h2.53c.085 0 .17.085.17.169v.619Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={32.151}
        x={-1.5}
        y={0.799}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50730"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50730"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
