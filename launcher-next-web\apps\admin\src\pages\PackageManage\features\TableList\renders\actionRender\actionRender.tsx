import { Space } from "antd";
import { Publish, UpdateNote } from "./features";
import { useHasAuthority } from "@/hooks";
import type { LauncherPackageConfig } from "@/pages/PackageManage/hooks/useLauncherPackageList/types";

export function actionRender(value: any, record: LauncherPackageConfig) {
  return <ActionRender value={value} record={record} />;
}

function ActionRender({
  value,
  record,
}: {
  value: any;
  record: LauncherPackageConfig;
}) {
  // const hasPublishAuthority = useHasAuthority(["game_version_publish"]);
  // const hasDeleteAuthority = useHasAuthority(["game_version_delete"]);
  // const hasDescriptionAuthority = useHasAuthority(["game_version_description"]);

  return (
    <Space align="center">
      <Publish config={record}></Publish>
      <UpdateNote config={record}></UpdateNote>
    </Space>
  );
}
