const fs = require("fs")
const path = require("path")
const archiver = require("archiver")

const ROOT_DIR = path.resolve(__dirname, "../../")
const BUILD_DIR = path.resolve(ROOT_DIR, "build")
const OUTPUT_DIR = path.resolve(ROOT_DIR, "dist")

function randomString(e = 32) {
    const t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    const a = t.length
    let n = ""
    for (let i = 0; i < e; i += 1) n += t.charAt(Math.floor(Math.random() * a))
    return n
}

async function zipBuild() {
    if (!fs.existsSync(BUILD_DIR)) {
        console.log("Build directory does not exist. Skipping zip build.")
        process.exit(1)
    }
    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR)
    } else {
        // 删除dist目录下的所有文件
        const files = fs.readdirSync(OUTPUT_DIR)
        for (const file of files) {
            fs.rmSync(path.join(OUTPUT_DIR, file), {
                recursive: true,
                force: true,
            })
        }
    }
    // 在OUTPUT_DIR下创建新文件夹，并读取version拼上随机字符串作为文件夹名称
    const version = require(path.resolve(ROOT_DIR, "package.json")).version
    const NEW_DIR = path.join(OUTPUT_DIR, `${version}_${randomString(8)}`)
    fs.mkdirSync(NEW_DIR)

    const zipName = `build.zip`
    const output = fs.createWriteStream(path.join(NEW_DIR, zipName))
    const archive = archiver("zip", {
        zlib: { level: 9 }, // 设置压缩级别
    })

    archive.on("error", (err) => {
        throw err
    })

    archive.pipe(output)
    // 把 build文件夹里的内容打包进压缩包里，false: 不保留 build文件夹本身，只保留它的内容
    archive.directory(BUILD_DIR, false, (entry) => {
        const relativePath = entry.name.replace(/\\/g, "/") // 兼容 Windows
        // 过滤qt_resources、.map、.DS_Store
        if (
            relativePath.startsWith("qt_resources/") ||
            relativePath === "qt_resources" ||
            relativePath.endsWith(".map") ||
            relativePath.endsWith(".DS_Store")
        ) {
            return false
        }
        return entry
    })

    await archive.finalize()
}

zipBuild().then(() => {
    console.log("Build zipped successfully.")
})
