import React, { useState, useRef, useLayoutEffect, useMemo } from "react"
import { createPortal } from "react-dom"
import clsx from "clsx"
import { motion, AnimatePresence } from "framer-motion"
import { Icon } from "@hg/launcher-share"
import bridge from "@/utils/bridge"
import { ArrowIcon } from "@/components/Icon"
import { SidebarData, SidebarType } from "@/types/mainData"
import { useImagePreload } from "@/hooks/useImagePreload"
import { EllipsisText } from "@/components/EllipsisText"

import "./index.scss"
import { channel } from "diagnostics_channel"

export const SidebarList = ({
    data: sidebarList,
    appcode,
}: {
    data?: SidebarData[]
    appcode?: string
}) => {
    if (!sidebarList?.length) return null
    const handleClick = (url?: string, extraData?: any) => {
        bridge.open(
            { url },
            "sidebar",
            { channel_name: extraData?.channel },
            "launcher_media_jump_event",
            {
                appcode,
                jump_url: url,
                channel: extraData?.channel,
            }
        )
    }
    return (
        <div
            className={clsx(
                "w-[56px] py-[12px]",
                "flex flex-col items-center gap-[10px]",
                "border-[1px] border-white/10 rounded-l-[16px]",
                "bg-black/30 backdrop-blur-[6px]"
            )}
            style={{ boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.65)" }}
        >
            {sidebarList?.map((item, index) => (
                <SidebarItem
                    key={item.media}
                    item={item}
                    onClick={handleClick}
                />
            ))}
        </div>
    )
}

const SidebarItem = React.memo(
    ({
        item,
        onClick,
    }: {
        item: SidebarData
        onClick?: (url?: string, extraData?: any) => void
    }) => {
        const { media, display_type, jump_url, pic, sidebar_labels } =
            item || {}
        const [isOpen, setIsOpen] = useState(false)
        const triggerRef = useRef<HTMLDivElement>(null)
        const popupRef = useRef<HTMLDivElement>(null)

        const handleJump = (jump_url?: string) => {
            if (jump_url) {
                onClick?.(jump_url, { channel: media })
            }
        }

        useImagePreload(pic?.url)

        const isJumpType = display_type === SidebarType.DisplayType_DEFAULT

        // 计算弹出框位置，超出底部则向上偏移
        useLayoutEffect(() => {
            if (!isOpen || !popupRef.current || !triggerRef.current) return
            const popup = popupRef.current
            const trigger = triggerRef.current
            const triggerRect = trigger.getBoundingClientRect()
            popup.style.top = `${triggerRect.top}px`
            popup.style.right = `${window.innerWidth - triggerRect.left}px`

            const mainContainer = document.querySelector("main")!
            const containerRect = mainContainer.getBoundingClientRect()
            const popupRect = popup.getBoundingClientRect()
            const overflow = popupRect.bottom - containerRect.bottom
            if (overflow > 0) {
                popup.style.transform = `translateY(-${overflow + 10}px)`
            }
        }, [isOpen])

        const popupContent = (pic?.url || !!sidebar_labels?.length) && (
            <AnimatePresence>
                {isOpen && (
                    <motion.div
                        ref={popupRef}
                        className={"pop-container"}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <div className="pop-content">
                            {pic?.url && (
                                <div
                                    className="img-container"
                                    style={{
                                        marginBottom: !!sidebar_labels?.length
                                            ? "0"
                                            : "10px",
                                        paddingBottom: pic?.description
                                            ? "0"
                                            : "12px",
                                    }}
                                >
                                    <img
                                        src={pic?.url}
                                        alt="image"
                                        className="img-item"
                                    />
                                    {pic?.description && (
                                        <span className="img-description">
                                            {pic?.description}
                                        </span>
                                    )}
                                </div>
                            )}
                            {!!sidebar_labels?.length && (
                                <div className="label-container">
                                    {sidebar_labels.map((label, index) => (
                                        <div
                                            key={label.content}
                                            className="label-item-container"
                                        >
                                            {/* {index !== 0 && (
                                                <div className="h-[1px] bg-white/10"></div>
                                            )} */}
                                            <div
                                                className="label-item"
                                                onClick={() =>
                                                    handleJump(label?.jump_url)
                                                }
                                            >
                                                <EllipsisText
                                                    text={label.content || ""}
                                                ></EllipsisText>
                                                {label?.jump_url && (
                                                    <ArrowIcon />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        )

        return (
            <div
                ref={triggerRef}
                className={clsx(
                    "relative w-[30px] h-[30px] px-[12px] box-content",
                    "cursor-pointer"
                )}
                onClick={() => handleJump(jump_url)}
                onPointerEnter={() => {
                    if (isJumpType) return
                    setIsOpen(true)
                }}
                onPointerLeave={() => {
                    if (isJumpType) return
                    setIsOpen(false)
                }}
            >
                <div
                    className={clsx(
                        "w-full h-full rounded-full text-white flex items-center justify-center transition-colors duration-300",
                        isOpen && "bg-white/[0.25]"
                    )}
                    style={{
                        boxShadow: isOpen
                            ? "0px 2px 6px 0px rgba(0, 0, 0, 0.55)"
                            : "",
                    }}
                >
                    <Icon name={media!}></Icon>
                </div>
                {isOpen && (
                    <div
                        style={{ width: "10px" }}
                        className={clsx("absolute", "h-10", "top-0 right-full")}
                    ></div>
                )}

                {/* 将弹出框渲染到body下，避免层叠上下文导致popContent的毛玻璃没效果 */}
                {createPortal(popupContent, document.body)}
            </div>
        )
    }
)
SidebarItem.displayName = "SidebarItem"
