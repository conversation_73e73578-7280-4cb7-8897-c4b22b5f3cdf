const addThemeClass = (appTheme: string) => {
    document.addEventListener("DOMContentLoaded", () => {
        const root = document.documentElement
        console.log(">>>addThemeClass", appTheme)
        const updateTheme = () => {
            root.classList.toggle(`theme-${appTheme}`)
        }
        updateTheme()

        // window.addEventListener('popstate', updateTheme); // 动态切换
    })
}
export default addThemeClass
