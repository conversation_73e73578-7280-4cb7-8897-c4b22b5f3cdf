import { EditForm } from "@/pages/Announcement/features";
import { Typography } from "antd";
import { ActionProps } from "../types";
import { AnnouncementState, useUpdate } from "@/pages/Announcement/hooks";

export function Edit({ announcement }: ActionProps) {
  const update = useUpdate();
  return (
    <EditForm
      title="编辑公告"
      trigger={<Typography.Link>编辑</Typography.Link>}
      initialValues={announcement}
      disableModify={announcement.state !== AnnouncementState.Unpublished}
      onFinish={async (value) => {
        update({ ...announcement, ...value });
      }}
    />
  );
}
