import { isHG } from "@/utils/env";

export enum CloudType {
  aliyun = "aliyun",
  tencent = "tencent",
  volcengine = "volcengine",
  wangsu = "wangsu",

  aws = "aws",
  akamai = "akamai",
}

const HgCloudTypeMap = {
  [CloudType.aliyun]: "阿里云",
  [CloudType.tencent]: "腾讯云",
  [CloudType.volcengine]: "火山引擎",
  // [CloudType.wangsu]: "网宿科技",
};

const GlCloudTypeMap = {
  [CloudType.aws]: "AWS",
  [CloudType.akamai]: "Akamai",
};

export const CloudTypeMap = isHG ? HgCloudTypeMap : GlCloudTypeMap;
