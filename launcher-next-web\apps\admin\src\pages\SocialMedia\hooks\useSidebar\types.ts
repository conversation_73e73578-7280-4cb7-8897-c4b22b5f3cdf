import { Language } from "@/data";

/**
 * sidebarListSidebarConfigsReq
 */
export interface ListRequest {
  appcode?: string;
  order_bys?: SidebarOrderby[];
  filter?: SidebarFilter;
  page_num?: number;
  page_size?: number;
  // search_keywords?: SidebarSearchKeyword[];
  states?: number[];
}

export interface SidebarFilter {
  states?: number[];
  languages?: string[];
}

export interface SidebarOrderby {
  field?: string;
  order?: string;
}

export interface SidebarSearchKeyword {
  field?: string;
  keyword?: string;
}

/**
 * ListSidebarConfigsRsp
 */
export interface ListResponse {
  Sidebar_configs?: SidebarConfig[];
  total_count?: number;
}

/**
 * SidebarConfig
 */
export interface SidebarConfig {
  id?: string;
  appcode?: string;
  languages?: string[];
  data?: SidebarData;
  description?: string;
  state?: SidebarState;
  mversion?: string;
  create_ts?: string;
  update_ts?: string;
}

/**
 * SidebarData
 */
export interface SidebarData {
  // type?: SidebarType; // 类型
  display_type?: SidebarType; // 展示类型
  media?: string; // 社媒
  jump_url?: string; // 跳转链接
  pic?: SideBarPic; // Sidebar图片&描述
  sidebar_labels?: SidebarLabel[]; // 标签列表
}

export interface SideBarPic {
  url?: string; // Sidebar图片链接
  md5?: string; // Sidebar图的md5值
  description?: string; // sidebar图片描述
}

export interface SidebarLabel {
  content?: string; // 标签名称
  jump_url?: string; // 标签跳转链接
}

// export enum SidebarType {
//   SidebarType_RESERVE = "SidebarType_RESERVE",
//   SidebarType_JUMP = "SidebarType_JUMP", // 普通跳转
//   SidebarType_PIC = "SidebarType_PIC", // 图片
// }

export enum SidebarType {
  DisplayType_RESERVE = "DisplayType_RESERVE",
  DisplayType_LIST = "DisplayType_LIST", // 列表展示
  DisplayType_GRID = "DisplayType_GRID", // 宫格展示
  DisplayType_DEFAULT = "DisplayType_DEFAULT", // 无交互反馈
}

export const SidebarTypeLabels: {
  [key in SidebarType]?: string;
} = {
  [SidebarType.DisplayType_DEFAULT]: "无交互反馈",
  [SidebarType.DisplayType_LIST]: "列表展示",
};

export enum SidebarState {
  Unpublished = 1,
  Published,
  OffLine,
}

export enum TimerKind {
  ShortTime = 1,
  LongTime,
}

export interface CreateSidebarRequest {
  appcode?: string;
  languages?: string[];
  data?: SidebarData;
  description?: string;
}

export interface UpdateSidebarRequest {
  id?: string;
  appcode?: string;
  languages?: string[];
  data?: SidebarData;
  description?: string;
}

export interface RemoveSidebarRequest {
  appcode?: string;
  id?: string;
}

export interface GetSidebarOrdersResponse {
  orders?: SidebarConfigOrder[];
}

/**
 * sidebarSidebarConfigOrder
 */
export interface SidebarConfigOrder {
  Sidebar_configs?: SidebarConfig[];
  language?: Language;
}

/**
 * sidebarPublishSidebarConfigReq
 */
export interface PublishRequest {
  appcode?: string;
  orders?: SidebarConfigIdOrder[];
}

/**
 * sidebarSidebarConfigIdOrder
 */
export interface SidebarConfigIdOrder {
  language?: string;
  order_ids?: string[];
  publish_ids?: string[];
}

/**
 * sidebarExpireSidebarConfigReq
 */
export interface ExpireSidebarRequest {
  appcode?: string;
  ids?: string[];
}
