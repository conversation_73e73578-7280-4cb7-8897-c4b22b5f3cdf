import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  Flex,
  Button,
  message,
  Modal,
  Form,
  Radio,
  Row,
  Col,
  Slider,
  InputNumber,
  InputNumberProps,
} from "antd";

import { PublishStage } from "@/data/publishStage";
import { getDateByTs } from "@/utils/time";
import {
  StageConfigMap,
  resultConfigMap,
  PublishStepConfig,
  PublishStep,
} from "@/features/PublishDetail/config";
import {
  PublishDetail,
  usePublishDetailConfig,
} from "@/features/PublishDetail";

import { AppEnvType, ReleaseState } from "@/pages/LauncherManage/types";
import { LauncherPackageConfig } from "@/pages/PackageManage/hooks/useLauncherPackageList";

import {
  useViewPublishReport,
  useSync,
  usePrewarm,
  usePublish,
  UpdateType,
  PublishType,
  UpdateTypeOptions,
  PublishTypeOptions,
} from "@/pages/PackageManage/hooks/usePublishPackage";

interface Props {
  config: LauncherPackageConfig;
  onCancel?: () => void;
}

export function PublishPackageDetail({ config, onCancel }: Props) {
  const [publish_stage, setPublish_stage] = useState<PublishStage>(
    PublishStage.PublishStage_RESERVE,
  );
  const refreshInterval = useRef(30000);
  const {
    appcode,
    version = "",
    channel,
    sub_channel,
    platform,
    register_time,
    launcher_app_env_type,
    launcher_release_state,
  } = config;

  const params = {
    appcode: appcode || "",
    channel,
    sub_channel,
    platform,
    version,
  };
  const { data: publishData, mutate } = useViewPublishReport(
    params,
    refreshInterval.current,
  );
  const {
    result = "warn",
    result_reason = "",
    update_type,
    publish_type,
    gray_ratio,
  } = publishData || {};

  const publishInitalValues = useMemo(() => {
    return {
      update_type: !!update_type ? update_type : undefined,
      publish_type:
        publish_type && publish_type !== PublishType.PublishType_RESERVE
          ? publish_type
          : undefined,
      gray_ratio,
    };
  }, [update_type, publish_type, gray_ratio]);

  useEffect(() => {
    const default_publish_stage = PublishStage.PublishStage_RESERVE;
    setPublish_stage(publishData?.publish_stage || default_publish_stage);
    // 流程结束时停止轮询
    if (
      publishData?.publish_stage === PublishStage.PublishStage_PUBLISH_FINISH
    ) {
      refreshInterval.current = 0;
    }
  }, [publishData?.publish_stage]);

  const isProdAndReleaseApp = useMemo(
    () =>
      launcher_app_env_type === AppEnvType.AppEnvType_PROD &&
      launcher_release_state === ReleaseState.ReleaseState_RELEASE,

    [launcher_app_env_type, launcher_release_state],
  );

  const stepList = useMemo(() => {
    return isProdAndReleaseApp
      ? [PublishStep.SYNC, PublishStep.PREWARM, PublishStep.PUBLISH]
      : [PublishStep.PUBLISH];
  }, [isProdAndReleaseApp]);

  const { stepConfig, buttonConfig } = usePublishDetailConfig(
    publish_stage,
    result,
    stepList,
    PublishStepConfig,
    StageConfigMap,
    resultConfigMap,
  );
  stepConfig.items.forEach((item) => {
    const PUBLISH_TEXT = PublishStepConfig[PublishStep.PUBLISH].title;
    const currentTitle = item?.title as string;
    if (
      currentTitle?.includes(PUBLISH_TEXT) &&
      gray_ratio !== undefined &&
      publish_stage === PublishStage.PublishStage_UNPUBLISH
    ) {
      item.description = `灰度比例：（${gray_ratio}%）`;
    }
  });
  console.log(">>>stepConfig", stepConfig);
  const resultConfig = useMemo(() => {
    const { color, icon, extra } = resultConfigMap?.[result] || {};
    return {
      color,
      icon,
      result_reason: result_reason,
      extra,
    };
  }, [result, result_reason]);

  const infoList = useMemo(() => {
    return [
      {
        label: "包体版本号",
        value: version,
      },
      {
        label: "注册时间",
        value: getDateByTs(register_time),
      },
    ];
  }, [version, register_time]);

  const { trigger: syncTrigger } = useSync();
  const { trigger: prewarmTrigger } = usePrewarm();
  const { trigger: publishTrigger } = usePublish();

  const handleSync = useCallback(async () => {
    const params = {
      appcode: appcode || "",
      channel,
      sub_channel,
      platform,
      version,
    };
    try {
      await syncTrigger(params);
      message.success("执行成功");
    } catch (error: any) {
      console.error(error);
      message.error(error.message || "执行失败");
    } finally {
      mutate();
    }
  }, []);

  const handlePrewarm = useCallback(async () => {
    const params = {
      appcode: appcode || "",
      channel,
      sub_channel,
      platform,
      version,
    };
    try {
      await prewarmTrigger(params);
      message.success("执行成功");
    } catch (error: any) {
      console.error(error);
      message.error(error.message || "执行失败");
    } finally {
      mutate();
    }
  }, []);

  const handleStartPublish = useCallback(async (values: any) => {
    const { update_type, publish_type, gray_ratio } = values;
    const params = {
      appcode: appcode || "",
      channel,
      sub_channel,
      platform,
      version,
      update_type,
      publish_type,
      gray_ratio,
    };
    try {
      await publishTrigger(params);
      setPublish_stage(PublishStage.PublishStage_PUBLISH_FINISH);
      onCancel?.();
      message.success("执行成功");
    } catch (e: any) {
      message.error(e.message || "请求错误");
      return Promise.reject();
    } finally {
      mutate();
    }
  }, []);

  const handlePublish = useCallback(async () => {
    let modal: { destroy: () => void } | null = null;
    const titleDescStyle = {
      marginLeft: "10px",
      fontWeight: 400,
      fontSize: "13px",
      color: "rgba(0, 0, 0, 0.45)",
    };
    const title = isProdAndReleaseApp ? (
      <>
        全量发布
        <span style={titleDescStyle}>全量发布后，该包体可被所有玩家下载</span>
      </>
    ) : (
      <>
        立即发布
        <span style={titleDescStyle}>
          发布后内网可升级下载，但因未同步&预热，外网用户无法访问。
        </span>
      </>
    );
    modal = Modal.confirm({
      title: title,
      content: (
        <Content
          initialValues={publishInitalValues}
          onFinish={handleStartPublish}
          onClose={() => modal?.destroy()}
        />
      ),
      onCancel() {},
      width: 660,
      style: { top: "300px" },
      // centered: true,
      footer: null,
    });
  }, [isProdAndReleaseApp, publishInitalValues, handleStartPublish]);

  const handleFinish = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  const eventHandlers = {
    handleSync,
    handlePrewarm,
    handlePublish,
    handleFinish,
  };

  const handleTrigger = async (triggerName: string) => {
    console.log(">>>handleTrigger", triggerName);
    const eventHandler =
      eventHandlers?.[triggerName as keyof typeof eventHandlers];
    if (eventHandler) {
      return eventHandler();
    }
    return () => {};
  };

  return (
    <div style={{ padding: "10px 30px" }}>
      <PublishDetail
        // type={"package"}
        infoList={infoList}
        stepConfig={stepConfig}
        resultConfig={resultConfig}
        buttonConfig={buttonConfig}
        onTrigger={handleTrigger}
        onExit={() => onCancel?.()}
      ></PublishDetail>
    </div>
  );
}

const Content = ({
  initialValues,
  onFinish,
  onClose,
}: {
  initialValues: Record<string, any>;
  onFinish: (val: any) => void;
  onClose: () => void;
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const handleFinish = async (values: any) => {
    setLoading(true);
    try {
      await onFinish(values);
      setLoading(false);
      onClose();
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      onFinish={handleFinish}
      initialValues={{
        update_type:
          initialValues?.update_type || UpdateType.nofocus_notip_update,
        publish_type:
          initialValues?.publish_type || PublishType.PublishType_Full,
        gray_ratio: initialValues?.gray_ratio || 0,
      }}
    >
      <Form.Item
        label="更新方式"
        name="update_type"
        rules={[{ required: true }]}
      >
        <Radio.Group
          options={UpdateTypeOptions}
          disabled={initialValues.update_type}
        ></Radio.Group>
      </Form.Item>
      <Form.Item
        label="发布方式"
        name="publish_type"
        rules={[{ required: true }]}
      >
        <Radio.Group
          options={PublishTypeOptions}
          disabled={initialValues.publish_type}
        ></Radio.Group>
      </Form.Item>
      <Form.Item noStyle dependencies={["publish_type"]}>
        {({ getFieldValue }) => {
          return getFieldValue("publish_type") ===
            PublishType.PublishType_Gray ? (
            <Form.Item
              name="gray_ratio"
              rules={[
                {
                  validator(_, value) {
                    if (
                      typeof initialValues?.gray_ratio === "number" &&
                      value < initialValues.gray_ratio
                    ) {
                      return Promise.reject(new Error("灰度发布比例不可后退"));
                    }

                    return Promise.resolve();
                  },
                },
              ]}
            >
              <IntegerStep></IntegerStep>
            </Form.Item>
          ) : null;
        }}
      </Form.Item>

      <Form.Item>
        <Flex justify="flex-end" gap={10}>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            确定
          </Button>
        </Flex>
      </Form.Item>
    </Form>
  );
};

const IntegerStep: React.FC<{ value?: any; onChange?: (val: any) => void }> = (
  props,
) => {
  const [inputValue, setInputValue] = useState(props.value || 0);

  const onChange: InputNumberProps["onChange"] = (newValue) => {
    setInputValue(newValue as number);
    props?.onChange?.(newValue);
  };

  return (
    <Row>
      <Col span={12}>
        <Slider
          min={0}
          max={100}
          onChange={onChange}
          value={typeof inputValue === "number" ? inputValue : 0}
        />
      </Col>
      <Col span={6}>
        <InputNumber
          min={0}
          max={100}
          precision={0}
          addonAfter="%"
          style={{ margin: "0 16px" }}
          value={inputValue}
          onChange={onChange}
        />
      </Col>
    </Row>
  );
};
