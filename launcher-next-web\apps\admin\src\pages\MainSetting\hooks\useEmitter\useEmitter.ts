import EventEmitter from "eventemitter3";
import { useEffect } from "react";
import { useMemoizedFn } from "ahooks";
import { Language } from "@/data";

enum Emitter {
  ChangeLang = "changeLang",
  CancelEdit = "cancelEdit",
  Save = "save",
}

export const mainSettingEventEmitter = new EventEmitter();

function useEmit<T>(emitter: Emitter) {
  return (data?: T) => {
    mainSettingEventEmitter.emit(emitter, data);
  };
}

function useOn<T>(emitter: Emitter, fn: (data: T) => void | Promise<void>) {
  const memoFn = useMemoizedFn(fn);
  useEffect(() => {
    mainSettingEventEmitter.on(emitter, memoFn);
  }, []);
}

/** cancel edit */
export function useEmitCancel() {
  return useEmit(Emitter.CancelEdit);
}

export function useOnCancel(fn: () => void) {
  useOn(Emitter.CancelEdit, fn);
}

/** change lang */
export function useEmitChangeLang() {
  return useEmit<Language>(Emitter.ChangeLang);
}

export function useOnChangeLang(fn: (data: Language) => void) {
  useOn(Emitter.ChangeLang, fn);
}

/** save */
export function useEmitSave() {
  return useEmit<Language>(Emitter.Save);
}

export function useOnSave(fn: () => void) {
  useOn(Emitter.Save, fn);
}
