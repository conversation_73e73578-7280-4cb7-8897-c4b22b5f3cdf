import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M17.533 6.75h-5.066c-4.623 0-5.717 1.095-5.717 5.717v5.066c0 4.622 1.095 5.717 5.717 5.717h5.066c4.622 0 5.717-1.095 5.717-5.717v-5.066c0-4.623-1.106-5.717-5.717-5.717Zm2.538 11.772h-1.199c-.454 0-.594-.36-1.41-1.187-.71-.688-1.024-.78-1.198-.78-.245 0-.315.07-.315.407v1.083c0 .291-.093.466-.862.466-1.269 0-2.678-.769-3.668-2.201-1.49-2.096-1.898-3.668-1.898-3.994 0-.175.07-.338.407-.338h1.2c.302 0 .419.14.536.466.593 1.712 1.583 3.214 1.99 3.214.152 0 .222-.07.222-.454v-1.77c-.047-.816-.478-.886-.478-1.176 0-.14.117-.28.303-.28h1.886c.257 0 .35.14.35.442v2.387c0 .256.116.35.186.35.15 0 .28-.094.559-.373.862-.966 1.478-2.457 1.478-2.457.082-.175.222-.338.525-.338h1.199c.36 0 .443.186.36.442-.15.7-1.617 2.772-1.617 2.772-.128.21-.175.302 0 .536.127.175.547.536.827.861.512.583.907 1.072 1.012 1.41.117.337-.058.511-.396.511l.001.001Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.5}
        height={31.5}
        x={-0.75}
        y={1.125}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50769"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50769"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
