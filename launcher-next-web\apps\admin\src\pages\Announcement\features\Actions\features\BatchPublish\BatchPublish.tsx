import { Button } from "antd";
import { Publish } from "@/pages/Announcement/features/Publish";
import { useBatchSelectValue, AnnouncementState } from "../../../../hooks";

export function BatchPublish() {
  const dataToPublish = useBatchSelectValue();
  const batchEnable = dataToPublish.some((item) => {
    return item.state === AnnouncementState.Unpublished;
  });

  return (
    <Publish
      dataToPublish={dataToPublish}
      trigger={<Button disabled={!batchEnable}>批量发布</Button>}
    />
  );
}
