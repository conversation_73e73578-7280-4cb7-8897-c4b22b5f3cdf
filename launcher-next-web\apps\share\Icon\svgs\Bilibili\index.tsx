import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M11.445 8.067c.372-.3.389-.174.515-.087.13.09 2.064 2.001 2.064 2.001h-.26c.743 0 1.51.008 2.246.008.285-.285 1.906-1.875 2.016-1.954.127-.08.151-.214.515.087.364.3.15.57.15.57l-1.353 1.305c1.859.016 3.291.023 3.291.023a2.54 2.54 0 0 1 1.883 2.508c-.024 2.104.008 6.336.008 6.336-.002.03-.117 2.059-1.86 2.38-.734-.022-.993 0-1.004 0-.001.02-.064.863-.79.879-.744.008-.855-.602-.879-.831-.449 0-5.826.023-5.869.023 0 0-.095.799-.83.799-.744 0-.784-.664-.83-.799-.477 0-1.114-.015-1.132-.015 0 0-1.63-.34-1.843-2.46.023-2.113 0-6.284 0-6.312 0 0-.15-1.954 1.795-2.516.602-.023 1.899-.03 3.402-.03l-1.385-1.346c-.005-.006-.209-.272.15-.569Zm-1.708 3.488a.592.592 0 0 0-.585.601v7.08c0 .332.261.6.585.6h10.68a.592.592 0 0 0 .584-.6v-7.08c0-.332-.26-.6-.585-.6H9.737Zm5.359 5.055c.701 1.523 1.477.414 1.485.41l.447.287s-.833 1.335-1.924.327c-.925 1.008-1.896-.319-1.896-.319l.493-.318s.66 1.19 1.395-.387ZM13.7 14.746l-2.98.576-.257-1.13 3.01-.576.227 1.13Zm6.117-.554-.25 1.13-2.987-.576.236-1.13 3.002.576Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={30.04}
        height={29.245}
        x={-0.02}
        y={2.253}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_52_981" />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_52_981"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
