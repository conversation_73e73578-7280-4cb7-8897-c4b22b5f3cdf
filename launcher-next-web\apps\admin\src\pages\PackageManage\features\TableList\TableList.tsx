import { ProTable } from "@ant-design/pro-components";
import { useColumns } from "./hooks";
import {
  usePage,
  usePageSize,
  useLauncherPackageList,
  LauncherPackageConfig,
} from "../../hooks";

export function TableList() {
  const [page, setPage] = usePage();
  const [pageSize, setPageSize] = usePageSize();
  const columns = useColumns();
  const { launchers, total, isLoading, mutate } = useLauncherPackageList();

  return (
    <ProTable<LauncherPackageConfig>
      rowKey={(record) => record.id || ""}
      search={false}
      columns={columns}
      dataSource={launchers}
      loading={isLoading}
      tableLayout={"auto"}
      options={{
        reload: () => {
          mutate();
        },
      }}
      pagination={{
        showSizeChanger: true,
        showTotal: () => "",
        current: page,
        pageSize: pageSize,
        total: Number(total),
        onChange(page, pageSize) {
          setPage(page);
          setPageSize(pageSize);
        },
      }}
    />
  );
}
