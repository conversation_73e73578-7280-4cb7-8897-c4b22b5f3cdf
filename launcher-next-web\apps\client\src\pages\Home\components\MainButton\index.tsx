import { useState, useMemo, useRef, useEffect } from "react"
import { motion, AnimatePresence, useAnimation } from "framer-motion"
import clsx from "clsx"
import { eventBus } from "@/utils/eventBus"
import { fetchQt, fetchQtCallback } from "@/utils/qwebchannel"
import { MainBtnState, Game, MainBtnStateResponse } from "@/types/games"
import { useOnMainBtnStateNotify } from "@/utils/eventBus"
import {
    DownloadBlackIcon,
    CircleIcon,
    QueueBlackIcon,
    ExceptBlackIcon,
    LoadingIcon,
    LoadingIconArrow,
    PauseBlackIcon,
    StartBlackIcon,
} from "@/components/Icon"
import { Dropdown, DropdownItem } from "@/components/Dropdown"
import { Tooltip } from "@/components/Tooltip"
import { useLocale } from "@/hooks/useLocale"
import "./index.scss"

import { MockMainBtnData } from "./MockMainBtnData"
import { main_btn_state_notify, main_btn_state_notify2 } from "@/utils/mockData"

const DownloadIconWithAnimate = ({
    needAnimating,
    onAnimationEnd,
}: {
    needAnimating?: boolean
    onAnimationEnd?: () => void
}) => {
    return (
        <div className="relative w-[26px] h-[26px] overflow-hidden">
            <div className="absolute inset-0">
                <CircleIcon />
            </div>
            <div
                className={clsx(needAnimating && "arrowAnimating")}
                onAnimationEnd={onAnimationEnd}
            >
                <DownloadBlackIcon />
            </div>
        </div>
    )
}

DownloadIconWithAnimate.displayName = "DownloadIconWithAnimate"

export const MainButton = ({
    game,
    btnWidth,
}: {
    game: Game | null
    btnWidth: number
}) => {
    const t = useLocale()
    const containerRef = useRef<HTMLDivElement>(null)
    const [notifyData, setNotifyData] = useState<any>()
    const notifyDataRef = useRef<MainBtnStateResponse>()
    const textRef = useRef<HTMLDivElement>(null)
    const [mainBtnState, setMainBtnState] = useState<MainBtnState>()

    const [btnText, setBtnText] = useState("")
    const [btnTextHover, setBtnTextHover] = useState("")
    const [btnTooltip, setBtnTooltip] = useState("")
    const [btnHover, setBtnHover] = useState(false)
    const [btnPressed, setBtnPressed] = useState(false)
    const [dropdownOpen, setDropdownOpen] = useState(false)

    const topCtrl = useAnimation()
    const bottomCtrl = useAnimation()
    const middleCtrl = useAnimation()
    const [isBarAnimating, setIsBarAnimating] = useState(false) // bar动画锁

    // const needArrowAnimatingState = [
    //     MainBtnState.ContinueDownload,
    //     MainBtnState.DownloadGame,
    //     MainBtnState.DownloadExcept,
    //     MainBtnState.DownloadPaused,
    //     MainBtnState.QueuingDownload,
    //     MainBtnState.UpdateGame,
    // ]

    const [isArrowAnimating, setIsArrowAnimating] = useState(false) // 下载箭头动画锁
    const [isMultiLine, setIsMultiLine] = useState(false) // 是否多行
    const displayText = useMemo(() => {
        const text = btnHover && btnTextHover ? btnTextHover : btnText

        return text
    }, [btnText, btnTextHover, btnHover])

    useEffect(() => {
        const el = textRef.current
        if (el) {
            // const height = parseInt(getComputedStyle(el).height)
            // const width = parseInt(getComputedStyle(el).width)
            // console.log("width", width)
            // setIsMultiLine(height > 40)
        }
    }, [displayText])

    useEffect(() => {
        const fetchMainBtnData = async () => {
            try {
                const params = {
                    uuid: game?.uuid,
                }
                const res: MainBtnStateResponse = await fetchQtCallback({
                    event: "main_btn_state_req",
                    data: params,
                    callbackEvent: "main_btn_state_rsp",
                    mockData: {
                        uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
                        state: MainBtnState.DownloadGame,
                        text_normal:
                            game?.game_state === "pause"
                                ? "暂停下载"
                                : "下载游戏",
                        // text_normal: "Preparando la instalaciónaran",
                        text_hover: "下载游戏",
                        tooltip: "",
                    },
                })
                console.log("main_btn_state_rsp", res)
                updateData(res)
            } catch (error) {
                console.error("fetchMainBtnData error", error)
            }
        }
        fetchMainBtnData()
    }, [game?.uuid])

    useOnMainBtnStateNotify((data) => {
        notifyDataRef.current = data
        if (data.uuid !== game?.uuid) return
        if (isArrowAnimating) {
            // 确保动画结束事件执行
            setTimeout(() => {
                handleAnimationEnd()
            }, 800)
            return
        }
        updateData(data)
    })
    const handleAnimationEnd = () => {
        setIsArrowAnimating(false)
        setBtnHover(false)
        if (notifyDataRef.current) {
            updateData(notifyDataRef.current)
        }
    }

    const DownloadIcon = (
        <DownloadIconWithAnimate
            needAnimating={isArrowAnimating}
            onAnimationEnd={handleAnimationEnd}
        />
    )

    const statusIconElement = useMemo(() => {
        let normal = null
        let hover = null
        let disabled = false

        switch (mainBtnState) {
            case MainBtnState.ContinueDownload:
                normal = DownloadIcon
                hover = DownloadIcon
                break
            case MainBtnState.DownloadGame:
                normal = DownloadIcon
                hover = DownloadIcon
                break
            case MainBtnState.Downloading:
                normal = <DownloadIconWithAnimate />
                hover = <PauseBlackIcon />
                break
            case MainBtnState.CheckingDownloadZip:
            case MainBtnState.CheckingGameComplete:
            case MainBtnState.Installing:
                normal = (
                    <div className="relative w-[26px] h-[26px]">
                        <div className="loading absolute inset-0 flex items-center justify-center">
                            <LoadingIcon />
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center">
                            <LoadingIconArrow />
                        </div>
                    </div>
                )
                break
            case MainBtnState.DownloadExcept:
                normal = <ExceptBlackIcon />
                hover = DownloadIcon
                break
            case MainBtnState.DownloadPaused:
                normal = <PauseBlackIcon />
                hover = DownloadIcon
                break
            case MainBtnState.QueuingDownload:
                normal = <QueueBlackIcon />
                hover = DownloadIcon
                break

            case MainBtnState.StartGame:
            case MainBtnState.NeedLogin:
            case MainBtnState.NeedPurchase:
                normal = <StartBlackIcon />
                break

            case MainBtnState.InGame:
                normal = <StartBlackIcon />
                disabled = true
                break

            case MainBtnState.UpdateGame:
                normal = <DownloadIconWithAnimate />
                hover = DownloadIcon
                break
            case MainBtnState.GameServiceStopped:
                normal = <ExceptBlackIcon />
                disabled = true
                break
            default:
                break
        }
        return { normal, hover, disabled }
    }, [mainBtnState, isArrowAnimating])

    const updateData = (data: MainBtnStateResponse) => {
        const { state, text_normal, text_hover, tooltip } = data
        setNotifyData(data)
        setMainBtnState(data.state)
        setBtnText(data.text_normal)
        setBtnTextHover(data.text_hover)
        setBtnTooltip(data.tooltip)
    }

    const handleClickBtn = () => {
        const needArrowAnimating =
            statusIconElement.hover?.type.displayName ===
            "DownloadIconWithAnimate"
        if (needArrowAnimating) {
            setIsArrowAnimating(true)
        }
        // setTimeout(() => {
        //     eventBus.emit("main_btn_state_notify", main_btn_state_notify)
        // }, 100)

        const data = {
            uuid: game?.uuid,
        }
        fetchQt({ event: "click_main_btn", data })
    }

    const handleClickBar = async (event: React.MouseEvent) => {
        if (isBarAnimating) return
        setIsBarAnimating(true)
        const yVal = 5
        if (!dropdownOpen) {
            await Promise.all([
                topCtrl.start({ y: yVal }),
                bottomCtrl.start({ y: -yVal }),
                middleCtrl.start({ opacity: 0 }),
            ])

            await Promise.all([
                topCtrl.start({ rotate: 45 }),
                bottomCtrl.start({ rotate: -45 }),
            ])
        } else {
            await Promise.all([
                topCtrl.start({ rotate: 0 }),
                bottomCtrl.start({ rotate: 0 }),
                middleCtrl.start({ opacity: 1 }),
            ])

            await Promise.all([
                topCtrl.start({ y: 0 }),
                bottomCtrl.start({ y: 0 }),
            ])
        }
        setIsBarAnimating(false)
    }

    const handleMouseLeave = async () => {
        if (isBarAnimating) return
        setIsBarAnimating(true)
        await Promise.all([
            topCtrl.start({ rotate: 0 }),
            bottomCtrl.start({ rotate: 0 }),
            middleCtrl.start({ opacity: 1 }),
        ])

        await Promise.all([topCtrl.start({ y: 0 }), bottomCtrl.start({ y: 0 })])
        setIsBarAnimating(false)
    }

    const handleGameSetting = () => {
        const data = {
            dialog_name: "game_setting",
            uuid: game?.uuid,
        }
        fetchQt({ event: "open_dialog", data })
        handleMouseLeave()
    }

    const handleCreateGameLink = () => {
        const data = {
            uuid: game?.uuid,
        }
        fetchQt({ event: "create_game_link", data })
        handleMouseLeave()
    }

    const handleCheckGameComplete = () => {
        const data = {
            uuid: game?.uuid,
        }
        fetchQt({ event: "check_game_complete", data })
        handleMouseLeave()
    }

    return (
        <div
            ref={containerRef}
            className={clsx(
                "main-btn w-full h-full flex justify-between items-center gap-2 rounded-[100px] relative"
            )}
            style={{
                backgroundColor: statusIconElement.disabled
                    ? btnHover
                        ? "rgb(var(--color-primary_disabled_hover))"
                        : "rgb(var(--color-primary_disabled))"
                    : btnPressed
                      ? "rgb(var(--color-primary_pressed))"
                      : btnHover
                        ? "rgb(var(--color-primary_hover))"
                        : "rgb(var(--color-primary))",
            }}
            // whileHover={{ backgroundColor: "rgb(var(--color-primary_hover))" }}
            // whileTap={{ backgroundColor: "rgb(var(--color-primary_pressed))" }}
        >
            {window.isDebug && <MockMainBtnData data={notifyData} />}
            {/* <MockMainBtnData data={notifyData} /> */}
            <div
                className={clsx(
                    "h-full min-w-0 flex items-center transition-colors duration-300",
                    "flex-1",
                    statusIconElement.disabled
                        ? "cursor-not-allowed"
                        : "cursor-pointer"
                )}
                style={{
                    willChange: "transform",
                }}
                onClick={handleClickBtn}
                onPointerEnter={() => setBtnHover(true)}
                onPointerLeave={() => {
                    if (isArrowAnimating) return
                    setBtnHover(false)
                    setBtnPressed(false)
                }}
                onPointerDown={(e) => {
                    setBtnPressed(true)
                }}
                onPointerUp={() => setBtnPressed(false)}
                onPointerCancel={() => setBtnPressed(false)}
            >
                {/* <div className="relative pl-[33px] pr-[9px] w-[26px] h-[26px]">
                    <div className={clsx("absolute inset-0 opacity-100", statusIconElement.hover && "group-hover:opacity-0 transition-opacity duration-200")}>
                        {statusIconElement.normal}
                    </div>
                    {statusIconElement.hover && (
                        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        {statusIconElement.hover}
                    </div>)} 
                */}
                <div className="pl-[33px] pr-[9px]">
                    {btnHover && statusIconElement.hover
                        ? statusIconElement.hover
                        : statusIconElement.normal}
                </div>
                <div
                    ref={textRef}
                    className={clsx(
                        "clamp-text-2 text-[#18171A] font-bold font-sans flex-1 text-ellipsis",
                        isMultiLine ? "text-[14px]" : "text-[16px]"
                    )}
                    // style={{
                    //     maxWidth: `${btnWidth - 56 - 68 - 8}px`,
                    // }}
                >
                    {displayText}
                </div>
            </div>
            <Tooltip
                triggerElement={containerRef.current}
                title={btnTooltip}
                show={!!btnTooltip && btnHover}
                placement="top-center"
                offset={12}
                // containerClassName="whitespace-nowrap"
            ></Tooltip>
            <Dropdown
                // className="absolute right-1 top-[50%] translate-y-[-50%] w-[52px] h-[52px]"
                contentClassName="w-[220px] translate-x-[-156px]"
                open={dropdownOpen}
                onOpenChange={(open) => {
                    if (!open) handleMouseLeave()
                    setDropdownOpen(open)
                }}
                trigger={
                    <div
                        className="relative mr-1 w-[52px] h-[52px] bg-[rgb(24,23,26)]/[0.78] rounded-[50%] flex items-center justify-center cursor-pointer"
                        style={{
                            willChange: "transform",
                        }}
                        onClick={handleClickBar}
                    >
                        <div className="absolute top-[50%] translate-y-[-50%] left-[50%] translate-x-[-50%] w-[18px]">
                            <motion.div
                                className="w-full h-[2px] bg-white mb-[3px]"
                                initial={{ y: 0, rotate: 0 }}
                                animate={topCtrl}
                                transition={{ duration: 0.1 }}
                                style={{
                                    willChange: "transform",
                                }}
                            />
                            <motion.div
                                className="w-full h-[2px] bg-white"
                                initial={{ opacity: 1 }}
                                animate={middleCtrl}
                                transition={{ duration: 0.1 }}
                                style={{
                                    willChange: "transform",
                                }}
                            />
                            <motion.div
                                className="w-full h-[2px] bg-white mt-[3px]"
                                initial={{ y: 0, rotate: 0 }}
                                animate={bottomCtrl}
                                transition={{ duration: 0.1 }}
                                style={{
                                    willChange: "transform",
                                }}
                            />
                            {/* <div className={clsx("absolute top-0 left-0 w-[18px] h-[2px] bg-white translate-y-[-5px] transform-all duration-200", barHover && "top-bar-animating")}></div>
                            <div className={clsx("absolute top-0 left-0 w-[18px] h-[2px] bg-white translate-y-[5px] transform-all duration-200", barHover && "bottom-bar-animating")}></div> */}
                        </div>
                    </div>
                }
                placement="top"
                offset={8}
            >
                <DropdownItem onClick={handleGameSetting}>
                    {t["mainBtn.gameSettings"]}
                </DropdownItem>
                <DropdownItem onClick={handleCreateGameLink}>
                    {t["mainBtn.adddShortcut"]}
                </DropdownItem>
                <DropdownItem
                    onClick={handleCheckGameComplete}
                    disabled={mainBtnState !== MainBtnState.StartGame}
                >
                    {t["mainBtn.checkGameComplete"]}
                </DropdownItem>
            </Dropdown>
        </div>
    )
}
