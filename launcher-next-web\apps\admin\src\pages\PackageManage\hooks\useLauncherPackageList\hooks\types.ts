export interface UpdateNoteRequest {
  appcode: string;
  channel?: string;
  sub_channel?: string;
  platform?: string;
  version?: string;
  release_notes?: Record<string, string>; // 多语言描述， key为语言，value为内容
  display_in_release_list?: boolean; // 日志列表同步展示开关
}

export interface GetNoteRequest {
  appcode: string;
  channel?: string;
  sub_channel?: string;
  platform?: string;
  version?: string;
}

export interface GetNoteReponse {
  release_notes: Record<string, string>; // 多语言描述， key为语言，value为内容
  display_in_release_list: boolean; // 日志列表同步展示开关
}
