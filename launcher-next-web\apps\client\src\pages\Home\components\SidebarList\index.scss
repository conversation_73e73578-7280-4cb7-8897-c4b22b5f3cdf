.pop-container {
    position: fixed;
    padding-right: 10px;
    
    .pop-content {
        display: flex;
        flex-direction: column;
        width: 11.25rem;
        padding: 0 10px;
        border-radius: 5px;
        background-color: rgba(24, 23, 26, 0.9);
        backdrop-filter: blur(6px);
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.65);
    }

    .img-container {
        margin: 10px 0;
        padding: 12px 12px 0 12px;  
        display: flex;
        flex-direction: column;
        gap: 1px;
        align-items: center;
        background-color: #ffffff;
        border-radius: 5px;
        .img-item {
            width: 136px;
            height: 136px;
        }
        .img-description {
            color: rgba(24, 23, 26, 0.78);
            font-size: 13px;
            font-family: Source Han Sans CN;
            line-height: 18px;    
            padding: 8px 0;       
        }
    }

    .label-container {
        margin: 8px 0;
        .label-item-container {
            margin-bottom: 4px;
            padding: 11px 8px;
            border-radius: 8px;
            &:hover {
                background-color: rgba(255, 255, 255, 0.10)
            }
            &:last-child {
                margin-bottom: 0;
            }      
        }
        .label-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // padding: 11px 0;
            color: #ffffff;
            font-family: "Source Han Sans CN";
            font-size: 13px;        
            line-height: 18px;
            cursor: pointer;
            transition: color 0.15s ease-in-out;
            // text-indent: 5px;
            
        }
    }
}
