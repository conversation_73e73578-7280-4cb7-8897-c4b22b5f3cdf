import { Table } from "antd";
import { AnnouncementTabConfig } from "../../hooks/useAnnounceTab/types";
import { useColumns, useOnTableChange } from "./hooks";
import { useAnnounceTab, usePage } from "../../hooks";

export function AnnounceTabList() {
  const [page, setPage] = usePage();
  const columns = useColumns();
  const { tabs, total } = useAnnounceTab();
  const onTableChange = useOnTableChange();
  return (
    <div>
      <Table<AnnouncementTabConfig>
        rowKey={"id"}
        columns={columns}
        dataSource={tabs}
        onChange={onTableChange}
        pagination={{
          current: page,
          total: Number(total),
          onChange(page) {
            setPage(page);
          },
        }}
      />
    </div>
  );
}
