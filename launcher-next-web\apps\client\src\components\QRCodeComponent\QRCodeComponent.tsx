import QRCode from "qrcode.react"
import { AnimatePresence, motion } from "framer-motion"

interface QRCodeComponentProps {
    value: string
    size?: number
    bgColor?: string
    fgColor?: string
    level?: "L" | "M" | "Q" | "H"
    className?: string
}

export const QRCodeComponent = ({
    value,
    size = 6,
    bgColor = "#ffffff",
    fgColor = "#000000",
    level = "M",
    className,
}: QRCodeComponentProps) => {
    const remSize =
        parseFloat(getComputedStyle(document.documentElement).fontSize) || 16
    const qrCodeSizeInPx = remSize * size // QRCode不支持传入rem，手动转换为 px
    return (
        <div style={{ textAlign: "center" }} className={className}>
            <QRCode
                value={value}
                size={qrCodeSizeInPx}
                bgColor={bgColor}
                fgColor={fgColor}
                level={level}
            />
        </div>
    )
}
