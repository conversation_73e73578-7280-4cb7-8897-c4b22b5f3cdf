import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M13.461 9.61c.866 0 2.564.655 1.376 2.73-.083.146 2.68-1.206 4.191-.317 1.51.889.004 2.316 0 2.478 0 .159 3.034.16 3.035 2.619 0 2.912-3.57 5.274-8.015 5.274S6 20.032 6 17.12c0-2.913 5.396-7.51 7.461-7.51Zm5.47 7.052c-.264-1.875-2.78-3.071-5.62-2.672-2.839.4-4.928 2.243-4.664 4.117.263 1.875 2.78 3.07 5.619 2.671 2.84-.4 4.928-2.242 4.665-4.116Zm-6.197-1.57c1.69-.329 3.284.564 3.562 1.994.278 1.43-.866 2.854-2.555 3.182-1.689.329-3.283-.564-3.561-1.994-.278-1.43.866-2.854 2.554-3.182Zm.47 2.603c-.186-.396-.757-.52-1.276-.278-.52.242-.791.76-.607 1.156.185.396.756.52 1.276.277.52-.242.79-.759.606-1.155Zm.584-1.102a.523.523 0 0 0-.518.528c0 .29.232.527.518.527.287 0 .52-.236.52-.527a.524.524 0 0 0-.52-.527Zm4.717-8.976s2.525-.231 4.245 1.42c1.717 1.65 1.166 4.607 1.164 4.618a.652.652 0 0 1-.637.56h-.24a.47.47 0 0 1-.482-.558c.006-.025.439-1.898-.586-3.393-1.032-1.504-3.456-1.247-3.456-1.247a.545.545 0 0 1-.57-.545v-.279c0-.313.252-.57.562-.576Zm.33 2.59c.02-.004 1.234-.305 2.164.546.929.849.544 2.39.536 2.422a.462.462 0 0 1-.444.373h-.341c-.205 0-.328-.16-.267-.358 0 0 .39-.976-.11-1.58-.5-.603-1.53-.463-1.53-.463a.358.358 0 0 1-.381-.358v-.148c0-.207.167-.402.373-.433Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={29.788}
        x={-1.5}
        y={1.981}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_52_979" />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_52_979"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
