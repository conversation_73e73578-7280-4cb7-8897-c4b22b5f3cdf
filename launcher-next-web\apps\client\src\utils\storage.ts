export const Storage = {
    setItem(key: string, value: any) {
        try {
            localStorage.setItem(key, JSON.stringify(value))
        } catch (error) {
            console.error(`localStorage set failed: ${key}`, error)
            if (error) {
                console.warn("localStorage is full")
                localStorage.clear()
                try {
                    localStorage.setItem(key, JSON.stringify(value))
                } catch (retryError) {
                    console.error(retryError)
                }
            }
        }
    },
    getItem(key: string, defaultValue?: any) {
        try {
            const value = localStorage.getItem(key)
            return value && value !== "undefined"
                ? JSON.parse(value)
                : defaultValue
        } catch (error) {
            console.error(`localStorage get failed: ${key}`, error)
            return defaultValue
        }
    },
    removeItem(key: string) {
        try {
            localStorage.removeItem(key)
        } catch (error) {
            console.error(`localStorage remove failed: ${key}`, error)
        }
    },
}
