import { Button, Space } from "antd";
import {
  MainPageState,
  useCurrentSourceValue,
  useEditState,
  useEmitCancel,
  useEmitSave,
  useMainData,
} from "../../hooks";
import { DataSource, EditState } from "../../types";
import { CancelPublishButton, PublishButton } from "./features";

export function Actions({ emitSave }: { emitSave: () => void }) {
  const currentSource = useCurrentSourceValue();
  const { currentConfig } = useMainData();
  const [editState, setEditState] = useEditState();
  const emitCancel = useEmitCancel();

  console.log(currentSource);
  if (currentSource === DataSource.Online) {
    return null;
  }

  const isEditing = editState === EditState.Editing;
  const isTiming = currentConfig?.state === MainPageState.Timing;
  return (
    <div>
      <Space>
        {/* <Button>预览</Button> */}
        {isEditing ? (
          <>
            <Button
              onClick={() => {
                emitCancel();
                switchEditState();
              }}
            >
              {"取消"}
            </Button>
            <Button
              onClick={() => {
                emitSave();
                console.log("emit");
                // switchEditState();
              }}
              type="primary"
              ghost
            >
              {"保存"}
            </Button>
          </>
        ) : (
          <>
            {!isTiming && <Button onClick={switchEditState}>{"编辑"}</Button>}
            {isTiming ? <CancelPublishButton /> : <PublishButton />}
          </>
        )}
      </Space>
    </div>
  );

  function switchEditState() {
    setEditState((prev) =>
      prev === EditState.Editing ? EditState.Readonly : EditState.Editing,
    );
  }
}
