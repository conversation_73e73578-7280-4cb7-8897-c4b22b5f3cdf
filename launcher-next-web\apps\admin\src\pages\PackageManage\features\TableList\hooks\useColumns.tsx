import type { ProColumns } from "@ant-design/pro-components";

import { ChannelNameEnum } from "@/data/channel";
import { tsToDateTimeRender } from "@/features/renders";
import { LauncherPackageConfig } from "../../../hooks";
import {
  versionRender,
  stateRender,
  packageUrlRender,
  actionRender,
} from "../renders";

export function useColumns() {
  const columns: ProColumns<LauncherPackageConfig>[] = [
    {
      dataIndex: "version",
      title: "包体版本号",
      render: versionRender,
    },
    {
      dataIndex: "channel",
      title: "渠道",
      renderText: (value) => {
        return ChannelNameEnum[value] || "";
      },
    },
    {
      dataIndex: "package_size_str",
      title: "包体大小",
      width: 100,
      ellipsis: true,
    },
    {
      dataIndex: "state",
      title: "发布状态",
      render: stateRender,
    },
    {
      dataIndex: "register_time",
      title: "注册时间",
      renderText: tsToDateTimeRender,
    },
    {
      dataIndex: "publish_time",
      title: "最新发布时间",
      renderText: tsToDateTimeRender,
    },
    {
      title: "下载链接",
      align: "center",
      // width: 250,
      render: packageUrlRender,
    },
    { key: "action", title: "操作", render: actionRender },
  ];
  return columns;
}
