import useS<PERSON> from "swr";
import { message } from "antd";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import {
  GetLauncherReportRequest,
  GetLauncherPublishReportReply,
} from "./types";

export function useViewPublishReport(
  args: GetLauncherReportRequest,
  refreshInterval?: number,
) {
  const key = `${URL_PREFIX}/publish_report`;
  const { data, isLoading, mutate } = useSWR([key, args], viewPublishReport, {
    refreshInterval: refreshInterval,
    onError(err, key, config) {
      message.error(err.message || "请求错误");
    },
  });
  return { data, isLoading, mutate };
}

async function viewPublishReport([url, arg]: [
  string,
  GetLauncherReportRequest,
]): Promise<GetLauncherPublishReportReply> {
  return fetcher(url, arg, { method: "GET" });
}
