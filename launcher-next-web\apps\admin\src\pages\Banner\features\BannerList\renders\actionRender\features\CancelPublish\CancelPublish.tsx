import { useAppCode } from "@/hooks";
import { BannerConfig, useCancelPublishBanner } from "@/pages/Banner/hooks";
import { Popconfirm, Typography } from "antd";

export function CancelPublish({
  bannerConfig,
}: {
  bannerConfig: BannerConfig;
}) {
  const appcode = useAppCode();
  const cancelPublish = useCancelPublishBanner();
  return (
    <Popconfirm
      title="是否确定要下线此 Banner ？"
      onConfirm={() =>
        cancelPublish({
          appcode,
          id: bannerConfig.id,
        })
      }
    >
      <Typography.Link type="warning">下线</Typography.Link>
    </Popconfirm>
  );
}
