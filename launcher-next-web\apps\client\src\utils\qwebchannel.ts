import { eventBus } from "./eventBus"
import { jsonStringify, jsonParse } from "./json"

interface Channel {
    objects: {
        HGJsBridge: {
            SendMsgToWeb: {
                connect: (fn: (msgType: string, msgBody: any) => void) => void
            } // 接收消息
            SendMsgToNative: (msgType: string, msgBody: any) => void // 发送消息给native
        }
        [key: string]: {
            [key: string]: any
        }
    }
}

interface FetchQtOptions {
    event: string
    data?: any
    // pageType: string
    callbackEvent?: string
    mockData?: any
}

function formatTime(date = new Date()) {
    const hh = String(date.getHours()).padStart(2, "0")
    const mm = String(date.getMinutes()).padStart(2, "0")
    const ss = String(date.getSeconds()).padStart(2, "0")
    const ms = String(date.getMilliseconds()).padStart(3, "0")
    return `${hh}:${mm}:${ss}:${ms}`
}

const isQtClient = typeof qt !== "undefined"

let qWebChannel: any

export const fetchQt = async ({ event, data }: FetchQtOptions) => {
    const channel = await QWebChannelPromise()
    return channel.fetchQt({ event, data })
}

export const fetchQtCallback = async ({
    event,
    data,
    callbackEvent,
    mockData,
}: FetchQtOptions) => {
    const channel = await QWebChannelPromise()
    return channel.fetchQtCallback({ event, data, callbackEvent, mockData })
}

export default function QWebChannelPromise() {
    return new Promise<any>((resolve, reject) => {
        if (!isQtClient) {
            qWebChannel = {}
            qWebChannel._api = {
                fetchQt: ({ event, data }: FetchQtOptions) => {
                    const msgBody = {
                        data,
                    }
                    console.log("fetchQt", event, msgBody)
                },

                fetchQtCallback: ({
                    event,
                    data,
                    callbackEvent,
                    mockData,
                }: FetchQtOptions) => {
                    return new Promise((resolve, reject) => {
                        const msgBody = mockData
                        console.log(
                            "fetchQtCallback",
                            event,
                            callbackEvent,
                            msgBody,
                            formatTime(new Date())
                        )
                        setTimeout(() => {
                            resolve(mockData)
                        }, 100)
                    })
                },
            }
            resolve(qWebChannel._api)
        } else {
            if (!qWebChannel) {
                qWebChannel = new QWebChannel(
                    qt.webChannelTransport,
                    (channel: Channel) => {
                        const jsbridge = channel.objects.HGJsBridge
                        window.jsbridge = jsbridge

                        jsbridge.SendMsgToWeb.connect(
                            (msgType: string, msgBody: any) => {
                                jsbridge.SendMsgToNative("log", msgBody)
                                console.log(
                                    "===接收Qt信息类型===",
                                    msgType,
                                    formatTime(new Date())
                                )
                                console.log("===接收Qt信息内容===", msgBody)
                                const parseBody = jsonParse(msgBody)
                                eventBus.emit(msgType, parseBody)
                            }
                        )
                        //发送初始化消息给native
                        jsbridge.SendMsgToNative("init", "")
                        qWebChannel._api = {
                            fetchQt: ({ event, data }: FetchQtOptions) => {
                                console.log("===fetchQt===", event, data)
                                // const msgBody = {
                                //     data: jsonStringify(data),
                                // }
                                jsbridge.SendMsgToNative(
                                    event,
                                    jsonStringify(data)
                                )
                            },
                            fetchQtCallback: ({
                                event,
                                data,
                                // pageType,
                                callbackEvent = "",
                            }: FetchQtOptions) => {
                                return new Promise((resolve, reject) => {
                                    console.log(
                                        "===fetchQtCallback start===",
                                        event,
                                        data
                                    )
                                    let timer: NodeJS.Timeout | null = null
                                    const callback = (msgBody: any) => {
                                        console.log(
                                            "===fetchQtCallback callback===",
                                            event,
                                            msgBody
                                        )
                                        clearTimeout(timer!)
                                        timer = null
                                        resolve(msgBody)
                                    }
                                    // 设置10s超时
                                    timer = setTimeout(() => {
                                        eventBus.off(callbackEvent, callback)
                                        reject(new Error("timeout"))
                                    }, 10000)
                                    eventBus.once(callbackEvent, callback)
                                    const msgBody = {
                                        data: data,
                                        callbackEvent,
                                    }
                                    jsbridge.SendMsgToNative(
                                        event,
                                        jsonStringify(msgBody)
                                    )
                                })
                            },
                            jsbridge,
                        }
                        resolve(qWebChannel._api)
                    }
                )
            } else {
                resolve(qWebChannel._api)
            }
        }
    })
}
