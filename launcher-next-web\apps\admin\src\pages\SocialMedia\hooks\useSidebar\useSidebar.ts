import useS<PERSON> from "swr";
import { fetcher } from "@/utils/fetcher";
import { SidebarFilter, ListResponse } from "./types";
import { useSidebarKey } from "./useSidebarKey";
import { message } from "antd";

export function useSidebar() {
  const key = useSidebarKey();
  const { data, isLoading } = useSWR(
    key,
    ([url, appCode, { page, sort, filter }]) =>
      fetcher<ListResponse>(url, {
        appcode: appCode,
        page_num: page,
        page_size: 10,
        // search_keywords: getSearchKeywords({ searchVal }),
        order_bys: sort,
        filter: getFilter(filter),
      }),
    {
      onError(err, key, config) {
        message.error(err?.message || "请求错误");
      },
    },
  );
  const sidebars = data?.Sidebar_configs;
  const total = data?.total_count;

  return {
    sidebars,
    isLoading,
    total,
  };
}

// function getSearchKeywords({ searchVal }: { searchVal: string }) {
//   const keywords = [];
//   if (searchVal) {
//     keywords.push({ field: "description", keyword: searchVal });
//   }
//   return keywords;
// }

function getFilter(filter?: {
  tab_id?: string[];
  state?: number[];
  languages?: string[];
}) {
  const filters: SidebarFilter = {};
  if (filter?.state) {
    filters.states = filter.state;
  }
  if (filter?.languages) {
    filters.languages = filter.languages;
  }
  return filters;
}
