import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
export function getUtcTs(values: { time: string; utc?: number }) {
  const defaultUTC = 8;
  if (!values.time) return;
  const startTs = dayjs(values.time)
    .utcOffset(values.utc || defaultUTC, true)
    .unix();
  return startTs * 1000;
}

export function getDateByTs(ts: string | number | undefined) {
  if (ts === undefined) {
    return "";
  }
  if (+ts <= 0) {
    return "-";
  }
  return dayjs(+ts).format("YYYY-MM-DD HH:mm:ss");
}

export function getDateByTsForTimePick(ts: string | number | undefined) {
  if (ts === undefined || +ts <= 0) {
    return undefined;
  }

  return getDateByTs(ts);
}

export function isCurrentBefore(ts: string | number | undefined) {
  if (!ts) {
    return false;
  }
  return dayjs().isBefore(dayjs(+ts));
}

export function isCurrentAfter(ts: string | number | undefined) {
  if (!ts) {
    return false;
  }
  return dayjs().isAfter(dayjs(+ts));
}
