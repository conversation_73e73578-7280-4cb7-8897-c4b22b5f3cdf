import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnounceTabKey } from "../useAnnounceTabKey";
import { UpdateBannerRequest } from "../types";

export function useUpdateTab() {
  const key = useAnnounceTabKey();
  const { trigger } = useSWRMutation(key, update);
  return trigger;
}

async function update(_: any, { arg }: { arg: UpdateBannerRequest }) {
  await fetcher(`${URL_PREFIX}/update`, arg);
}
