const fs = require("fs")
const path = require("path")

const ROOT_DIR = path.resolve(__dirname, "../../")
const BUILD_DIR = path.resolve(ROOT_DIR, "build")
const OUTPUT_DIR = path.resolve(ROOT_DIR, "dist")

// 获取 build 文件夹下的所有文件和嵌套文件,输出相对路径
function getBuildFiles(dir) {
    const result = []
    const files = fs.readdirSync(dir, { withFileTypes: true })
    for (const file of files) {
        if (
            file.name === ".DS_Store" ||
            file.name === "qt_resources" ||
            file.name.endsWith(".map")
        )
            continue
        const filePath = path.resolve(dir, file.name)
        const stat = fs.statSync(filePath)
        if (stat.isDirectory()) {
            result.push(...getBuildFiles(filePath))
        } else {
            result.push(path.relative(BUILD_DIR, filePath))
        }
    }
    return result
}

function generateVersionFile() {
    if (!fs.existsSync(BUILD_DIR)) {
        console.error(`[Error] "${BUILD_DIR}" does not exist.`)
        process.exit(1)
    }
    const files = getBuildFiles(BUILD_DIR)
    // console.log("files", files)
    const version = require(path.resolve(ROOT_DIR, "package.json")).version
    const versionData = {
        version,
        files,
    }
    const versionJson = JSON.stringify(versionData, null, 2)

    // if (!fs.existsSync(OUTPUT_DIR)) {
    //     fs.mkdirSync(OUTPUT_DIR)
    // }
    // const outputPath = path.resolve(OUTPUT_DIR, "version.json")
    // fs.writeFileSync(outputPath, versionJson)
    const buildPath = path.resolve(BUILD_DIR, "version.json")

    fs.writeFileSync(buildPath, versionJson)

    console.log("Generated version.json successfully. version:", version)
}

generateVersionFile()
