import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useAnnouncementKey } from "../../useAnnouncementKey";
import { ExpireBannerRequest, PublishRequest } from "../../types";
import { useSWRConfig } from "swr";
import { useAppCode } from "@/hooks";

export function useCancelPublish() {
  const key = useAnnouncementKey();
  const appCode = useAppCode();
  const { mutate } = useSWRConfig();
  const { trigger } = useSWRMutation(key, cancelPublish, {
    onSuccess() {
      mutate([URL_PREFIX + "/get_orders", appCode]);
    },
  });
  return trigger;
}

async function cancelPublish(_: any, { arg }: { arg: ExpireBannerRequest }) {
  await fetcher(`${URL_PREFIX}/expire`, arg);
}
