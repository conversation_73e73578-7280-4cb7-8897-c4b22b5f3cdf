import useS<PERSON> from "swr";
import { fetcher } from "@/utils/fetcher";
import { BannerFilter, ListResponse } from "./types";
import { useBanner<PERSON>ey } from "./hooks";
import { message } from "antd";

export function useBanner() {
  const key = useBannerKey();
  const { data, isLoading } = useSWR(
    key,
    ([url, appCode, { page, searchVal, sort, filter }]) =>
      fetcher<ListResponse>(url, {
        appcode: appCode,
        page_num: page,
        page_size: 10,
        search_keywords: getSearchKeywords({ searchVal }),
        order_bys: sort,
        filter: getFilter(filter),
      }),
    {
      onError(err, key, config) {
        message.error(err.message || "请求错误");
      },
    },
  );
  const banners = data?.banner_configs;
  const total = data?.total_count;

  return {
    banners,
    isLoading,
    total,
  };
}

function getSearchKeywords({ searchVal }: { searchVal: string }) {
  const keywords = [];
  if (searchVal) {
    keywords.push({ field: "description", keyword: searchVal });
  }
  return keywords;
}

function getFilter(filter?: {
  tab_id?: string[];
  state?: number[];
  languages?: string[];
}) {
  const filters: BannerFilter = {};
  if (filter?.state) {
    filters.states = filter.state;
  }
  if (filter?.languages) {
    filters.languages = filter.languages;
  }
  return filters;
}
