const zhcn = {
    allGames: "全部游戏",
    "header.login": "登录",
    "header.logout": "退出登录",
    "header.settings": "设置",
    "header.minimize": "最小化",
    "header.close": "关闭",
    "mainBtn.gameSettings": "游戏设置",
    "mainBtn.checkGameComplete": "游戏完整性检查",
    "mainBtn.adddShortcut": "添加桌面快捷方式",
}
const enus = {
    allGames: "All Games",
    "header.login": "Login",
    "header.settings": "Settings",
    "header.minimize": "Minimize",
    "header.close": "Close",
    "mainBtn.gameSettings": "Game settings",
    "mainBtn.checkGameComplete": "Check game integrity",
    "mainBtn.adddShortcut": "Add desktop shortcut",
}
const zhTW = {
    allGames: "全部遊戲",
    "header.login": "登錄",
    "header.settings": "設置",
    "header.minimize": "最小化",
    "header.close": "關閉",
    "mainBtn.gameSettings": "遊戲設置",
    "mainBtn.checkGameComplete": "遊戲完整性檢查",
    "mainBtn.adddShortcut": "添加桌面快捷方式",
}
const jaJP = {
    allGames: "全て表示",
    "header.login": "ログイン",
    "header.settings": "設定",
    "header.minimize": "最小化",
    "header.close": "閉じる",
    "mainBtn.gameSettings": "ゲーム設定",
    "mainBtn.checkGameComplete": "ゲームの整合性確認",
    "mainBtn.adddShortcut": "ショートカット作成",
}
const koKR = {
    allGames: "모든 게임",
    "header.login": "로그인",
    "header.settings": "설정",
    "header.minimize": "최소화",
    "header.close": "닫기",
    "mainBtn.gameSettings": "게임 설정",
    "mainBtn.checkGameComplete": "게임 무결성 검사",
    "mainBtn.adddShortcut": "바탕화면 바로가기 추가",
}

const i18n: Record<string, Record<string, string>> = {
    "zh-cn": zhcn,
    "en-us": enus,
    "zh-tw": zhTW,
    "ja-jp": jaJP,
    "ko-kr": koKR,
}

export default i18n
