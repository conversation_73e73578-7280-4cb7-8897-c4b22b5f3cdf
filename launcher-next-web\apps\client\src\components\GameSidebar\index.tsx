import React, { useEffect, useState } from "react"
import clsx from "clsx"
import { useNavigate, useLocation } from "react-router-dom"
import { motion, AnimatePresence } from "framer-motion"

import logo from "@/assets/logo.png"
import { GameListResponse, Game, SwitchGameResponse } from "@/types/games"
import { fetchQtCallback } from "@/utils/qwebchannel"
import { useSelectedGame } from "@/store/useSelectedGame"
import { useAllGameInfo } from "@/store/useAllGameInfo"
import { useUrlParams } from "@/hooks/useUrlParams"
import {
    useOnGameListChange,
    useOnSwitchGameNotify,
    useOnAllGameListChange,
} from "@/utils/eventBus"

import { AllGamesIconItem } from "./components/AllGamesIconItem"
import { GameIconItem } from "./components/GameIconItem"

import { downloaded_game_list_rsp } from "@/utils/mockData"
import { all_game_list_rsp, logout_all_game_list_rsp } from "@/utils/mockData"

import { eventBus } from "@/utils/eventBus"

export const GameSidebar = () => {
    const navigate = useNavigate()
    const location = useLocation()
    const urlParams = useUrlParams()

    const [gameList, setGameList] = useState<Game[]>([])
    const [selectedGame, setSelectedGame] = useSelectedGame()
    const [allGameInfo, setAllGameInfo] = useAllGameInfo()
    const [newGameIds, setNewGameIds] = useState<string[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [isSelecting, setIsSelecting] = useState(false)
    const [isAllGames, setIsAllGames] = useState(
        location.pathname.includes("/all_games")
    )
    const [isSingleGame, setIsSingleGame] = useState(
        urlParams.get("is_single_game") === "true"
    )

    useEffect(() => {
        const isAllGamePage = location.pathname.includes("/all_games")
        setIsAllGames(isAllGamePage)
    }, [location.pathname])

    // 监听已下载游戏列表变化消息
    useOnGameListChange((data) => {
        console.log("useOnGameListChange", data)
        setNewGameIds([])
        const newGameList = data?.items || []
        // 检测新添加的游戏
        const addGameList = newGameList.filter(
            (item) => !gameList.some((game) => game.uuid === item.uuid)
        )
        if (addGameList.length > 0) {
            console.log(">>>new game add", addGameList)
            setNewGameIds(addGameList.map((item) => item.uuid))
        }
        setGameList(newGameList)
        // 更新当前选中游戏信息
        setSelectedGame((prev) => {
            if (!prev) return prev
            const newSelectedGame = newGameList.find(
                (item) => item.uuid === prev.uuid
            )
            // 如果游戏移除，清空进度条状态
            if (!newSelectedGame)
                return {
                    ...prev,
                    download_state: undefined,
                }
            return newSelectedGame
        })
    })

    // 监听全部游戏列表变化消息
    useOnAllGameListChange((data) => {
        console.log("useOnAllGameListChange", data)
        handleAllGamesListChange(data)
    })

    // 监听游戏切换消息 (作用于打开启动器后又从桌面打开其它游戏 & 全部游戏跳转游戏主页)
    useOnSwitchGameNotify((data) => {
        console.log("useOnSwitchGameNotify", data)
        const { from, to } = data
        if (to && to !== selectedGame?.uuid) {
            handleSelectGame(to)
        }
    })

    const handleJumpAllGames = async () => {
        if (isSelecting) return
        if (isAllGames) return
        setIsSelecting(true)
        try {
            const data = {
                from: selectedGame?.uuid,
                to: allGameInfo.uuid,
            }
            const res: SwitchGameResponse = await fetchQtCallback({
                event: "switch_game_req",
                data,
                callbackEvent: "switch_game_rsp",
                mockData: {
                    result: true,
                    // cur_game: {
                    //     show_404_page: false,
                    // },
                },
            })
            if (res?.result) {
                setSelectedGame(null)
                if (res?.cur_game?.show_404_page) {
                    navigate("/all_games/404")
                } else {
                    navigate("/all_games")
                }
            }
            setIsSelecting(false)
        } catch (error) {
            setIsSelecting(false)
        }
    }

    const handleSelectGame = async (uuid: string) => {
        console.log("handleSelectGame", uuid)
        if (isSelecting) return
        if (selectedGame?.uuid === uuid) return
        setIsSelecting(true)

        try {
            const data = {
                from: selectedGame?.uuid,
                to: uuid,
            }
            const res: SwitchGameResponse = await fetchQtCallback({
                event: "switch_game_req",
                data,
                callbackEvent: "switch_game_rsp",
                mockData: {
                    result: true,
                    cur_game: {
                        ...all_game_list_rsp.items.find(
                            (item) => item.uuid === uuid
                        ),
                        show_404_page: false,
                    },
                },
            })
            console.log("switch_game_rsp", res)
            if (res?.result) {
                let selectedGame = gameList.find((item) => item.uuid === uuid)
                if (res?.cur_game) {
                    selectedGame = {
                        ...selectedGame,
                        ...res.cur_game,
                    }
                }

                if (selectedGame) {
                    setSelectedGame(selectedGame)
                    // setIsAllGames(false)
                }
                // 如果当前不在主页则导航到主页(在全部游戏页点击左侧游戏图标)
                if (location.pathname !== "/") {
                    navigate("/")
                }
                if (selectedGame?.show_404_page) {
                    navigate("/404")
                }
            }
            setIsSelecting(false)
        } catch (error) {
            setIsSelecting(false)
        }
    }

    const fetchAllGamesList = async () => {
        try {
            const res: GameListResponse = await fetchQtCallback({
                event: "all_game_list_req",
                callbackEvent: "all_game_list_rsp",
                mockData: all_game_list_rsp,
            })
            handleAllGamesListChange(res)
        } catch (error) {
            console.error("fetchAllGamesList error", error)
        }
    }

    const handleAllGamesListChange = (data: GameListResponse) => {
        const allGamesList = data?.items?.filter((item) => item.is_game)
        console.log("allGamesList", allGamesList)
        setIsSingleGame(allGamesList.length <= 1)
        setAllGameInfo((prev) => {
            return {
                ...prev,
                // uuid: allGameItem?.uuid || "",
                firstTheme: allGamesList[0]?.theme || prev.firstTheme,
                isSingleGame: allGamesList.length === 1,
                allGamesList,
            }
        })
        const isNotFoundPage = location.pathname.includes("/404")
        const isAllGamePage = location.pathname.includes("/all_games")

        if (isAllGamePage) {
            // 如果当前在全部游戏页且只有一个游戏，直接跳转到该游戏
            if (allGamesList.length === 1)
                handleSelectGame(allGamesList[0]?.uuid)
            // 如果当前在全部游戏页且没有游戏，跳转到404页
            if (allGamesList.length === 0) navigate("/all_games/404")
            // 如果当前在404页且有多个游戏，跳转到全部游戏页
            if (isNotFoundPage && allGamesList.length > 1)
                navigate("/all_games")
        }
    }

    const fetchGameList = async () => {
        try {
            setIsLoading(true)
            const res: GameListResponse = await fetchQtCallback({
                event: "downloaded_game_list_req",
                // data: {},
                callbackEvent: "downloaded_game_list_rsp",
                mockData: downloaded_game_list_rsp,
            })
            const { items } = res
            setGameList(items)
            const initialGame = items.find(
                (item) => item.uuid === urlParams.get("uuid")
            )
            if (initialGame) {
                setSelectedGame(initialGame)
            }

            setIsLoading(false)
        } catch (error) {
            setIsLoading(false)
            console.error("fetchGameList error", error)
        }
    }

    // 初始主动获取下载游戏列表和全部游戏列表
    useEffect(() => {
        fetchGameList()
        fetchAllGamesList()
        const allGameUuid = urlParams.get("all_game_uuid") // 全部游戏的uuid
        const allGameFirstTheme = urlParams.get("all_game_first_theme") // 全部游戏页的首个游戏类型
        if (allGameUuid) {
            setAllGameInfo((prev) => ({
                ...prev,
                uuid: allGameUuid,
                firstTheme: allGameFirstTheme,
            }))
        }
        // 初始是否显示404页面，客户端控制
        if (urlParams.get("show_404_page") === "true") {
            navigate("/404")
        }
    }, [])

    return (
        <div
            className={clsx(
                "w-[88px]", // 固定宽度，防止初始抖动
                "h-full bg-[rgb(24,23,26)]/90",
                "flex flex-col items-center",
                "px-[14px] py-[1.125rem]",
                "shrink-0",
                "border-[1px] border-white/10 rounded-[18px]"
            )}
            style={{
                boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.65)",
            }}
        >
            <img src={logo} alt="logo" className="w-[2rem] mb-5 " />

            <div
                className={clsx(
                    "flex-1 flex flex-col items-center space-y-2",
                    isSingleGame ? "justify-start" : "justify-center"
                )}
            >
                <AnimatePresence>
                    {gameList.map((game, i) => {
                        const isNewGame = newGameIds.includes(game.uuid)
                        return (
                            <motion.div
                                key={game.uuid}
                                layout
                                initial={{
                                    opacity: 0,
                                    scale: isNewGame ? 0.5 : 1,
                                }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 1 }}
                                transition={{ duration: 0.5 }}
                            >
                                <GameIconItem
                                    key={game.uuid}
                                    isNewGame={isNewGame}
                                    game={game}
                                    isSelected={
                                        selectedGame?.uuid === game.uuid
                                    }
                                    onSelect={handleSelectGame}
                                    onAnimationComplete={(uuid) => {
                                        // console.log("onAnimationComplete", uuid)
                                        // setNewGameIds((prev) =>
                                        //     prev.filter((id) => id !== game.uuid)
                                        // )
                                        setNewGameIds([])
                                    }}
                                />
                            </motion.div>
                        )
                    })}
                </AnimatePresence>
            </div>
            {!isSingleGame && (
                <AllGamesIconItem
                    isAllGames={isAllGames}
                    onJumpAllGames={handleJumpAllGames}
                />
            )}
        </div>
    )
}
