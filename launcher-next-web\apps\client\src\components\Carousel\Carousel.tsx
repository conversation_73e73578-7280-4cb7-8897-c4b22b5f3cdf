import { useEffect, useState } from "react"
import clsx from "clsx"
import useEmblaCarousel from "embla-carousel-react"
import { wrap } from "popmotion"
import { twMerge } from "tailwind-merge"

import bridge from "@/utils/bridge"
import { useInterval, useImagePreload } from "@/hooks"
import useEvent from "@/hooks/useEvent"
import { ArrowIcon } from "@/components/Icon"

// import "./styles.css";

const arrowClassName = clsx(
    "absolute top-1/2 z-10 translate-y-[-50%]",
    "w-[26px] h-[36px] rounded-[100px]",
    "border-[1px] border-white/10",
    "flex justify-center items-center",
    "bg-[rgb(24,23,26)]/[0.55] hover:bg-[rgb(24,23,26)]/[0.78] transition-colors",
    "cursor-pointer"
    // "text-white hover:text-primary "
)

interface CarouselProps {
    images: { url: string; jump_url?: string }[]
    interval: number
    onClick?: (url?: string) => void
}

export const Carousel = ({ images = [], interval, onClick }: CarouselProps) => {
    const [[page, direction], setPage] = useState([0, 0])
    const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true })

    const imageIndex = wrap(0, images.length, page)

    const { reClock } = useInterval(() => paginateNext(), interval)

    function paginateNext() {
        emblaApi?.scrollNext()
    }
    function paginateTo(newPage: number) {
        if (newPage === page) {
            return
        }
        emblaApi?.scrollTo(newPage)
    }

    const logSlidesInView = useEvent((emblaApi) => {
        const newPage: number = emblaApi.selectedScrollSnap()
        setPage([newPage, page < newPage ? 1 : -1])
        reClock()
    })

    useEffect(() => {
        if (emblaApi) emblaApi.on("select", logSlidesInView)
    }, [emblaApi, logSlidesInView])

    return (
        <div className="group h-full relative">
            {/* preload */}
            <div className="opacity-0 absolute pointer-events-none">
                {images.map((image, index) => (
                    <img key={image.url + index} src={image.url}></img>
                ))}
            </div>
            <div className="w-full h-full overflow-hidden" ref={emblaRef}>
                <div className="flex relative">
                    {images.map((img, index) => (
                        <div
                            className="flex-shrink-0 flex-grow-0 basis-full cursor-pointer"
                            key={img.url + index}
                            onClick={() => onClick?.(img?.jump_url)}
                        >
                            <img src={img.url} />
                        </div>
                    ))}
                </div>
            </div>
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div
                    className={clsx(arrowClassName, "rotate-180 left-[8px]")}
                    onClick={() => emblaApi?.scrollPrev()}
                >
                    <ArrowIcon />
                </div>
                <div
                    className={clsx(arrowClassName, "right-[8px]")}
                    onClick={() => emblaApi?.scrollNext()}
                >
                    <ArrowIcon />
                </div>
                <div
                    className={clsx(
                        "flex",
                        "absolute z-10 right-[10px] bottom-[9px]"
                    )}
                >
                    {images.map((img, i) => (
                        <div
                            key={i}
                            onClick={() => paginateTo(i)}
                            className={twMerge(
                                clsx(
                                    "cursor-pointer",
                                    "w-[14px] h-[14px] px-[2px] py-[5px]"
                                )
                            )}
                        >
                            <div
                                className={clsx(
                                    "w-full h-full hover:bg-white",
                                    i === imageIndex
                                        ? "bg-white"
                                        : "bg-white/[0.55]"
                                )}
                            ></div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}
