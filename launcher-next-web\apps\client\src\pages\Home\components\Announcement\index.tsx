import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import dayjs from "dayjs"
import { formatDate } from "@/utils/format"
import bridge from "@/utils/bridge"
import { Tabs } from "@/components/Tabs"

import { AnnouncementTab } from "@/types/mainData"
import { InfoList } from "./InfoList"

export function Announcement({
    data: oriTabs,
    appcode,
    fullRadius,
}: {
    data?: AnnouncementTab[]
    appcode?: string
    fullRadius?: boolean
}) {
    const [activeIndex, setActiveIndex] = useState<number>(0)

    const formatTabs =
        oriTabs?.map((tab) => ({
            id: tab.tabName,
            // tab_id: tab.tab_id,
            title: tab.tabName,
            content: tab.announcements.map((anno) => ({
                url: anno.jump_url,
                title: anno.content,
                date: formatDate(+anno.start_ts),
            })),
        })) || []
    const tabs = formatTabs.filter((item) => item?.content?.length)

    if (!tabs || !tabs?.length) return null

    const activeTab = tabs[activeIndex || 0]
    const activeTabContent = activeTab?.content || []

    const handleTabChange = (index: number) => {
        setActiveIndex(index)
        bridge.eventLog("game", "click", {
            page: "home",
            target: "tab",
            tab_id: tabs[index]?.id,
        })
    }

    const handleClick = (url?: string) => {
        bridge.open({ url }, "announcement", {}, "launcher_notice_jump_event", {
            appcode,
            jump_url: url,
            tab_id: activeTab?.id,
        })
    }

    return (
        <div className="flex flex-col h-full w-[26.25rem] border-l-[0px] border-white/10">
            {tabs.length ? (
                <>
                    <Tabs
                        value={activeIndex}
                        tabs={tabs}
                        fullRadius={fullRadius}
                        onChange={handleTabChange}
                    />
                    <InfoList
                        data={activeTabContent}
                        fullRadius={fullRadius}
                        onClick={handleClick}
                    />
                </>
            ) : null}
        </div>
    )
}
