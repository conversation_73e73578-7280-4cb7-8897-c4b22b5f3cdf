export const GameHomeIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={52}
            height={52}
            fill="none"
        >
            <g filter="url(#a)">
                <path
                    fill="#fff"
                    d="M25 33h-8v-6h8v6Zm10 0h-8v-6h8v6Zm-10-8h-8v-6h8v6Zm10 0h-8v-6h8v6Z"
                />
            </g>
            <defs>
                <filter
                    id="a"
                    width={68}
                    height={68}
                    x={-8}
                    y={-6}
                    colorInterpolationFilters="sRGB"
                    filterUnits="userSpaceOnUse"
                >
                    <feFlood floodOpacity={0} result="BackgroundImageFix" />
                    <feColorMatrix
                        in="SourceAlpha"
                        result="hardAlpha"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    />
                    <feOffset dy={2} />
                    <feGaussianBlur stdDeviation={4} />
                    <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_1595_23937"
                    />
                    <feBlend
                        in="SourceGraphic"
                        in2="effect1_dropShadow_1595_23937"
                        result="shape"
                    />
                </filter>
            </defs>
        </svg>
    )
}
