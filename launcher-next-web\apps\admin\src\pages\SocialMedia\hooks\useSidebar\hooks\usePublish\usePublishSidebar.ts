import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useSidebarKey } from "../../useSidebarKey";
import { PublishRequest } from "../../types";
import { useSWRConfig } from "swr";
import { useAppCode } from "@/hooks";

export function usePublishSidebar() {
  const key = useSidebarKey();
  const appCode = useAppCode();
  const { mutate } = useSWRConfig();
  const { trigger } = useSWRMutation(key, publishSidebar, {
    onSuccess() {
      mutate([URL_PREFIX + "/get_orders", appCode]);
    },
  });
  return trigger;
}

async function publishSidebar(_: any, { arg }: { arg: PublishRequest }) {
  await fetcher(`${URL_PREFIX}/publish`, arg);
}
