import dayjs from "dayjs"

export const formatBytes = (
    value: number,
    decimals = 2,
    unit: "B" | "KB" | "MB" | "GB" | "count" = "B"
) => {
    if (value < 0 || !value)
        return { value: 0, unit: unit === "count" ? "" : "B" }
    if (unit === "count") return { value, unit: "" }

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ["B", "KB", "MB", "GB"]

    let i = sizes.indexOf(unit)
    while (value >= k && i < sizes.length - 1) {
        value /= k
        i++
    }

    return {
        value: parseFloat(value.toFixed(dm)),
        unit: sizes[i],
    }
}
export const formatDuration = (seconds: number) => {
    if (seconds < 0 || !seconds) return "00:00:00"
    const h = Math.floor(seconds / 3600)
    const m = Math.floor((seconds % 3600) / 60)
    const s = Math.floor(seconds % 60)

    return [h, m, s].map((n) => n.toString().padStart(2, "0")).join(":")
}

export const formatDate = (ts: number) => {
    if (ts < 0 || !ts) return ""
    const curYear = dayjs().year()
    const tsYear = dayjs(+ts).year()
    if (curYear !== tsYear) {
        return dayjs(+ts).format("YYYY/MM/DD")
    }
    return dayjs(+ts).format("MM/DD")
}
