import React from "react"
import ReactDom from "react-dom/client"
import App from "./App"
import QWebChannelPromise from "./utils/qwebchannel"

import "@/styles/global.scss"
import "@/styles/tailwind.css"

const Root: React.FC = () => {
    return <App />
}

QWebChannelPromise().then(() => {
    const container = document.getElementById("root")
    if (container) {
        const root = ReactDom.createRoot(container)
        root.render(<Root />)
    }
})
