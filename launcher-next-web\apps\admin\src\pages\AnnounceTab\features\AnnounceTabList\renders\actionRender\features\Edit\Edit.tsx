import { Typography } from "antd";
import { BannerActionProps } from "../types";
import { EditForm } from "@/pages/AnnounceTab/features";
import { useUpdateTab } from "@/pages/AnnounceTab/hooks";

export function Edit({ tab }: BannerActionProps) {
  const update = useUpdateTab();
  return (
    <EditForm
      title="编辑公告页签"
      trigger={<Typography.Link>编辑</Typography.Link>}
      initialValues={tab}
      onFinish={async (value) => {
        update({ ...tab, ...value });
      }}
    />
  );
}
