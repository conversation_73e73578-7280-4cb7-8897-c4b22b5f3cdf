import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M15 6.75c1.137 0 2.267-.01 3.406.054 1.32.064 2.49.364 3.459 1.331.965.966 1.267 2.139 1.33 3.46.065 1.137.054 2.266.054 3.406 0 1.139.013 2.268-.053 3.405-.064 1.32-.364 2.491-1.331 3.459-.966.966-2.139 1.267-3.46 1.33-1.137.065-2.265.055-3.405.055-1.137 0-2.267.01-3.406-.054-1.32-.064-2.491-.363-3.459-1.331-.966-.966-1.267-2.138-1.331-3.46-.064-1.136-.054-2.267-.054-3.404 0-1.138-.01-2.267.054-3.407.064-1.32.363-2.49 1.331-3.459.966-.965 2.138-1.267 3.46-1.33C12.73 6.74 13.86 6.75 15 6.75Zm-.002 1.481c-1.203 0-3.783-.097-4.869.332-.375.15-.654.334-.945.623-.291.29-.472.57-.623.946-.43 1.085-.333 3.665-.333 4.869 0 1.203-.096 3.78.333 4.868.15.375.332.654.623.945.289.291.57.473.945.623 1.086.43 3.666.333 4.87.333 1.203 0 3.78.096 4.866-.335a2.64 2.64 0 0 0 .945-.623c.29-.289.472-.57.623-.945.431-1.086.335-3.663.335-4.866 0-1.204.096-3.784-.333-4.87a2.642 2.642 0 0 0-.623-.945 2.624 2.624 0 0 0-.946-.623c-1.085-.43-3.664-.332-4.868-.332Zm0 2.536a4.227 4.227 0 0 1 4.233 4.234 4.227 4.227 0 0 1-4.233 4.232 4.227 4.227 0 0 1-4.233-4.232 4.228 4.228 0 0 1 4.233-4.234Zm0 1.482A2.758 2.758 0 0 0 12.246 15a2.758 2.758 0 0 0 2.752 2.75 2.758 2.758 0 0 0 2.751-2.75A2.758 2.758 0 0 0 15 12.249Zm4.407-2.642a.988.988 0 1 1 0 1.976.988.988 0 0 1 0-1.976Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.5}
        height={31.5}
        x={-0.75}
        y={1.125}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50700"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50700"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
