import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../../const";
import { useLauncherPackageListKey } from "../useLauncherPackageListKey";
import { UpdateNoteRequest } from "./types";

export function useUpdateNote() {
  const key = useLauncherPackageListKey();
  const { trigger, isMutating } = useSWRMutation(key, updateNote);
  return { trigger, isMutating };
}

async function updateNote(
  [url, { page, searchVal, platformVal }]: any,
  { arg }: { arg: UpdateNoteRequest },
): Promise<any> {
  return await fetcher(`${URL_PREFIX}/description/update`, arg);
}
