import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useBannerKey } from "../useBannerKey";
import { RemoveBannerRequest } from "../types";

export function useRemoveBanner() {
  const key = useBannerKey();
  const { trigger } = useSWRMutation(key, remove);
  return trigger;
}

async function remove(_: any, { arg }: { arg: RemoveBannerRequest }) {
  await fetcher(`${URL_PREFIX}/delete`, arg);
}
