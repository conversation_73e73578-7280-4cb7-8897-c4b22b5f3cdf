import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M15 6a9 9 0 1 1-.001 18.001A9 9 0 0 1 15 6Zm3.98 3.94c-.414 0-.774.23-.957.57l-1.84-.532a.428.428 0 0 0-.512.237l-1.173 2.634c-.962.066-1.846.327-2.573.724a1.446 1.446 0 1 0-1.847 2.027c-.092.272-.14.555-.14.846 0 1.997 2.266 3.616 5.062 3.616 2.796 0 5.063-1.619 5.063-3.616 0-.291-.049-.574-.141-.845.508-.225.864-.734.866-1.323a1.447 1.447 0 0 0-2.71-.703c-.743-.405-1.65-.668-2.636-.728l.864-1.939 1.651.476A1.086 1.086 0 1 0 18.98 9.94Zm-2.5 7.329a.43.43 0 0 1 .533.674c-.519.41-1.308.673-2.013.673-.705 0-1.494-.263-2.01-.673a.429.429 0 1 1 .53-.674c.297.235.89.487 1.48.487s1.181-.253 1.48-.487ZM12.83 15a.724.724 0 1 1 0 1.448.724.724 0 0 1 0-1.448Zm4.34 0a.723.723 0 1 1-.001 1.446.723.723 0 0 1 0-1.446Zm-6.509-1.446c.287 0 .538.169.652.414-.377.287-.69.617-.921.98a.724.724 0 0 1 .27-1.395Zm8.678 0a.724.724 0 0 1 .27 1.394 3.823 3.823 0 0 0-.923-.98.721.721 0 0 1 .653-.414Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={33}
        x={-1.5}
        y={0.375}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50766"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50766"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
