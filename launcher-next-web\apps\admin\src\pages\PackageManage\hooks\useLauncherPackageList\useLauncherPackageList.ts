import useSWR from "swr";
import { fetcher } from "@/utils/fetcher";
import { ListResponse } from "./types";
import { useLauncherPackageListKey } from "./useLauncherPackageListKey";
import { message } from "antd";

export function useLauncherPackageList() {
  const key = useLauncherPackageListKey();
  const { data, isLoading, mutate } = useSWR(
    key,
    ([url, appCode, { page, pageSize, searchVal }]) => {
      if ("channel" in searchVal) delete searchVal.channel; // 不传channel

      return fetcher<ListResponse>(url, {
        ...searchVal,
        appcode: appCode,
        page_num: page,
        page_size: pageSize,
      });
    },
    {
      onError(err, key, config) {
        // console.log(">>>err", err);
        message.error(err?.message || "请求错误");
      },
    },
  );
  const launchers = data?.launchers;
  const total = Number(data?.total);

  return {
    launchers,
    total,
    isLoading,
    mutate,
  };
}
