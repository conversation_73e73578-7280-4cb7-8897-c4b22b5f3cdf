import { useEffect, useRef } from "react";
import useEvent from "./useEvent";

export function useInterval(
  fn: () => void,
  delay: number,
  options?: {
    immediate?: boolean;
  },
) {
  const callback = useEvent(fn);
  const lastRunTime = useRef(new Date().getTime());

  function reClock() {
    lastRunTime.current = new Date().getTime();
  }
  useEffect(() => {
    function check() {
      const now = new Date().getTime();
      if (now - lastRunTime.current >= delay) {
        callback();
        lastRunTime.current = now;
      }
      requestAnimationFrame(check);
    }
    check();
    if (options?.immediate) {
      callback();
    }
  }, [callback, delay, options?.immediate]);

  return {
    reClock,
  };
}
