import React, { useState } from "react"
import clsx from "clsx"
import { motion } from "framer-motion"
import { useLocale } from "@/hooks/useLocale"
import { Tooltip } from "@/components/Tooltip"

export const AllGamesIconItem: React.FC<{
    isAllGames: boolean
    onJumpAllGames: () => void
}> = ({ isAllGames, onJumpAllGames }) => {
    const t = useLocale()
    const [showTooltip, setShowTooltip] = useState(false)
    const squareClass = "w-[6px] h-[6px] bg-white"

    return (
        <div className="relative mt-5">
            <Tooltip
                show={showTooltip}
                title={t["allGames"]}
                placement="right-center"
                offset={9}
            >
                <motion.div
                    className="rounded-lg border-[2px] border cursor-pointer"
                    whileHover={{ borderColor: "rgba(255, 255, 255, 0.35)" }}
                    initial={{
                        borderColor: isAllGames
                            ? "rgba(255, 255, 255, 1)"
                            : "rgba(255, 255, 255, 0)",
                    }}
                    animate={{
                        borderColor: isAllGames
                            ? "rgba(255, 255, 255, 1)"
                            : "rgba(255, 255, 255, 0)",
                    }}
                    transition={{ duration: 0.3 }}
                    onClick={onJumpAllGames}
                    onMouseEnter={() => setShowTooltip(true)}
                    onMouseLeave={() => setShowTooltip(false)}
                >
                    <motion.div
                        initial={{
                            rotate: isAllGames ? 45 : 0,
                            scaleX: isAllGames ? 1 : 1.4,
                        }}
                        animate={{
                            rotate: isAllGames ? 45 : 0,
                            scaleX: isAllGames ? 1 : 1.4,
                        }}
                        className="w-[52px] h-[52px] flex justify-center items-center"
                        transition={{ duration: 0.3 }}
                    >
                        <motion.div className="relative w-[14px] h-[14px]">
                            <div
                                className={clsx(
                                    "absolute top-0 left-0",
                                    squareClass
                                )}
                            />
                            <div
                                className={clsx(
                                    "absolute top-0 right-0",
                                    squareClass
                                )}
                            />
                            <div
                                className={clsx(
                                    "absolute bottom-0 left-0",
                                    squareClass
                                )}
                            />
                            <div
                                className={clsx(
                                    "absolute bottom-0 right-0",
                                    squareClass
                                )}
                            />
                        </motion.div>
                    </motion.div>
                </motion.div>
            </Tooltip>
        </div>
    )
}
