<!doctype html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta name="viewport"
        content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, height=device-height, viewport-fit=cover" />
    <title></title>
    <script src="qrc:///web/js/qwebchannel.js"></script>
</head>

<body>
    <div id="root"></div>
    <script>

        function handleWindowsSize(msgBody) {
            var body = parseData(msgBody)
            var scaleRatio = body && body.scaleRatio || 1 // 启动器缩放比例
            console.log("scaleRatio", scaleRatio)

            var baseFontSize = 16
            var newFontSize = baseFontSize * scaleRatio
            document.documentElement.style.fontSize = `${newFontSize}px`
        }
    </script>
</body>

</html>