import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useSidebarKey } from "../useSidebarKey";
import { CreateSidebarRequest } from "../types";

export function useCreateSidebar() {
  const key = useSidebarKey();
  const { trigger } = useSWRMutation(key, createSidebar);
  return trigger;
}

async function createSidebar(_: any, { arg }: { arg: CreateSidebarRequest }) {
  await fetcher(`${URL_PREFIX}/create`, arg);
}
