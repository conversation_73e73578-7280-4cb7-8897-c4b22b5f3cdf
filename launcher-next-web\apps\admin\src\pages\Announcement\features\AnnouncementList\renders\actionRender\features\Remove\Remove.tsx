import { Popconfirm, Typography } from "antd";
import { ActionProps } from "../types";
import { useRemove } from "@/pages/Announcement/hooks";

export function Remove({ announcement }: ActionProps) {
  const removeBanner = useRemove();
  return (
    <Popconfirm
      title="是否确认删除此公告 ？"
      onConfirm={() =>
        removeBanner({ id: announcement.id, appcode: announcement.appcode })
      }
    >
      <Typography.Link>删除</Typography.Link>
    </Popconfirm>
  );
}
