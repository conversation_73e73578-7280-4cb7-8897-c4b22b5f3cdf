import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useSidebarKey } from "../useSidebarKey";
import { UpdateSidebarRequest } from "../types";

export function useUpdateSidebar() {
  const key = useSidebarKey();
  const { trigger } = useSWRMutation(key, updateSidebar);
  return trigger;
}

async function updateSidebar(_: any, { arg }: { arg: UpdateSidebarRequest }) {
  await fetcher(`${URL_PREFIX}/update`, arg);
}
