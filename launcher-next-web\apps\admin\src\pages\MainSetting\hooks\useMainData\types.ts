import { Language } from "@/data";
import { FileData } from "@/types";

/**
 * main_pageListMainPageConfigsRsp
 */
export interface Response {
  main_page_configs?: MainPageConfig[];
}

/**
 * main_pageMainPageConfig
 */
export interface MainPageConfig {
  appcode?: string;
  createTs?: string;
  data?: MainPageData;
  id?: string;
  language?: Language;
  modify_data?: MainPageData;
  operator?: string;
  start_ts?: string;
  state?: MainPageState;
  create_ts?: string;
  update_ts?: string;
}

/**
 * main_pageMainPageData
 */
export interface MainPageData {
  main_bg_image?: MainBgImageData;
  single_ent_image?: MainPageSingleEntImageData;
  icon?: FileData;
}

/**
 * main_pageMainBgImageData
 */
export type MainBgImageData = FileData;

/**
 * main_pageSingleEntImageData
 */
export interface MainPageSingleEntImageData {
  version_url?: string; // 版本信息图片
  version_md5?: string; // 版本信息图片的md5值
  jump_url?: string; // 跳转链接
  button_url?: string; // 按钮默认图片
  button_md5?: string; // 按钮默认图片的md5值
  button_hover_url?: string; // 按钮悬浮图片
  button_hover_md5?: string; // 按钮悬浮图片的md5值
}

export enum MainPageState {
  Unpublished = 1,
  Timing,
  Published,
  AutoOffLine,
  ManualOffLine,
}

/**
 * main_pageUpdateMainPageConfigReq
 */
export interface UpdateRequest {
  id?: string;
  appcode?: string;
  modify_data?: MainPageMainPageData;
  language?: string;
}

/**
 * main_pageMainPageData
 */
export interface MainPageMainPageData {
  main_bg_image?: MainBgImageData;
  single_ent_image?: MainPageSingleEntImageData;
  icon?: FileData;
}

/**
 * main_pagePublishMainPageConfigReq
 */
export interface PublishRequest {
  appcode?: string;
  id?: string;
  start_ts?: string;
  language?: string;
}

/**
 * main_pageCancelPublishMainPageConfigReq
 */
export interface CancelPublishRequest {
  appcode?: string;
  id?: string;
}
