import * as Sentry from "@sentry/react"

Sentry.init({
    release: _SENTRY_RELEASE_NAME,
    dsn: _SENTRY_DSN,
    integrations: [Sentry.browserTracingIntegration()],
    tracesSampleRate: 0.5,
    ignoreErrors: [
        // 忽略掉https://sentry.hypergryph.com/organizations/hypergryph/issues/45779/?project=17
        /@hg\/one-account/,
        // 忽略掉https://sentry.hypergryph.com/organizations/hypergryph/issues/46133/?project=17
        /^undefined is not an object \(evaluating 'this._audioElem.play\(\).catch'\)$/,
        /^TypeError: Cannot read property 'play' of undefined$/,
        // 忽略掉https://sentry.hypergryph.com/organizations/hypergryph/issues/46145/?project=17
        /^window\.android\.unLoad is not a function$/,
        /^TypeError: window\.android\.unLoad is not a function$/,
        // 忽略掉https://sentry.hypergryph.com/organizations/hypergryph/issues/46136/?project=17
        /^undefined is not an object \(evaluating 'r\.play\(\).catch'\)$/,
        /^TypeError: Cannot read property 'play' of undefined$/,
        // 忽略掉https://sentry.hypergryph.com/organizations/hypergryph/issues/46286/?project=17
        /^__TB_API__CB_\d+_\d+ is not defined$/,
    ],
})
