// 主容器动画
.content-container-appear,
.content-container-enter {
    .footer-container {
        opacity: 0;
        transform: translateY(20px);
    }
    .right-sidebar-container {
        opacity: 0;
        transform: translate(20px, -50%);
    }
    .single-ent-container {
        opacity: 0;
        transform: translateY(20px);
    }
}

.content-container-appear-active,
.content-container-enter-active {
    .footer-container {
        opacity: 1;
        transform: translateY(0);
        transition:
            opacity 300ms,
            transform 300ms;
    }
    .right-sidebar-container {
        opacity: 1;
        transform: translate(0, -50%);
        transition:
            opacity 100ms,
            transform 100ms;
    }
    .single-ent-container {
        opacity: 1;
        transform: translateY(0);
        transition:
            opacity 300ms,
            transform 300ms;
    }
}

.content-container-exit {
    .footer-container {
        opacity: 1;
        transform: translateY(0);
    }
    .right-sidebar-container {
        opacity: 1;
        transform: translate(0, -50%);
    }
    .single-ent-container {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-container-exit-active {
    .footer-container {
        opacity: 0;
        transform: translateY(20px);
        transition:
            opacity 300ms,
            transform 300ms;
    }
    .right-sidebar-container {
        opacity: 0;
        transform: translate(20px, -50%);
        transition:
            opacity 100ms,
            transform 100ms;
    }
    .single-ent-container {
        opacity: 0;
        transform: translateY(20px);
        transition:
            opacity 300ms,
            transform 300ms;
    }
}

// 内容区域动画
.content-enter {
    opacity: 0;
    transform: translateX(-50px);
}

.content-enter-active {
    opacity: 1;
    transform: translateX(0px);
    transition:
        opacity 300ms,
        transform 300ms;
}

.content-exit {
    opacity: 1;
    transform: translateX(0px);
}

.content-exit-active {
    opacity: 0;
    transform: translateX(50px);
    transition:
        opacity 300ms,
        transform 300ms;
}

// 内容容器样式
// .content-container {
//     height: 10rem;
//     width: 42.5rem;
//     padding-left: 1.5rem;
//     padding-right: 1.5rem;
//     display: flex;
//     align-items: center;
//     gap: 1rem;
//     border: 0.5px solid rgba(255, 255, 255, 0.1);
//     box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.65);
// }
