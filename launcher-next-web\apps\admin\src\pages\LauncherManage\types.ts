export enum ReleaseState {
  ReleaseState_RESERVE = "ReleaseState_RESERVE",
  ReleaseState_INNER = "ReleaseState_INNER", // 内部访问
  ReleaseState_RELEASE = "ReleaseState_RELEASE", // 外部可访问
}

export enum AppEnvType {
  AppEnvType_RESERVE = "AppEnvType_RESERVE",
  AppEnvType_QA = "AppEnvType_QA", // 测试应用
  AppEnvType_PROD = "AppEnvType_PROD", // 正式应用
}
export interface ListHgLauncherAppRequest {
  caption: string; //标识名
  launcher_envs: AppEnvType[]; // 应用类型
  launcher_release_states: ReleaseState[]; // 应用类
  page_num: number;
  page_size: number;
}

export interface ListHgLauncherAppReply {
  total: number;
  launcher_briefs: HgLauncherBriefConfig[];
}

export interface HgLauncherBriefConfig {
  id: number;
  caption: string; // 标识名
  latest_version: string; // 最新版本号
  force_update_version: string; // 最低可玩版本
  launcher_release_state: ReleaseState; // 是否外部可访问
  launcher_env: AppEnvType; // 应用类型
  publish_overview: PublishOverview; // 发布概览
  appcode: string;
}

export interface PublishOverview {
  abnormal_num: number; // 状态异常数
  in_task_num: number; // 进行中数
  wait_for_publish_num: number; // 待发布数
  online_num: number; // 发布完成数
}

export interface GetHgLauncherAppRequest {
  appcode: string;
}

export interface GetHgLauncherAppReply {
  launcher_app: HgLauncherApp;
}

export interface HgLauncherApp {
  id: number; // 首次创建不用填
  appcode: string; // 首次创建不用填
  caption: string; // 标识名
  launcher_release_state: ReleaseState; // 是否外部可访问
  launcher_env: AppEnvType; // 应用类型
  publish_setting: LauncherPublishSetting; // launcher发布配置
}

export interface LauncherPublishSetting {
  cloud_types: string[]; // 云商列表
  inner_domain: string; // 内部测试资源域名
  public_cdn_domin: string; // 非加签公开资源域名
  sign_cdn_domin: string; // 加签域名
}

export interface SaveHgLauncherAppRequest {
  launcher_app: HgLauncherApp;
}
