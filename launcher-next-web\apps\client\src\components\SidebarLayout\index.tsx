import React, { useEffect, useMemo } from "react"
import { Outlet, useLocation } from "react-router-dom"
import { useHydrateAtoms } from "jotai/utils"
import { motion, AnimatePresence } from "framer-motion"
import { GameSidebar } from "../GameSidebar/index"
import { PageHeader } from "../PageHeader/index"
import { Provider } from "jotai"
import { useOverlayCount } from "@/store/useOverlayCount"
import { languageAtom, useLanguage } from "@/store/useLanguage"
import { selectedGameAtom } from "@/store/useSelectedGame"
import { globalDataAtom } from "@/store/useGlobalData"
import { useUrlParams } from "@/hooks/useUrlParams"
import { Channel } from "@/types/channel"
import {
    useOnShowMarkWndNotify,
    useOnLangChangedNotify,
} from "@/utils/eventBus"

const HydrateAtoms = ({ initialValues, children }: any) => {
    useHydrateAtoms(initialValues)
    return children
}

const parseUrlParams = () => {
    const hash = window.location.hash
    const hashIndex = hash.indexOf("#")
    if (hashIndex >= 0) {
        const queryIndex = hash.indexOf("?")
        const queryString = hash.substring(queryIndex + 1)
        return new URLSearchParams(queryString)
    } else {
        return new URLSearchParams(window.location.search)
    }
}

export const SidebarLayout: React.FC = () => {
    const [, setLanguage] = useLanguage()
    useOnLangChangedNotify((data) => {
        console.log("useOnLangChangedNotify", data)
        setLanguage({
            lang: data.lang,
            main_btn_width: isNaN(Number(data.main_btn_width))
                ? 218
                : Number(data.main_btn_width) || 218,
        })
    })

    const [overlayCount, setOverlayCount] = useOverlayCount()
    useOnShowMarkWndNotify((data) => {
        console.log("useOnShowMarkWndNotify", data)
        // 弹窗计数
        setOverlayCount((prev) => {
            if (data.show) {
                return prev + 1
            } else {
                return prev - 1 < 0 ? 0 : prev - 1
            }
        })
    })

    return (
        <div
            className="flex h-screen bg-white/0 rounded-3xl p-3 select-none gap-2 overflow-y-hidden"
            style={{
                border: "1px solid rgba(24,23,26, 0.1)",
            }}
        >
            <GameSidebar />
            <main
                className="flex-1 rounded-[18px] overflow-hidden select-none"
                style={{
                    boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.65)",
                }}
            >
                <PageHeader />
                <Outlet />
            </main>
            <AnimatePresence>
                {overlayCount > 0 && (
                    <motion.div
                        key="overlay"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="fixed inset-0 rounded-3xl bg-black/40 z-50"
                        style={{
                            backdropFilter: "blur(6px)",
                            WebkitBackdropFilter: "blur(6px)",
                        }}
                    />
                )}
            </AnimatePresence>
        </div>
    )
}

export const SidebarLayoutProvider: React.FC = () => {
    const urlParams = parseUrlParams()
    const params = Object.fromEntries(urlParams.entries())

    const initialGame = useMemo(() => {
        return {
            uuid: params.uuid,
            app_code: params.app_code,
            channel: Number(params.channel),
            sub_channel: Number(params.sub_channel),
            region: params.region,
            theme: params.theme,
            is_game: params.is_game === "true",
            show_404_page: params.show_404_page === "true",
        }
    }, [params])

    const initalLanguage = {
        lang: params.lang || "zh-cn",
        main_btn_width: isNaN(Number(params.main_btn_width))
            ? 218
            : Number(params.main_btn_width) || 218,
    }
    const globalData = {
        hg_channel_name: params.hg_channel_name || Channel.official,
    }

    window.isDebug = params.debug === "true"

    return (
        <Provider>
            <HydrateAtoms
                initialValues={[
                    [selectedGameAtom, initialGame],
                    [languageAtom, initalLanguage],
                    [globalDataAtom, globalData],
                ]}
            >
                <SidebarLayout />
            </HydrateAtoms>
        </Provider>
    )
}
