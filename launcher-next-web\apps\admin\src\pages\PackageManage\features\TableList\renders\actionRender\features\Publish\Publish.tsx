import { Typography, Modal, message } from "antd";
import { useState, useCallback } from "react";
import semver from "semver";

import {
  LauncherPackageConfig,
  PackagePublishState,
} from "@/pages/PackageManage/hooks/useLauncherPackageList";
import { PublishPackageDetail } from "@/pages/PackageManage/features/PublishPackageDetail";

const maxVersion = (version1: string, version2: string) => {
  if (!version1 || !version2) {
    return version1 || version2;
  }
  return semver.gt(version1, version2) ? version1 : version2;
};

export function Publish({ config }: { config: LauncherPackageConfig }) {
  const {
    state = "",
    version = "",
    latest_version = "",
    gray_latest_version = "",
  } = config;
  const enableStatus: string[] = [
    PackagePublishState.LauncherState_UNPUBLISH,
    PackagePublishState.LauncherState_Gray,
  ];
  const statusEnable = enableStatus.includes(state);
  const packageVersionEnable = latest_version
    ? semver.gte(version, maxVersion(latest_version, gray_latest_version))
    : true;
  const isReportEnable = statusEnable && packageVersionEnable;

  return (
    <PublishModal config={config} disabled={!isReportEnable}></PublishModal>
  );
}

function PublishModal({
  config,
  disabled,
}: {
  config: LauncherPackageConfig;
  disabled: boolean;
}) {
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const handleOpenModal = useCallback(() => {
    setModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setModalOpen(false);
  }, []);

  return (
    <>
      <Typography.Link
        style={{ whiteSpace: "nowrap" }}
        disabled={disabled}
        onClick={handleOpenModal}
      >
        发布
      </Typography.Link>
      <Modal
        title="发布"
        width={720}
        destroyOnClose
        maskClosable={false}
        open={modalOpen}
        footer={null}
        onCancel={handleCloseModal}
        // loading={isLoading}
      >
        <PublishPackageDetail
          config={config}
          onCancel={handleCloseModal}
        ></PublishPackageDetail>
      </Modal>
    </>
  );
}
