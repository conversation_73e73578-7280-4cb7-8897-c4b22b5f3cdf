import { Button } from "antd";
import { Publish } from "@/pages/Banner/features/Publish";
import { useBatchSelectValue, BannerState } from "../../../../hooks";

export function BatchPublish() {
  const batchBannerConfig = useBatchSelectValue();
  const batchEnable = batchBannerConfig.some((item) => {
    return item.state === BannerState.Unpublished;
  });

  return (
    <Publish
      bannersToPublish={batchBannerConfig}
      trigger={<Button disabled={!batchEnable}>批量发布</Button>}
    />
  );
}
