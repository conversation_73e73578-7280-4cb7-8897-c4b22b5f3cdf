import { useEffect, useMemo, useState, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigate } from "react-router-dom"
import clsx from "clsx"

import { Game, GameListResponse, SwitchGameResponse } from "@/types/games"
import { fetchQtCallback } from "@/utils/qwebchannel"
import { getQtPath } from "@/utils/misc"
import bridge from "@/utils/bridge"
import { useUrlParams } from "@/hooks/useUrlParams"
import { useLanguageValue } from "@/store/useLanguage"
import { usePrefetchOnHover } from "@/hooks/useMainData"
import { useSetSelectedGame } from "@/store/useSelectedGame"
import { useAllGameInfo } from "@/store/useAllGameInfo"
import { usePrevBackgroundImage } from "@/store/usePrevBackgroundImage"
import { FallbackImage } from "@/components/FallbackImage"

import { eventBus } from "@/utils/eventBus"
import { EventName } from "@/utils/eventBus"

import "./index.scss"

const AllGamesPage: React.FC = () => {
    const qtPath = getQtPath()
    const { lang } = useLanguageValue()
    const navigate = useNavigate()
    const urlParams = useUrlParams()
    const [globlePrevBackgroundImage, setGloblePrevBackgroundImage] =
        usePrevBackgroundImage()
    const [allGameInfo, setAllGameInfo] = useAllGameInfo()
    const setSelectedGame = useSetSelectedGame()

    const { startPrefetch, cancelPrefetch } = usePrefetchOnHover(200)

    const firstTheme =
        allGameInfo.firstTheme ||
        urlParams.get("all_game_first_theme") ||
        "theme_2"
    const backgroundImage = `${qtPath}/theme/${firstTheme}/background.png`

    const [activeIndex, setActiveIndex] = useState<number>(0)
    const [activeBackgroundImage, setActiveBackgroundImage] = useState<
        string | null
    >(backgroundImage)
    const [prevBackgroundImage, setPrevBackgroundImage] = useState<
        string | null
    >(globlePrevBackgroundImage)

    const allGamesList = useMemo(() => {
        return allGameInfo?.allGamesList || []
    }, [allGameInfo.allGamesList])

    const firstRenderRef = useRef(true)
    const [hasAnimation, setHasAnimation] = useState(!!prevBackgroundImage)

    useEffect(() => {
        handlePageViewEvent()
        if (firstRenderRef.current) {
            firstRenderRef.current = false
        }
    }, [])

    useEffect(() => {
        setGloblePrevBackgroundImage(backgroundImage) // 解决进入全部游戏页后直接点击游戏图标，prev背景图没有值的问题
        if (backgroundImage === activeBackgroundImage) return
        // console.log(">>>allGameInfo change")
        setActiveBackgroundImage(backgroundImage)
        setActiveIndex(0)
    }, [allGameInfo])

    // 启动器侧-全部游戏页曝光埋点
    const handlePageViewEvent = () => {
        bridge.eventLog("launcher", "launcher_page_exposure", {
            type: "gamelist",
        })
    }

    const handleMouseEnter = (game: Game, index: number) => {
        // 预加载数据
        startPrefetch(game)
        setActiveIndex(index)
        const newBackgroundImage = `${qtPath}/theme/${game.theme}/background.png`
        if (newBackgroundImage === activeBackgroundImage) return
        if (!firstRenderRef.current && !hasAnimation) setHasAnimation(true)
        setPrevBackgroundImage(activeBackgroundImage)
        setActiveBackgroundImage(newBackgroundImage)
        setGloblePrevBackgroundImage(newBackgroundImage)
    }

    const handleClickGame = async (game: Game) => {
        const data = {
            from: allGameInfo.uuid,
            to: game.uuid,
        }
        eventBus.emit(EventName.SwitchGameNotify, data)
        // const res: SwitchGameResponse = await fetchQtCallback({
        //     event: "switch_game_req",
        //     data,
        //     callbackEvent: "switch_game_rsp",
        //     mockData: {
        //         result: true,
        //         // cur_game: {
        //         //     show_404_page: true,
        //         // },
        //     },
        // })
        // console.log("handleClickGame switch_game_rsp", res)
        // if (res?.result) {
        //     setSelectedGame(game)
        //     if (res?.cur_game?.show_404_page) {
        //         navigate("/404")
        //     } else {
        //         navigate("/")
        //     }
        // }
    }

    return (
        <div
            className={clsx("flex flex-col", "relative h-full")}
            style={{
                boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.5)",
            }}
        >
            {/* 背景图层 - 旧图淡出 */}
            {prevBackgroundImage && (
                <div
                    key={`prev-${prevBackgroundImage}`}
                    onAnimationEnd={() => setPrevBackgroundImage(null)} // 动画结束后移除
                    className="prev-fade absolute inset-0 rounded-2xl z-0"
                    style={{
                        backgroundImage: `url(${prevBackgroundImage})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                    }}
                />
            )}
            {activeBackgroundImage && (
                <div
                    key={`active-${activeBackgroundImage}`}
                    className={clsx(
                        "absolute inset-0 rounded-2xl z-0",
                        hasAnimation && "active-fade"
                    )}
                    style={{
                        backgroundImage: `url(${activeBackgroundImage})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                    }}
                />
            )}

            {!!allGamesList.length && (
                <div className="animate-fadeSlideIn relative z-10 flex-1 flex flex-col justify-center px-10 py-6 overflow-auto scrollbar-hidden">
                    {allGamesList.map((game, index) => (
                        // <div
                        //     className={clsx(
                        //         "relative w-[130px] h-[130px] shrink-0",
                        //         "my-[15px]",
                        //     )}
                        //     key={game.uuid}
                        // >
                        <div
                            key={game.uuid}
                            className={clsx(
                                "relative my-[15px]",
                                "transition-all duration-300",
                                "shrink-0 overflow-hidden cursor-pointer",
                                // index === activeIndex
                                //     ? "scale-[1.234] rounded-[15px]"
                                //     : "scale-[1] rounded-[12px]",
                                index === activeIndex
                                    ? "w-[164px] h-[164px] rounded-[15px]"
                                    : "w-[130px] h-[130px] rounded-[12px]"
                            )}
                            style={{
                                transformOrigin: "left top",
                                // transformOrigin: "left center",
                                boxShadow:
                                    index === activeIndex
                                        ? "0px 3.16px 8.48px 0px rgba(0, 0, 0, 0.55)"
                                        : "0px 2px 6px 0px rgba(0, 0, 0, 0.55)",
                            }}
                            onMouseEnter={() => handleMouseEnter(game, index)}
                            onMouseLeave={cancelPrefetch}
                            onClick={() => handleClickGame(game)}
                        >
                            <FallbackImage
                                className="w-full h-full object-cover"
                                src={`${qtPath}/theme/${game.theme}/lang/${lang}/cover.png`}
                                fallback={`${qtPath}/theme/${game.theme}/lang/${"en-us"}/cover.png`}
                                key={game.uuid}
                            />
                            <div
                                className={clsx(
                                    "absolute inset-0 border-[3px] border-white rounded-[15px] duration-300 transition-opacity pointer-events-none",
                                    index === activeIndex
                                        ? "opacity-100"
                                        : "opacity-0"
                                )}
                            ></div>
                            <div
                                className={clsx(
                                    "absolute inset-0 border-[1px] border-white/10 rounded-[12px] duration-300 transition-opacity pointer-events-none",
                                    index === activeIndex
                                        ? "opacity-0"
                                        : "opacity-100"
                                )}
                            ></div>
                        </div>
                        // </div>
                    ))}
                </div>
            )}
        </div>
    )
}

export default AllGamesPage
