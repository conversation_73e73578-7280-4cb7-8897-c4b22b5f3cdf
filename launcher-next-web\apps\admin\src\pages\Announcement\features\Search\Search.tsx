import { Input } from "antd";
import { useSearch, useSetPageValue } from "../../hooks";

export function Search() {
  const setPageValue = useSetPageValue();
  const [searchVal, setSearchVal] = useSearch();
  return (
    <Input.Search
      allowClear
      style={{ width: 300 }}
      defaultValue={searchVal}
      onSearch={(v) => {
        console.log(v);
        setPageValue(1);
        setSearchVal(v);
      }}
    />
  );
}
