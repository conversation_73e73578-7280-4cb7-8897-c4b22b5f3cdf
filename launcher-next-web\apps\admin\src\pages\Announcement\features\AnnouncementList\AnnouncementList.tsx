import { useState, useCallback } from "react";
import { Table } from "antd";
import { AnnouncementConfig } from "../../hooks/useAnnouncement/types";
import { useColumns, useOnTableChange } from "./hooks";
import {
  useAnnouncement,
  usePage,
  useSetSort,
  useSort,
  useBatchSelect,
} from "../../hooks";

export function AnnouncementList() {
  const [page, setPage] = usePage();
  const columns = useColumns();
  const { announcements, total } = useAnnouncement();
  const onTableChange = useOnTableChange();

  // const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedAnnouncement, setSelectedAnnouncement] = useBatchSelect();

  const onSelectChange = useCallback(
    (selectedRowKeys: React.Key[], selectedRows: AnnouncementConfig[]) => {
      console.log("selectedRowKeys changed: ", selectedRowKeys);
      console.log("selectedRows changed: ", selectedRows);
      // setSelectedRowKeys(selectedRowKeys);
      setSelectedAnnouncement(selectedRows);
    },
    [],
  );

  const rowSelection = {
    preserveSelectedRowKeys: true,
    selectedRowKeys: selectedAnnouncement.map((item) => item.id!),
    onChange: onSelectChange,
  };

  return (
    <div>
      <Table<AnnouncementConfig>
        rowKey={(record) => record.id || ""}
        tableLayout={"auto"}
        columns={columns}
        dataSource={announcements}
        rowSelection={rowSelection}
        onChange={onTableChange}
        pagination={{
          current: page,
          total: Number(total),
          onChange(page) {
            setPage(page);
          },
        }}
      />
    </div>
  );
}
