import { useAppCode } from "@/hooks";
import {
  SidebarConfig,
  useCancelPublishSidebar,
} from "@/pages/SocialMedia/hooks";
import { Popconfirm, Typography } from "antd";

export function CancelPublish({
  sidebarConfig,
}: {
  sidebarConfig: SidebarConfig;
}) {
  const appcode = useAppCode();
  const cancelPublish = useCancelPublishSidebar();
  return (
    <Popconfirm
      title="是否确定要下线此 Sidebar ？"
      onConfirm={() =>
        cancelPublish({
          appcode,
          ids: [sidebarConfig?.id || ""],
        })
      }
    >
      <Typography.Link type="warning">下线</Typography.Link>
    </Popconfirm>
  );
}
