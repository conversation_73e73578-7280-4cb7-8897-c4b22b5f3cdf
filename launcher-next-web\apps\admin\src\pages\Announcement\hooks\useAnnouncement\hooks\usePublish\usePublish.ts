import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useAnnouncementKey } from "../../useAnnouncementKey";
import { PublishRequest } from "../../types";
import { useSWRConfig } from "swr";
import { useAppCode } from "@/hooks";

export function usePublish() {
  const key = useAnnouncementKey();
  const appCode = useAppCode();
  const { mutate } = useSWRConfig();
  const { trigger } = useSWRMutation(key, publish, {
    onSuccess() {
      mutate([URL_PREFIX + "/get_orders", appCode]);
    },
  });
  return trigger;
}

async function publish(_: any, { arg }: { arg: PublishRequest }) {
  await fetcher(`${URL_PREFIX}/publish`, arg);
}
