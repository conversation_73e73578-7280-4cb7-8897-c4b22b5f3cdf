import { useEffect, useRef, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import clsx from "clsx"
import bridge from "@/utils/bridge"
import { EllipsisText } from "@/components/EllipsisText"
import mask from "@/assets/mask.png"

interface InfoListProps {
    data: { url: string; title: string; date: string }[]
    fullRadius?: boolean
    onClick?: (url?: string) => void
}

export const InfoList = ({ data, fullRadius, onClick }: InfoListProps) => {
    const scrollRef = useRef<HTMLDivElement>(null)
    const [showShadow, setShowShadow] = useState(true)

    const handleScroll = () => {
        const el = scrollRef.current
        if (!el) return
        const { scrollTop, scrollHeight, clientHeight } = el
        const isBottom = scrollTop + clientHeight >= scrollHeight - 1
        setShowShadow(!isBottom)
    }

    useEffect(() => {
        const el = scrollRef.current
        if (!el) return
        handleScroll()
        el.addEventListener("scroll", handleScroll)
        return () => {
            el.removeEventListener("scroll", handleScroll)
        }
    }, [data])

    return (
        <div className="relative flex-1 overflow-auto">
            <div
                className={clsx(
                    "h-full px-[6px] py-[8px] bg-[rgb(24,23,26)]/[0.78] backdrop-blur-[4px]",
                    fullRadius ? "rounded-b-[16px]" : "rounded-br-[16px]"
                )}
                style={
                    {
                        // maskImage:"linear-gradient(to top, transparent, rgb(0,0,0) 30%)",
                    }
                }
            >
                <div ref={scrollRef} className="h-full px-[4px] overflow-auto">
                    {data
                        .filter((info) => info.title)
                        // .slice(0, 3)
                        .map((info, i) => (
                            <a
                                onClick={() => onClick?.(info.url)}
                                onDragStart={(e) => e.preventDefault()}
                                target="_blank"
                                key={info.title + i}
                                className={clsx(
                                    "flex justify-between items-center gap-[6px]",
                                    "px-[10px] py-[6px] rounded-[4px]",
                                    "text-white/[0.75] text-[13px] font-sans cursor-pointer",
                                    "hover:bg-white/10 transition-colors duration-300"
                                )}
                            >
                                <div className="flex-1 h-[18px] overflow-hidden">
                                    <EllipsisText text={info.title} />
                                </div>
                                <p className="text-white/[0.35] font-[350] text-[12px] min-w-[75px] text-right">
                                    {info.date}
                                </p>
                            </a>
                        ))}
                </div>
            </div>
            <AnimatePresence>
                {showShadow && (
                    <motion.div
                        className="absolute bottom-0 left-0 w-full h-[40px] pointer-events-none"
                        initial={{ opacity: 1 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        style={{
                            background:
                                "linear-gradient(to top, rgba(24, 23, 26, 0.78) 20%, rgba(24, 23, 26, 0.78) 50%, transparent)",
                        }}
                    ></motion.div>
                )}
            </AnimatePresence>
        </div>
    )
}
