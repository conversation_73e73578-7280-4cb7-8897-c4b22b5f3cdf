import { Badge } from "antd";
import { BannerState } from "../../../hooks/useBanner/types";

export function stateRender(value: BannerState) {
  if (value === BannerState.Unpublished) {
    return <Badge text="未发布" color="pink" />;
  }
  if (value === BannerState.Timing) {
    return <Badge text="定时中" color="orange" />;
  }
  if (value === BannerState.Published) {
    return <Badge text="已发布" color="green" />;
  }
  if (value === BannerState.AutoOffLine) {
    return <Badge text="自动下线" color="red" />;
  }
  if (value === BannerState.ManualOffLine) {
    return <Badge text="手动下线" color="red" />;
  }
  return <div>{value}</div>;
}
