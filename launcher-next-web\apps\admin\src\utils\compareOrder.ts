export const compareOrder = (
  curData: Record<string, any>,
  nextData: Record<string, any>,
) => {
  const curKeys = Object.keys(curData);
  const nextKeys = Object.keys(nextData);

  const diffKey: any[] = [];
  // 新增的tab key
  nextKeys?.forEach((key) => {
    if (curKeys.indexOf(key) < 0 && nextData[key]?.length) {
      console.log(">>>addKey", key);
      diffKey.push(key);
    }
  });

  curKeys?.forEach((key) => {
    const curOrder = curData[key]?.map((ele: any) => ele.id);
    const nextOrder = nextData[key]?.map((ele: any) => ele.id);
    // 已有的tab key，新增数据
    if (curOrder.length !== nextOrder.length) {
      console.log(">>>length不相等", key);
      diffKey.push(key);
    } else {
      // 已有的tab key，变更数据顺序
      const res = nextOrder.every(
        (value: any, index: number) => value === curOrder[index],
      );
      if (!res) {
        console.log(">>>顺序不一致", key);
        diffKey.push(key);
      }
    }
  });

  const diffKeyRes = [...new Set(diffKey)];
  console.log("diffKeyRes", diffKeyRes);
  return diffKeyRes;
};
