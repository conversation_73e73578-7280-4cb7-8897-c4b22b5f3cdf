@backgroundColor: rgba(67, 67, 67, 0.4);

.preview-container {
  margin-top: 10px;
  // overflow: "hidden";
  display: flex;
  flex-direction: column;
  width: 560px;
  min-height: 400px;

  .highlight-color {
    color: rgb(57, 148, 255, 0.75);
  }

  .sidebar {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 3.75rem;
    padding: 10px 0;
    border-radius: 10px;
    background-color: @backgroundColor;
  }

  .sidebar-item {
    position: relative;
    display: flex;
    justify-content: center;
    width: 100%;
    margin-bottom: 10px;
  }

  .icon-item {
    width: 2.5rem;
    height: 2.5rem;
    color: rgb(214, 214, 214);
    cursor: pointer;
    transition: color 0.15s ease-in-out;
    &:hover {
      .highlight-color;
    }
    &.highlight-status {
      .highlight-color;
    }
  }
  .fade-enter {
    opacity: 0;
    transform: translateX(-10px);
  }

  .fade-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 300ms ease-in-out;
  }

  .fade-exit {
    opacity: 1;
    transform: translateX(0);
  }

  .fade-exit-active {
    opacity: 0;
    transform: translateX(-10px);
    transition: all 300ms ease-in-out;
  }
  // .fade {
  //   transition: all 0.3s ease-in-out;
  //   // transform: translateX(-10px);
  //   // opacity: 0;
  // }
  // .fade-in {
  //   opacity: 1;
  //   transform: translateX(10px);
  // }
  // .fade-out {
  //   opacity: 0;
  //   transform: translateX(0px);
  // }

  .sub-container {
    position: absolute;
    left: 100%;
    padding-left: 10px;
  }

  .sub-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 8rem;
    padding: 0.75rem;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.4);
  }

  .img-container {
    padding: 5px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    background-color: #ffffff;
    // align-items: center;
    // width: 100%;
    // height: 100%;
    .img {
      width: 100%;
    }
    .img-description {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .label-container {
    .label-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.75rem;
      color: #ffffff;
      line-height: 1.25rem;
      cursor: pointer;
      transition: color 0.15s ease-in-out;
      text-indent: 5px;
      &:hover {
        .highlight-color;
      }
    }
  }
}
