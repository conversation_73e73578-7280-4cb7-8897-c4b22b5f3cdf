import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useSidebarKey } from "../useSidebarKey";
import { RemoveSidebarRequest } from "../types";

export function useRemoveSidebar() {
  const key = useSidebarKey();
  const { trigger } = useSWRMutation(key, remove);
  return trigger;
}

async function remove(_: any, { arg }: { arg: RemoveSidebarRequest }) {
  await fetcher(`${URL_PREFIX}/delete`, arg);
}
