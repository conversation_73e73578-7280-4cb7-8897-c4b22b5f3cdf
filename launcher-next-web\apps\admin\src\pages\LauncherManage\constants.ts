import { ReleaseState, AppEnvType } from "./types";
// import { PresetStatusColorType } from "antd/es/_util/colors";

export const ReleaseStateConfig: {
  [key in ReleaseState]?: {
    text?: string;
    color?: string;
    // status?: PresetStatusColorType;
  };
} = {
  [ReleaseState.ReleaseState_INNER]: {
    text: "内部访问",
    color: "default",
  },
  [ReleaseState.ReleaseState_RELEASE]: {
    text: "外部访问",
    color: "processing",
  },
  [ReleaseState.ReleaseState_RESERVE]: {
    text: "-",
    color: "default",
  },
};

export const ReleaseStateOptions = [
  {
    label: "内部访问",
    value: ReleaseState.ReleaseState_INNER,
  },
  {
    label: "外部访问",
    value: ReleaseState.ReleaseState_RELEASE,
  },
];

export const AppEnvTypeMap: { [key in AppEnvType]: string } = {
  [AppEnvType.AppEnvType_RESERVE]: "",
  [AppEnvType.AppEnvType_QA]: "测试启动器",
  [AppEnvType.AppEnvType_PROD]: "正式启动器",
};

export const AppEnvTypeOptions: Array<{ label: string; value: AppEnvType }> = [
  {
    label: AppEnvTypeMap[AppEnvType.AppEnvType_QA],
    value: AppEnvType.AppEnvType_QA,
  },
  {
    label: AppEnvTypeMap[AppEnvType.AppEnvType_PROD],
    value: AppEnvType.AppEnvType_PROD,
  },
];
