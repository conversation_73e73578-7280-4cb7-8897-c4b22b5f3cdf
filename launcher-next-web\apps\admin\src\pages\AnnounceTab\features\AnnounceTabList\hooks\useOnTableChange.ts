import {
  BannerConfig,
  Sort,
  useSetSort,
  useSetFilter,
} from "@/pages/Banner/hooks";
import { TableProps } from "antd";
import { SorterResult } from "antd/es/table/interface";
import { isArray, isEmpty } from "lodash";

export function useOnTableChange(): TableProps<BannerConfig>["onChange"] {
  const setSort = useSetSort();
  const setFilter = useSetFilter();

  return (pagination, filters, sorter, extra) => {
    console.log(pagination, filters, sorter, extra);
    if (!isEmpty(sorter)) {
      const sortData = isArray(sorter)
        ? sorter.map(transSort)
        : [transSort(sorter)];

      setSort(sortData.filter(Boolean) as Sort[]);
    }
    if (!isEmpty(filters)) {
      setFilter(filters);
    }
  };
}

function transSort(sorter: SorterResult<BannerConfig>): Sort | undefined {
  if (sorter.order === undefined) {
    return;
  }
  return {
    field: isArray(sorter.field) ? sorter.field[0] : sorter.field,
    order: sorter.order === "ascend" ? "asc" : "desc",
  };
}
