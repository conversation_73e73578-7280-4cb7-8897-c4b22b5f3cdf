import { configure } from "@hg-omc/bootstrap-app";
import config from "../../config/config.admin.json";
const deps = require("./package.json").dependencies;
export const omcConfig: Parameters<typeof configure>[0] = {
  key: "launcher_next",
  name: "聚合启动器后台",
  dev: {
    host: "127.0.0.1",
    port: 8005,
    client: {
      overlay: {
        runtimeErrors: false,
      },
    },
  },
  // auth: false,
  auth: {
    prefix: "/hg_launcher_omcv2",
  },
  server: {
    host: config.server,
    prefix: "/api",
  },
  shared: {
    "@ant-design/icons": {
      singleton: true,
      requiredVersion: deps["@ant-design/icons"],
    },
  },
  defines: {
    __SERVER_URL: config.server,
  },
  pages: {
    // layout: "@/layouts/MainLayout",
    main: {
      displayName: "系统",
      children: [
        { path: "/", redirect: "/launcher-manage", hideInMenu: true },
        {
          path: "/launcher-manage",
          component: "@/pages/LauncherManage",
          access: ["launcher_modify"],
          hideInMenu: true,
        },
        {
          path: "/package-manage",
          name: "包体管理",
          component: "@/pages/PackageManage",
          access: ["launcher_channel_modify"],
          hideInMenu: true,
        },
      ],
    },
    topic: {
      children: [
        // { path: "/", redirect: "/setting/main", hideInMenu: true },
        {
          path: "/base-setting",
          name: "基础信息",
          access: ["launcher_setting_modify"],
          component: "@/pages/BaseSetting",
        },
        {
          path: "/client-setting",
          name: "C端配置",
          children: [
            {
              path: "/client-setting/main",
              name: "主界面",
              access: ["main_page_setting_modify"],
              component: "@/pages/MainSetting",
            },
            {
              path: "/client-setting/banner",
              name: "Banner",
              access: ["banner_modify"],
              component: "@/pages/Banner",
            },
            {
              path: "/client-setting/announcement-list",
              name: "公告列表",
              access: ["announcement_modify"],
              component: "@/pages/Announcement",
            },
            {
              path: "/client-setting/announcement-tab",
              name: "公告页签",
              access: ["announcement_tab_modify"],
              component: "@/pages/AnnounceTab",
            },
            {
              path: "/client-setting/socialMedia",
              name: "社媒",
              access: ["sidebar_modify"],
              component: "@/pages/SocialMedia",
            },
          ],
        },
      ],
    },
  },
};

export default configure(omcConfig);
