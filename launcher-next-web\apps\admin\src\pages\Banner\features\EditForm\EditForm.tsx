import {
  DrawerForm,
  ProForm,
  ProFormDateTimePicker,
  ProFormDateTimeRangePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from "@ant-design/pro-components";
import { Form, message } from "antd";
import {
  BannerConfig,
  BannerState,
  TimerKind,
} from "../../hooks/useBanner/types";
import { ProFormOssUpload } from "@/features";
import { utcList } from "@/data/utc";
import { getDateByTsForTimePick, getUtcTs } from "@/utils/time";
import { useLangList } from "@/hooks";
import "./index.less";

type ValueWithTs = Values & { start_ts?: string; end_ts?: string };

interface Props {
  title: React.ReactNode;
  trigger: JSX.Element;
  onFinish: (values: ValueWithTs) => Promise<void>;
  initialValues?: BannerConfig;
  disableModify?: boolean;
}

interface Values {
  description: string;
  data: BannerConfig["data"];
  timer_kind: TimerKind;
  utc: number;
  startTime: string;
  onlineTime: [string, string];
}

export function EditForm({
  trigger,
  title,
  onFinish,
  initialValues,
  disableModify,
}: Props) {
  const [form] = Form.useForm<{ name: string; company: string }>();
  const langList = useLangList();
  return (
    <DrawerForm<Values>
      className="launcher-editForm"
      layout={"horizontal"}
      labelAlign={"left"}
      labelCol={{ flex: "110px", offset: 1 }}
      title={title}
      resize={{
        maxWidth: window.innerWidth * 0.8,
        minWidth: 650,
      }}
      form={form}
      trigger={trigger}
      drawerProps={{
        destroyOnClose: true,
      }}
      initialValues={{
        description: initialValues?.description,
        languages: initialValues?.languages,
        data: initialValues?.data,
        timer_kind: initialValues?.timer_kind || TimerKind.ShortTime,
        utc: 8,
        startTime: initialValues
          ? getDateByTsForTimePick(initialValues?.start_ts)
          : undefined,
        onlineTime: initialValues
          ? [
              getDateByTsForTimePick(initialValues?.start_ts),
              getDateByTsForTimePick(initialValues?.end_ts),
            ]
          : undefined,
      }}
      onFinish={async (values) => {
        console.log(values);
        const bannerConfig: ValueWithTs = {
          ...values,
        };
        if (values.timer_kind === TimerKind.LongTime) {
          bannerConfig.start_ts = String(
            getUtcTs({
              time: values.startTime,
              utc: values.utc,
            }),
          );
        }
        if (values.timer_kind === TimerKind.ShortTime) {
          bannerConfig.start_ts = String(
            getUtcTs({
              time: values.onlineTime[0],
              utc: values.utc,
            }),
          );
          bannerConfig.end_ts = String(
            getUtcTs({
              time: values.onlineTime[1],
              utc: values.utc,
            }),
          );
        }
        try {
          await onFinish(bannerConfig);
          message.success("提交成功");
          return true;
        } catch (error) {
          console.error(error);
          message.success("提交失败");
        }
        // 不返回不会关闭弹框
        // return true;
      }}
    >
      <h3>属性设置</h3>
      <ProFormText
        name="description"
        // width="md"
        label="描述"
        // tooltip="最长为 24 位"
        placeholder="请输入名称"
        rules={[{ required: true, max: 200 }]}
      />
      <ProFormSelect
        label="应用语种"
        name="languages"
        disabled={disableModify}
        mode="multiple"
        options={langList}
        style={{ width: "100%" }}
        width={"sm"}
        placeholder="请选择应用语种"
        rules={[{ required: true }]}
      />

      <h3>内容设置</h3>
      <ProFormOssUpload
        rules={[
          {
            required: true,
            message: "请上传 Banner 图",
          },
        ]}
        tooltip="图片尺寸要求"
        fieldProps={{
          md5: true,
          limit: { aspectRatio: { width: 16, height: 9 }, size: 1 },
          description: "请保持图片分辨率16:9 最小为(280:158)，大小不超过1M",
        }}
        name="data"
        label="Banner 图"
      />
      <ProFormText
        name={["data", "jump_url"]}
        label="跳转链接"
        rules={[{ required: false }, { type: "url" }]}
      />

      <h3>有效期设置</h3>
      <ProFormRadio.Group
        name="timer_kind"
        label="在线时长"
        rules={[{ required: true }]}
        disabled={disableModify}
        fieldProps={{
          options: [
            {
              label: "定时下线",
              value: TimerKind.ShortTime,
            },
            {
              label: "长期在线",
              value: TimerKind.LongTime,
            },
          ],
        }}
      />
      <ProFormSelect
        label="时区选择"
        name="utc"
        options={utcList}
        disabled
        width={"sm"}
        rules={[{ required: true }]}
      />
      <ProFormDependency name={["timer_kind"]}>
        {({ timer_kind }) => {
          if (timer_kind === TimerKind.LongTime) {
            return (
              <ProFormDateTimePicker
                label="在线时间"
                name="startTime"
                width={"md"}
                disabled={disableModify}
                rules={[{ required: true }]}
              />
            );
          }
          return (
            <ProFormDateTimeRangePicker
              label="在线时间"
              name="onlineTime"
              width={"xl"}
              disabled={disableModify}
              rules={[{ required: true }]}
            />
          );
        }}
      </ProFormDependency>
    </DrawerForm>
  );
}
