export function getMajorMinor(version: string) {
  return version.split(".").slice(0, 2).join(".");
}

export function gtVersion(version1: string, version2: string) {
  const version1List = version1.split(".").map(Number);
  const version2List = version2.split(".").map(Number);

  for (let i = 0; i < Math.max(version1List.length, version2List.length); i++) {
    const version1Item = version1List[i] || 0;
    const version2Item = version2List[i] || 0;
    if (version1Item > version2Item) return 1;
    else if (version1Item < version2Item) return -1;
  }
  return 0;
}
