const designWidth = 1920
const designHeight = 1080

let _fontSize = 16

export function handleResize(width: number, height: number, baseFontSize = 16) {
    let fontSize = baseFontSize

    if (width / height > designWidth / designHeight) {
        fontSize *= height / designHeight
    } else {
        fontSize *= width / designWidth
    }

    // set root font size
    const html = document.querySelector("html") as HTMLElement
    html.style.fontSize = `${fontSize}px`
    _fontSize = fontSize

    if (window.navigator.userAgent.includes("HarmonyOS")) {
        return
    }

    // font size should be scaled if system font setting is enabled
    const realFontSize = Number(
        window.getComputedStyle(html)["font-size"].replace("px", "")
    )
    if (realFontSize !== fontSize) {
        html.style.fontSize = `${(fontSize * fontSize) / realFontSize}px`
        _fontSize = (fontSize * fontSize) / realFontSize
    }
}

export function getFontSize() {
    return _fontSize
}
