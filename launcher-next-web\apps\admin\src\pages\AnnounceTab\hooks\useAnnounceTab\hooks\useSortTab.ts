import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnounceTabKey } from "../useAnnounceTabKey";
import { RemoveBannerRequest } from "../types";

export function useSortTab() {
  const key = useAnnounceTabKey();
  const { trigger } = useSWRMutation(key, sort);
  return trigger;
}

async function sort(_: any, { arg }: { arg: RemoveBannerRequest }) {
  await fetcher(`${URL_PREFIX}/update`, arg);
}
