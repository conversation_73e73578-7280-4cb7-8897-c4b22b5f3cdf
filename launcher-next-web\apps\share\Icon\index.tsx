import React, { Suspense } from "react";

const IconMap: Record<
  string,
  () => Promise<{ default: React.ComponentType<any> }>
> = {
  Bilibili: () => import("./svgs/Bilibili"),
  CS: () => import("./svgs/CS"),
  Douyin: () => import("./svgs/Douyin"),
  Skland: () => import("./svgs/Skland"),
  SklandTool: () => import("./svgs/SklandTool"),
  Taptap: () => import("./svgs/Taptap"),
  Wechat: () => import("./svgs/Wechat"),
  Weibo: () => import("./svgs/Weibo"),
  Xiaohongshu: () => import("./svgs/Xiaohongshu"),

  CS_gl: () => import("./svgs/CS_gl"),
  Discord: () => import("./svgs/Discord"),
  Facebook: () => import("./svgs/Facebook"),
  Instagram: () => import("./svgs/Instagram"),
  Line: () => import("./svgs/Line"),
  NAVER_CAFE: () => import("./svgs/NAVER_CAFE"),
  NAVER_GAME: () => import("./svgs/NAVER_GAME"),
  Reddit: () => import("./svgs/Reddit"),
  TikTok: () => import("./svgs/TikTok"),
  Twitch: () => import("./svgs/Twitch"),
  VK: () => import("./svgs/VK"),
  X: () => import("./svgs/X"),
  YouTube: () => import("./svgs/YouTube"),
};

interface IconProps extends React.SVGProps<SVGSVGElement> {
  name: string;
  style?: React.CSSProperties;
}

export const Icon: React.FC<IconProps> = React.memo((props) => {
  const { name, style, ...svgProps } = props;
  const IconComponent = IconMap[name] ? React.lazy(IconMap[name]) : null;

  if (!IconComponent) return null;

  const wrapStyle = style || { width: "30px", height: "30px" };

  return (
    // 防抖动处理：在组件懒加载阶段，没有元素table行会塌缩，导致视觉上有抖动
    <Suspense fallback={<div style={wrapStyle}></div>}>
      <div style={style}>
        <IconComponent {...svgProps}></IconComponent>
      </div>
    </Suspense>
  );
});
Icon.displayName = "Icon";
