import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tabs, message } from "antd";
import { <PERSON><PERSON>lock, Page } from "@hg-omc/layouts";
import { useCallback, useEffect, useRef } from "react";
import { Actions, Content, Header } from "./features";
import { useCurrentSource, useMainData } from "./hooks";
import { DataSource } from "./types";
import { Provider } from "jotai";

export function MainContent() {
  const contentRef = useRef<{ submit: () => void }>(null);
  const [currentSource, setCurrentSource] = useCurrentSource();
  const handleSave = useCallback(() => {
    contentRef.current?.submit();
  }, []);
  return (
    <Page>
      <Card title={<Header />} styles={{ body: { paddingTop: 10 } }}>
        <Tabs
          // tabPosition="left"
          tabBarExtraContent={<Actions emitSave={handleSave} />}
          activeKey={currentSource}
          onChange={(activeKey) => setCurrentSource(activeKey as DataSource)}
          items={[
            { label: "线上", key: DataSource.Online },
            { label: "草稿", key: DataSource.Draft },
          ]}
        />
        <Content ref={contentRef} />
      </Card>
    </Page>
  );
}

export function Main() {
  return (
    <Provider>
      <MainContent />
    </Provider>
  );
}
