import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnouncementKey } from "../useAnnouncementKey";
import { UpdateRequest } from "../types";

export function useUpdate() {
  const key = useAnnouncementKey();
  const { trigger } = useSWRMutation(key, update);
  return trigger;
}

async function update(_: any, { arg }: { arg: UpdateRequest }) {
  await fetcher(`${URL_PREFIX}/update`, arg);
}
