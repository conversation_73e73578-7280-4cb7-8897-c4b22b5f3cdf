import { fetcher } from "@/utils/fetcher";

/**
 * main_pageGetOssStsRsp
 */
export interface UploadDataResponse {
  body?: MainPagePostBody;
  download_url?: string;
  expire?: string;
  method?: string;
  upload_domain?: string;
}

/**
 * main_pagePostBody
 */
export interface MainPagePostBody {
  key?: string;
  oss_access_key_id?: string;
  policy?: string;
  signature?: string;
  success_action_status?: string;
}

export async function getUploadData(body: {
  appcode: string;
  file_ext: string;
}) {
  const res = await fetcher<UploadDataResponse>(
    "/admin/launcher/config/oss/get_sts",
    body,
  );
  return res;
}
