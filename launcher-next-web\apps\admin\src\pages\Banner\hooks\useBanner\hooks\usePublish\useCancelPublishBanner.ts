import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useBannerKey } from "../../useBannerKey";
import { ExpireBannerRequest, PublishRequest } from "../../types";
import { useSWRConfig } from "swr";
import { useAppCode } from "@/hooks";

export function useCancelPublishBanner() {
  const key = useBannerKey();
  const appCode = useAppCode();
  const { mutate } = useSWRConfig();
  const { trigger } = useSWRMutation(key, cancelPublishBanner, {
    onSuccess() {
      mutate([URL_PREFIX + "/get_orders", appCode]);
    },
  });
  return trigger;
}

async function cancelPublishBanner(
  _: any,
  { arg }: { arg: ExpireBannerRequest },
) {
  await fetcher(`${URL_PREFIX}/expire`, arg);
}
