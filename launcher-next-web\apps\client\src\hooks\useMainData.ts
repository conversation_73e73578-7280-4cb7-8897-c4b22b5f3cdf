import { useCallback, useEffect, useRef } from "react"
import useS<PERSON>, { preload } from "swr"
import { fetcher } from "@/utils/fetcher"
import { Game } from "@/types/games"
import { WebBatchProxyRsp, WebProxyRsp } from "@/types/mainData"
import { getAppCode } from "@/utils/misc"
import { useLanguageValue } from "@/store/useLanguage"
import bridge from "@/utils/bridge"
import { Storage } from "@/utils/storage"
import { imagePreload } from "@/utils/imagePreload"

const needsUpdateStorage = (
    cache: WebBatchProxyRsp | null,
    response: WebBatchProxyRsp
) => {
    if (!cache) return true
    return response?.proxy_rsps?.some((webProxyRsp) => {
        const kind = webProxyRsp.kind
        const cacheItem = cache.proxy_rsps.find(
            (item) => item.kind === kind
        ) as any
        if (!cacheItem) return true
        if (kind === "get_hg_sidebar") {
            return (
                cacheItem.get_hg_sidebar_rsp?.data_version !==
                webProxyRsp.get_hg_sidebar_rsp.data_version
            )
        }
        if (kind === "get_banner") {
            return (
                cacheItem.get_banner_rsp?.data_version !==
                webProxyRsp.get_banner_rsp.data_version
            )
        }
        if (kind === "get_announcement") {
            return (
                cacheItem.get_announcement_rsp?.data_version !==
                webProxyRsp.get_announcement_rsp.data_version
            )
        }
        if (kind === "get_main_bg_image") {
            return (
                cacheItem.get_main_bg_image_rsp?.data_version !==
                webProxyRsp.get_main_bg_image_rsp.data_version
            )
        }
        if (kind === "get_single_ent") {
            return false
        }
        return true
    })
}

const genStorageKey = (language: string, appcode: string) => {
    return `main_${language}+${appcode}`
}

const url = "/api/proxy/web/batch_proxy"
const getParams = (params: {
    appcode: string
    language: string
    channel?: string
    sub_channel?: string
    platform?: string
}) => {
    return {
        proxy_reqs: [
            {
                kind: "get_hg_sidebar",
                get_hg_sidebar_req: params,
            },
            {
                kind: "get_single_ent",
                get_single_ent_req: params,
            },
            {
                kind: "get_main_bg_image",
                get_main_bg_image_req: params,
            },
            {
                kind: "get_banner",
                get_banner_req: params,
            },
            {
                kind: "get_announcement",
                get_announcement_req: params,
            },
        ],
    }
}

export function useMainData(params: {
    appcode: string
    language: string
    channel?: string
    sub_channel?: string
    platform?: string
}) {
    const { lang: language } = useLanguageValue()
    const storageKey = genStorageKey(language, params.appcode)
    const storageData: WebBatchProxyRsp | null = Storage.getItem(storageKey)

    const key = [url, storageKey]

    const reqParams = getParams(params)

    const { data, isLoading } = useSWR(
        key,
        ([url]) => {
            return fetcher<WebBatchProxyRsp>(url, reqParams, {
                method: "POST",
            })
        },
        {
            onSuccess: (data) => {
                bridge.eventLog("launcher", "launcher_server_aggregation", {
                    appcode: params.appcode,
                    err_code: "",
                })
                if (needsUpdateStorage(storageData, data)) {
                    console.log("======set storage======", key)
                    Storage.setItem(storageKey, data)
                }
            },
            onError: (err) => {
                console.log(">>>useMainData error", err)
                bridge.eventLog("launcher", "launcher_server_aggregation", {
                    appcode: params.appcode,
                    err_code: err.code,
                    err_message: err.message || "http error",
                })
            },
            fallbackData: storageData || undefined,
        }
    )

    const get_hg_sidebar_rsp = data?.proxy_rsps?.find(
        (item) => item.kind === "get_hg_sidebar"
    )?.get_hg_sidebar_rsp
    const get_banner_rsp = data?.proxy_rsps?.find(
        (item) => item.kind === "get_banner"
    )?.get_banner_rsp
    const get_announcement_rsp = data?.proxy_rsps?.find(
        (item) => item.kind === "get_announcement"
    )?.get_announcement_rsp
    const get_main_bg_image_rsp = data?.proxy_rsps?.find(
        (item) => item.kind === "get_main_bg_image"
    )?.get_main_bg_image_rsp
    const get_single_ent_rsp = data?.proxy_rsps?.find(
        (item) => item.kind === "get_single_ent"
    )?.get_single_ent_rsp

    return {
        isLoading,
        data,
        get_hg_sidebar_rsp,
        get_banner_rsp,
        get_announcement_rsp,
        get_main_bg_image_rsp,
        get_single_ent_rsp,
    }
}

let prefetchTimeout: NodeJS.Timeout | null = null
// 已预加载的游戏ID，避免重复预加载
const prefetchedGames = new Set<string>()

export const usePrefetchOnHover = (hoverDelay: number) => {
    const currentGameId = useRef<string | null>(null)
    const { lang: language } = useLanguageValue()

    const startPrefetch = useCallback((game: Game) => {
        currentGameId.current = game.uuid
        if (prefetchedGames.has(game.uuid)) return
        if (prefetchTimeout) {
            clearTimeout(prefetchTimeout)
        }

        prefetchTimeout = setTimeout(() => {
            if (currentGameId.current !== game.uuid) return
            console.log("prefetch", game.uuid)
            try {
                const appCode = game.app_code

                const storageKey = genStorageKey(language, appCode)
                const key = [url, storageKey]
                const params = {
                    appcode: appCode,
                    language,
                    channel: "1",
                    sub_channel: "1",
                    platform: "Windows",
                }
                const reqParams = getParams(params)
                preload(key, async () => {
                    try {
                        const res = await fetcher<WebBatchProxyRsp>(
                            url,
                            reqParams,
                            {
                                method: "POST",
                            }
                        )
                        const backgroundImage = res?.proxy_rsps?.find(
                            (item) => item.kind === "get_main_bg_image"
                        )?.get_main_bg_image_rsp?.main_bg_image?.url
                        const firstBannerImage = res?.proxy_rsps?.find(
                            (item) => item.kind === "get_banner"
                        )?.get_banner_rsp?.banners?.[0]?.url
                        const singleEntImage = res?.proxy_rsps?.find(
                            (item) => item.kind === "get_single_ent"
                        )?.get_single_ent_rsp?.single_ent
                        const singleEntVersionImage =
                            singleEntImage?.version_url
                        const singleEntButtonImage = singleEntImage?.button_url
                        const preloadImages = [
                            backgroundImage,
                            firstBannerImage,
                            singleEntVersionImage,
                            singleEntButtonImage,
                        ]
                        imagePreload(preloadImages)
                        bridge.eventLog(
                            "launcher",
                            "launcher_server_aggregation",
                            {
                                appcode: params.appcode,
                                err_code: "",
                            }
                        )
                        return res
                    } catch (err: any) {
                        console.error("fetch error", err)
                        bridge.eventLog(
                            "launcher",
                            "launcher_server_aggregation",
                            {
                                appcode: params.appcode,
                                err_code: err.code,
                                err_message: err.message || "http error",
                            }
                        )
                    }
                })
                prefetchedGames.add(game.uuid)
            } catch (error) {
                console.error("preload error", error)
            }
        }, hoverDelay)
    }, [])

    const cancelPrefetch = useCallback(() => {
        currentGameId.current = null
        if (prefetchTimeout) {
            clearTimeout(prefetchTimeout)
        }
    }, [])

    return {
        startPrefetch,
        cancelPrefetch,
    }
}
