import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { BannerConfig } from "../hooks/useBanner/types";

export const batchSelectAtom = atom<BannerConfig[]>([]);

export function useBatchSelect() {
  return useAtom(batchSelectAtom);
}

export function useBatchSelectValue() {
  return useAtomValue(batchSelectAtom);
}

export function useSetBatchSelect() {
  return useSetAtom(batchSelectAtom);
}
