import { Flex, Modal, Select, Space, message, Tabs, Typography } from "antd";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { cloneDeep } from "lodash";
import { Preview, Sort } from "./features";
import {
  usePublishedSidebar,
  usePublishSidebar,
  type SidebarConfig,
  SidebarConfigOrder,
  SidebarState,
} from "../../hooks/useSidebar";
import { useSetBatchSelect } from "../../hooks/useBatchSelect";
import { Language } from "@/data";
import { useAppCode, useLangList, useLangListMap } from "@/hooks";
import { compareOrder } from "@/utils/compareOrder";

interface Props {
  trigger: JSX.Element;
  sidebarsToPublish?: SidebarConfig[];
}

// 根据选中发布的bananer锚定到对应的语言
function getInitalLanguage(sidebarsToPublish: SidebarConfig[]) {
  return (sidebarsToPublish?.[0]?.languages?.[0] as Language) || Language.ZhCN;
}

export function Publish({ trigger, sidebarsToPublish }: Props) {
  const appcode = useAppCode();
  const [isOpen, setIsOpen] = useState(false);
  const [language, setLanguage] = useState(() =>
    getInitalLanguage(sidebarsToPublish || []),
  );
  const curOrderRef = useRef<any>();
  // const publishSidebarsRef = useRef<Record<string, SidebarConfig[]>>({});

  const [allSidebars, setAllSidebars] =
    useState<Record<Language, SidebarConfig[]>>();

  const { orders, mutate, isLoading } = usePublishedSidebar();
  const publishSidebar = usePublishSidebar();
  const langList = useLangList();
  const langTabsList = langList.map((item) => ({ ...item, key: item.value }));
  const langListMap = useLangListMap();
  const setBatchSelect = useSetBatchSelect();

  const getAllSidebars = useCallback(
    (data: SidebarConfigOrder[]) => {
      console.log(">>>getAllSidebars data", data);
      const sidebarsLangMap: Record<string, SidebarConfig[]> = {};

      data?.forEach(
        (order) =>
          (sidebarsLangMap[order.language!] = order?.Sidebar_configs || []),
      );
      curOrderRef.current = cloneDeep(sidebarsLangMap);
      if (!sidebarsToPublish) {
        return sidebarsLangMap;
      }

      sidebarsToPublish
        .filter((item) => {
          return item?.state === SidebarState.Unpublished;
        })
        .forEach((sidebar) => {
          sidebar.languages?.forEach((lang) => {
            // publishSidebarsRef.current[lang] = [
            //   sidebar,
            //   ...(publishSidebarsRef.current[lang] || []),
            // ];
            sidebarsLangMap[lang] = [sidebar, ...(sidebarsLangMap[lang] || [])];
          });
        });
      return sidebarsLangMap;
    },
    [sidebarsToPublish],
  );

  const handleLanguageChange = useCallback(
    (value: Language) => {
      setLanguage(value);
    },
    [orders],
  );

  const isButton = !!trigger.type.__ANT_BUTTON;
  return (
    <div>
      {React.cloneElement(trigger, {
        onClick: () => {
          // publishSidebarsRef.current = {};
          setAllSidebars(getAllSidebars(orders || []));
          setIsOpen(true);
        },
        ...(isButton ? { loading: isLoading } : {}), // 为了解决其它trigger本地报错不识别loading属性
      })}
      <Modal
        width={1200}
        open={isOpen}
        destroyOnClose
        onCancel={() => setIsOpen(false)}
        onOk={async () => {
          try {
            const compareRes = compareOrder(curOrderRef.current, allSidebars!);
            const updateLangTextNode = compareRes.map((lang, index) => {
              const langText = langListMap[lang as Language];
              return (
                <Typography.Link key={lang}>
                  {langText}
                  {index < compareRes.length - 1 ? "、" : ""}
                </Typography.Link>
              );
            });

            Modal.confirm({
              title: "提示",
              content: updateLangTextNode.length ? (
                <Typography.Paragraph>
                  本次操作将涉及 {updateLangTextNode} 的内容变更，是否确认执行？
                </Typography.Paragraph>
              ) : (
                <span>本次操作无内容变更，是否确认执行？</span>
              ),
              onOk: async () => {
                const params = {
                  appcode: appcode,
                  orders: Object.keys(allSidebars!)?.map((key) => ({
                    language: key,
                    // publish_ids: publishSidebarsRef.current?.[key]?.map(
                    //   (ele) => ele.id!,
                    // ),
                    order_ids: allSidebars?.[key as Language]?.map(
                      (ele) => ele.id!,
                    ),
                  })),
                };
                try {
                  await publishSidebar(params);
                  message.success("执行成功");
                  setBatchSelect([]);
                  setIsOpen(false);
                } catch (e: any) {
                  message.error(e.message || "请求错误");
                  return Promise.reject();
                }
              },
              onCancel() {},
            });
          } catch (error) {
            console.error(error);
            message.error("发布失败");
          }
        }}
        okText="发布"
        cancelText="取消"
        title={
          <Space>
            设置排序
            {/* <Select
              style={{ width: 200 }}
              options={langList}
              value={language}
              onChange={handleLanguageChange}
            /> */}
          </Space>
        }
      >
        <Tabs
          items={langTabsList}
          activeKey={language}
          onChange={(val) => handleLanguageChange(val as Language)}
        ></Tabs>
        <Flex justify="space-around">
          <Sort
            sidebars={allSidebars?.[language] || []}
            setSidebars={(val) =>
              setAllSidebars((prev) => {
                const newData = cloneDeep(prev)!;
                newData[language] = val;
                return newData;
              })
            }
            language={language}
          />
          <Preview sidebars={allSidebars?.[language] || []} />
        </Flex>
      </Modal>
    </div>
  );
}
