import { message } from "antd";
import { request } from "@hg-omc/bootstrap-app";
const OMC_TOKEN_KEY = "_OMC_AUTH_TOKEN";

const HOST =
  __SERVER_URL ||
  "https://launcher-manager-biz-platform-launcher-dev.hypergryph.net";
const PREFIX = "";

interface ErrResp {
  code: number;
  msg?: string;
  reason: string;
  message?: string;
}

export async function fetcher<T>(
  path: string,
  data?: Record<string, any>,
  options?: RequestInit & {
    method?: "POST" | "GET";
    type?: "normal" | "permission";
    hideErrorMessage?: boolean;
    skipAxiosErrorHandler?: boolean;
    withoutToken?: boolean;
  },
) {
  try {
    const isGet =
      options?.method && ["GET", "HEAD"].includes(options.method.toUpperCase());
    if (isGet && data && Object.keys(data).length) {
      const queryString = new URLSearchParams(data).toString();
      path += `?${queryString}`;
    }
    const response = await fetch(HOST + PREFIX + path, {
      // common: {
      //   // appId: appId || undefined,
      //   // token: options?.withoutToken ? undefined : getToken(),
      // },
      method: options?.method || "POST",
      headers: {
        "content-type": "application/json",
        "x-hgomc-token": localStorage.getItem(OMC_TOKEN_KEY) || "",
      },
      body: isGet ? undefined : JSON.stringify(data),
    });
    const res = (await response.json()) as T | ErrResp;
    if (res && typeof res === "object" && "code" in res && res.code !== 0) {
      // message.error(res.msg);
      throw res;
    }
    return res as T;
  } catch (error: any) {
    console.error("fetcher error:", error);
    if (options?.skipAxiosErrorHandler || options?.hideErrorMessage) {
      throw error;
    }
    // if (isAxiosError(error)) {
    //   console.error(error);
    //   if (error.request) {
    //     const reason: string = (error.response?.data as any)?.reason || "";
    //     let errorMsg = "";
    //     if (reason) {
    //       errorMsg = reasonToText[reason] || reason;
    //     }
    //     if (errorMsg) {
    //       message.error(errorMsg);
    //     } else if (error.request.status === 401) {
    //       // 外层 errorHandler 处理，重新登录
    //     } else if (error.request.status === 403) {
    //       message.info("您无权限执行此操作");
    //     } else {
    //       message.error(error.response?.data?.message || "请求失败");
    //     }
    //   } else if (error.response) {
    //     message.error("服务器错误");
    //   } else {
    //     message.error("未知错误");
    //   }
    // }
    throw error;
  }
}
