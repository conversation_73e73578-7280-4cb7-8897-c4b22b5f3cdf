declare module "*.scss" {
    const styles: { [key: string]: string }
    export default styles
}

declare module "*.less" {
    const styles: { [key: string]: string }
    export default styles
}

declare module "*.png" {
    const image: string
    export default image
}

declare module "*.jpg" {
    const image: string
    export default image
}

declare module "*.jpeg" {
    const image: string
    export default image
}

declare module "*.mp4" {
    const video: string
    export default video
}

declare module "*.mp3" {
    const audio: string
    export default audio
}

declare module "*.wav" {
    const audio: string
    export default audio
}

declare module "*.ply" {
    const image: string
    export default image
}

declare module "*.fs" {
    const content: string
    export default content
}

declare module "*.vs" {
    const content: string
    export default content
}

declare module "*.eot" {
    const content: string
    export default content
}

declare module "*.ttf" {
    const content: string
    export default content
}

declare module "*.woff" {
    const content: string
    export default content
}

declare module "*.svg" {
    const content: string
    export default content
}

declare module "*.svg?url" {
    const content: string
    export default content
}

declare module "*.obj" {
    const content: string
    export default content
}

interface Window {
    jsbridge: any
    qt: any
    QWebChannel: any
    isDebug: boolean
}

declare const __REQUEST_HOST: string
declare const __SDK_HOST: (typeof import("../../../config/config.json"))["web"]["sdk"]["host"]
declare const __SDK_SRC: (typeof import("../../../config/config.json"))["web"]["sdk"]["src"]
declare const __SDK_DOMAIN: (typeof import("../../../config/config.json"))["web"]["sdk"]["domain"]
declare const __SDK_SUB_DOMAIN: (typeof import("../../../config/config.json"))["web"]["sdk"]["subDomain"]
declare const _SENTRY_DSN: (typeof import("../../../config/config.json"))["web"]["sentryDsn"]
declare const _SENTRY_RELEASE_NAME: string
declare const __SERVER_URL: string

declare const jsbridge: any
declare const qt: any
declare const QWebChannel: any
