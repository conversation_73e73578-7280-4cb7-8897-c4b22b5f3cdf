export interface SwitchGameResponse {
    cmd: string
    result: boolean
    cur_game?: Game
}

export interface SwitchGameNotifyResponse {
    from: string
    to: string
}

export interface GameListResponse {
    cmd: string
    is_single_game: boolean //是否为单游戏启动器
    items: Game[]
}

export interface MainBtnStateResponse {
    uuid: string
    state: MainBtnState
    text_normal: string
    text_hover: string
    tooltip: string
}

export enum MainBtnState {
    ContinueDownload = "continue_download", // 继续下载
    DownloadGame = "download_game", // 下载游戏
    Downloading = "downloading", // 正在下载
    CheckingDownloadZip = "checking_download_zip", // 校验下载zip文件
    CheckingGameComplete = "checking_game_complete", // 游戏完整性检查中
    Installing = "installing", // 安装中
    DownloadExcept = "download_except", // 下载异常
    DownloadPaused = "download_paused", // 已暂停
    QueuingDownload = "queuing_download", // 下载排队中
    StartGame = "start_game", // 开始游戏
    UpdateGame = "update_game", // 更新游戏
    GameServiceStopped = "game_service_stopped", // 游戏已下架（不可用）
    InGame = "in_game", // 游戏中
    NeedLogin = "need_login", // 请先登录
    NeedPurchase = "need_purchase", // 请先购买
}

export interface Game {
    game_name: string
    app_code: string
    channel: number
    sub_channel: number
    region: string
    uuid: string
    is_user_visible: boolean // 当前用户是否可见（暂时不用）
    is_stop_service: boolean // 是否下架（暂时不用）
    is_game?: boolean // 是否为游戏（全部游戏页使用），为false则为全部游戏页本身uuid
    theme: string // 游戏主题
    game_state: GameState // 游戏状态
    download_state?: DownloadState // 游戏下载状态
    show_404_page?: boolean // 是否显示404页面
}

export enum GameState {
    Download = "download", // 下载中
    Queue = "queue", // 排队中
    Installed = "installed", // 已安装
    Uninstalled = "uninstalled", // 未安装
    Pause = "pause", // 暂停
    Except = "except", // 异常
}

export interface DownloadState {
    step: DownloadStep
    progress: DownloadProgress
}

export enum DownloadStep {
    Download = "download", // 下载
    Check = "check", // 校验
    Decompress = "decompress", // 解压
    Copy = "copy", // 拷贝
    GameCompleteCheck = "game_complete_check", // 游戏完整性检查
}

export interface DownloadProgress {
    from: number
    to: number
    value: number
    speed: number // 单位字节
    remaining_time: number // 单位秒
}
