import { Badge } from "antd";
import { AnnouncementState } from "../../../hooks/useAnnouncement/types";

export function stateRender(value: AnnouncementState) {
  if (value === AnnouncementState.Unpublished) {
    return <Badge text="未发布" color="pink" />;
  }
  if (value === AnnouncementState.Timing) {
    return <Badge text="定时中" color="orange" />;
  }
  if (value === AnnouncementState.Published) {
    return <Badge text="已发布" color="green" />;
  }
  if (value === AnnouncementState.AutoOffLine) {
    return <Badge text="自动下线" color="red" />;
  }
  if (value === AnnouncementState.ManualOffLine) {
    return <Badge text="手动下线" color="red" />;
  }
  return <div>{value}</div>;
}
