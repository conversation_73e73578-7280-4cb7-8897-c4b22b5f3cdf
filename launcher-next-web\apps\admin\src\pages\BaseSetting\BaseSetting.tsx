import {
  Card,
  Space,
  Flex,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
  Modal,
  Typography,
} from "antd";
import { Provider } from "jotai";
import { Page } from "@hg-omc/layouts";
import { GameStatus, BaseInfo, LauncherSetting } from "./features";
import { useBaseSettingList } from "./hooks/useBaseSettingList";
import { AppConfig, LauncherConfig } from "./hooks/types";

export function BaseSetting() {
  const { data } = useBaseSettingList();
  const appConfig = data?.app || ({} as AppConfig);
  const launcherConfig = data?.launcher_config || ({} as LauncherConfig);

  return (
    <Provider>
      <Page>
        <Flex vertical gap={20}>
          <Card title="游戏状态">
            <GameStatus state={appConfig?.state}></GameStatus>
          </Card>
          <Card title="基础信息">
            <BaseInfo config={appConfig}></BaseInfo>
          </Card>

          <LauncherSetting config={launcherConfig}></LauncherSetting>
        </Flex>
      </Page>
    </Provider>
  );
}
