import React, { useState, useRef, useEffect, useCallback } from "react";
import { Modal, Card, message } from "antd";
import { ProForm, ProFormRadio } from "@ant-design/pro-components";
import type { ProFormInstance } from "@ant-design/pro-components";

import { isHG } from "@/utils/env";
import { useAppCode } from "@/hooks/useAppCode";
import { LauncherConfig } from "../../hooks/types";
import { SubmitterButton } from "./features/SubmitterButton";
import { useUpdateLauncherSetting } from "../../hooks/useUpdateLauncherSetting";

interface Props {
  config: LauncherConfig;
}

export const LauncherSetting: React.FC<Props> = ({
  config: launcherConfig,
}) => {
  const appCode = useAppCode();
  const { trigger } = useUpdateLauncherSetting();

  const [readonly, setReadonly] = useState(true);
  const formRef = useRef<ProFormInstance>();

  const handleFinish = useCallback(
    async (formData: LauncherConfig) => {
      const params = {
        launcher_config: {
          ...launcherConfig,
          ...formData,
        },
        appcode: appCode || "",
      };
      try {
        await trigger(params);
        setReadonly(true);
        message.success("执行成功");
      } catch (e: any) {
        message.error(e.message || "请求错误");
        return Promise.reject();
      }
    },
    [appCode, launcherConfig, trigger],
  );

  useEffect(() => {
    if (!readonly) return; // 编辑态时不变更数据
    formRef?.current?.setFieldsValue({
      ...launcherConfig,
      disable_launch_game:
        launcherConfig.disable_launch_game === undefined
          ? true
          : launcherConfig.disable_launch_game,
    });
  }, [readonly, launcherConfig]);

  return (
    <Card
      title="启动器配置"
      extra={
        <SubmitterButton
          readonly={readonly}
          onSubmit={() => {
            formRef.current?.submit?.();
          }}
          onReset={() => {
            setReadonly(true);
            formRef.current?.resetFields();
          }}
          onToggle={setReadonly}
        />
      }
    >
      <ProForm<LauncherConfig>
        formRef={formRef}
        layout="horizontal"
        readonly={readonly}
        // initialValues={initialValues}
        labelAlign="left"
        labelCol={{ span: 5 }}
        submitter={false}
        onFinish={handleFinish}
      >
        <ProFormRadio.Group
          label="启动器可否启动游戏"
          name="disable_launch_game"
          rules={[{ required: true }]}
          tooltip="下架后，在启动器前端主按钮会变为“已下架”状态"
          options={[
            { label: "否", value: true },
            {
              label: "是",
              value: false,
            },
          ]}
        />
      </ProForm>
    </Card>
  );
};
