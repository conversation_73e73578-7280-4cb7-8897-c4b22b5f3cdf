import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { GetLauncherReportRequest } from "./types";

export function usePrewarm() {
  const key = `${URL_PREFIX}/prewarm`;
  const { trigger, isMutating } = useSWRMutation(key, prewarm);
  return { trigger, isMutating };
}

async function prewarm(
  url: string,
  { arg }: { arg: GetLauncherReportRequest },
): Promise<any> {
  return fetcher(url, arg);
}
