import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M18.589 12.474C21.45 12.474 24 14.53 24 17.05c0 1.422-.953 2.68-2.231 3.63l.478 1.575-1.747-.946c-.637.158-1.277.316-1.911.316-3.031 0-5.419-2.05-5.42-4.575 0-2.52 2.389-4.575 5.42-4.575ZM12.37 7.746c3.132 0 5.877 1.887 6.428 4.426a5.675 5.675 0 0 0-.614-.037c-3.026 0-5.416 2.236-5.416 4.99 0 .457.072.9.197 1.32a7.519 7.519 0 0 1-.595.026c-.795 0-1.434-.161-2.231-.317l-2.228 1.105.638-1.896C6.955 16.26 6 14.838 6 13.107c0-3 2.87-5.361 6.371-5.361Zm4.461 7.252c-.317 0-.638.317-.638.631 0 .319.321.631.638.631.481 0 .797-.312.797-.63 0-.315-.316-.631-.797-.632Zm3.504 0c-.315 0-.633.317-.633.631 0 .319.318.631.633.631.478 0 .797-.312.797-.63 0-.315-.32-.631-.797-.632Zm-10.034-4.572c-.478 0-.96.313-.96.787 0 .473.482.79.96.79.477 0 .795-.317.795-.79 0-.474-.318-.787-.795-.787Zm4.458 0c-.478 0-.957.313-.957.787 0 .473.479.79.957.79.48 0 .798-.317.798-.79 0-.474-.319-.787-.798-.787Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={29.509}
        x={-1.5}
        y={2.121}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_52_976" />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_52_976"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
