import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnouncementKey } from "../useAnnouncementKey";
import { RemoveRequest } from "../types";

export function useRemove() {
  const key = useAnnouncementKey();
  const { trigger } = useSWRMutation(key, remove);
  return trigger;
}

async function remove(_: any, { arg }: { arg: RemoveRequest }) {
  await fetcher(`${URL_PREFIX}/delete`, arg);
}
