// 读取dist中zip包的名称和package.json中的version，然后调用接口发布到版本管理平台
const fs = require("fs")
const path = require("path")
const fetch = require("node-fetch")
const dayjs = require("dayjs")
const config = require("../../../../config/config.web.json")

const ROOT_DIR = path.resolve(__dirname, "../../")
const DIST_DIR = path.resolve(ROOT_DIR, "dist")
const PACKAGE_JSON_PATH = path.resolve(ROOT_DIR, "package.json")

const SECRET_KEY = config.publish["game-config-accessSecret"] // 游戏配置平台的密钥
const HOST = config.publish["game-config-host"] // 游戏配置平台的域名
const ENV = config.env === "prod" ? "prod" : "stable"
const appId = config.region === "hg" ? "103" : "1103"

async function publishPlatform() {
    if (!fs.existsSync(DIST_DIR)) {
        console.log("Dist directory does not exist. Skipping publish.")
        process.exit(1)
    }
    const version = require(PACKAGE_JSON_PATH).version
    const files = fs.readdirSync(DIST_DIR)
    const versionFile = files.find((file) => file.startsWith(`${version}_`))
    const zipFile = path.join(versionFile, "build.zip")

    // 检查zip文件是否存在
    if (!fs.existsSync(path.join(DIST_DIR, zipFile))) {
        console.log("Zip file does not exist. Skipping publish.")
        process.exit(1)
    }
    const cdn = `${config.publish.cdn}/${config.publish.publicPath}`
    const zipFileCdn = `${cdn}${zipFile}`

    // console.log("zipFileCdn", zipFileCdn)
    const payload = {
        configData: JSON.stringify({
            cdn: zipFileCdn,
            version,
            // publishTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        }),
    }
    console.log("payload", payload)

    const modify_url = `${HOST}/admin/remote_config/${appId}/${ENV}/official/Windows/launcher-web`
    const publish_url = `${HOST}/admin/remote_config/publish/${appId}/${ENV}/official/Windows/launcher-web`
    // console.log("modify_url", modify_url)
    // console.log("publish_url", publish_url)
    try {
        const res = await fetch(modify_url, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                "x-hg-access-secret": SECRET_KEY,
            },
            body: JSON.stringify(payload),
        })
        if (!res.ok) throw res
        const data = await res.json()
        const publishRes = await fetch(publish_url, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                "x-hg-access-secret": SECRET_KEY,
            },
        })
        if (!publishRes.ok) throw new Error(`HTTP ${publishRes.status}`)
        const publishData = await publishRes.json()
        console.log(
            `发布版本管理平台成功，version: ${version}, build: ${zipFile}`
        )
    } catch (e) {
        console.log("error", e)
    }
}

publishPlatform()
