import React, { useContext, useMemo } from "react";
import { HolderOutlined } from "@ant-design/icons";
import type { DragEndEvent } from "@dnd-kit/core";
import { DndContext } from "@dnd-kit/core";
import type { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import { tsToDateTimeRender } from "@/features/renders";
import { BannerConfig } from "@/pages/Banner/hooks";
import { Language } from "@/data";
import { stateRender } from "../../../BannerList/renders";

interface Props {
  banners: BannerConfig[];
  setBanners: (val: BannerConfig[]) => void;
  language: Language;
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: "move" }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

const columns: ColumnsType<BannerConfig> = [
  { key: "sort", align: "center", width: 80, render: () => <DragHandle /> },
  { title: "ID", dataIndex: "id" },
  { title: "描述", dataIndex: "description" },
  { title: "创建时间", dataIndex: "create_ts", render: tsToDateTimeRender },
  { title: "状态", dataIndex: "state", render: stateRender },
  // { title: "Address", dataIndex: "address" },
];

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}

const Row: React.FC<RowProps> = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

export function Sort({ banners, setBanners }: Props) {
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = banners.findIndex(
        (record) => record.id === active?.id,
      );
      const overIndex = banners.findIndex((record) => record.id === over?.id);
      const result = arrayMove(banners, activeIndex, overIndex);
      setBanners(result);
    }
  };

  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext
        items={banners.map((i) => i.id!)}
        strategy={verticalListSortingStrategy}
      >
        <Table
          rowKey="id"
          title={() => "拖动以修改顺序"}
          style={{ width: 600 }}
          components={{ body: { row: Row } }}
          columns={columns}
          dataSource={banners}
          pagination={false}
        />
      </SortableContext>
    </DndContext>
  );
}
