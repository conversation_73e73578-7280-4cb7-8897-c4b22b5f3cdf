import { useEffect } from "react"
import bridge from "@/utils/bridge"
import { Page404Icon } from "@/components/Icon/Page404Icon"

const Page404: React.FC = () => {
    useEffect(() => {
        handlePageViewEvent()
    }, [])

    // 启动器侧-404页曝光埋点
    const handlePageViewEvent = () => {
        bridge.eventLog("launcher", "launcher_page_exposure", {
            type: "404",
        })
    }
    return (
        <div className="bg-[rgb(24,23,26)]/90 h-full">
            <div
                className={"flex flex-col items-center pt-[180px] select-none"}
            >
                <Page404Icon />
                <span className="text-white text-[12px] mt-3">暂无游戏</span>
            </div>
        </div>
    )
}

export default Page404
