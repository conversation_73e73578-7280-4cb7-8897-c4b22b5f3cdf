import { MainPageState } from "@/pages/MainSetting/hooks";

export function getStateColor(state?: MainPageState) {
  switch (state) {
    case MainPageState.Unpublished:
      return undefined;
    case MainPageState.Timing:
      return "orange";
    case MainPageState.Published:
      return "green";
    case MainPageState.AutoOffLine:
      return "magenta";
    case MainPageState.ManualOffLine:
      return "volcano";
    default:
      return `未知${state}`;
  }
}
