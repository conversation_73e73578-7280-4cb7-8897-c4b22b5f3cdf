import dayjs from "dayjs";
import { ModalForm } from "@ant-design/pro-components";
import { Button, message } from "antd";
import { useAppCode } from "@/hooks";
import utc from "dayjs/plugin/utc";
import {
  useMainData,
  usePublishMainData,
  useCurrentLangValue,
} from "../../../../hooks";
import { ProFormUtcTime } from "@/features";
import { getStartTs } from "@/features/ProFormUtcTime/utils";
dayjs.extend(utc);

interface Values {
  utc: number;
  startTime: string;
}

export function PublishButton() {
  const publish = usePublishMainData();
  const currentLangValue = useCurrentLangValue();
  const appcode = useAppCode();
  const { currentConfig } = useMainData();

  return (
    <div>
      <ModalForm<Values>
        width={600}
        title="发布设置"
        initialValues={{ utc: 8 }}
        onFinish={async (values) => {
          const startTs = getStartTs(values);
          try {
            await publish({
              id: currentConfig?.id,
              appcode: currentConfig?.appcode || appcode,
              start_ts: startTs + "",
              language: currentLangValue,
            });
            message.success("操作成功");
            return true;
          } catch (error) {
            message.error("发布失败");
          }
        }}
        trigger={<Button type="primary">发布</Button>}
      >
        <ProFormUtcTime />
      </ModalForm>
    </div>
  );
}
