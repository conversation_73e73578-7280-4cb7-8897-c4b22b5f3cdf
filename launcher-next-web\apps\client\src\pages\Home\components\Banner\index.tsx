import bannerImage from "@/assets/banner.png"
import { Carousel } from "@/components/Carousel"
import bridge from "@/utils/bridge"
import { Banner as BannerData } from "@/types/mainData"

// TODO: 加个 preload
const wrapperClassName = "w-[16.25rem] h-full relative overflow-hidden shrink-0"
export function Banner({
    data: banners,
    appcode,
}: {
    data?: BannerData[]
    appcode?: string
}) {
    if (!banners || !banners?.length) return null

    const handleClick = (url?: string) => {
        bridge.open({ url }, "banner", {}, "launcher_banner_jump_event", {
            appcode,
            jump_url: url,
        })
    }

    return (
        <div className={wrapperClassName}>
            {banners?.length === 1 ? (
                <img
                    className="w-full absolute cursor-pointer"
                    src={banners[0]?.url}
                    onClick={() => handleClick(banners[0]?.jump_url)}
                    onDragStart={(e) => e.preventDefault()}
                ></img>
            ) : (
                <Carousel
                    images={banners.slice(0, 8)}
                    interval={5000}
                    onClick={handleClick}
                />
            )}
        </div>
    )
}
