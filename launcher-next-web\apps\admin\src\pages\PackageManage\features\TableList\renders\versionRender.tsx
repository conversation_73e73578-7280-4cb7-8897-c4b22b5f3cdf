import { useState } from "react";
import { Flex, Space, Typography, Drawer, Descriptions, Tag } from "antd";
import {
  PackagePublishStateConfig,
  PackagePublishState,
  LauncherPackageConfig,
  useGetNote,
} from "@/pages/PackageManage/hooks/useLauncherPackageList";
import { useDescriptions } from "../hooks/useDescriptions";
import { gtVersion } from "@/utils/version";

export function versionRender(value: any, record: LauncherPackageConfig) {
  return <VersionRender value={value} record={record} />;
}

export function VersionRender({
  value,
  record,
}: {
  value: any;
  record: LauncherPackageConfig;
}) {
  const {
    appcode = "",
    version = "",
    gray_latest_version = "",
    sub_channel,
    channel,
    platform,
    latest_version = "",
    force_update_version,
    state,
  } = record;
  const isGrayState = state === PackagePublishState.LauncherState_Gray;
  const isLastestVersion = version === latest_version && !isGrayState;
  const isForceUpdateVersion = version === force_update_version && !isGrayState;
  const isLastestGrayVersion = version === gray_latest_version && isGrayState;
  const params = {
    appcode,
    channel,
    sub_channel,
    platform,
    version,
  };

  const { trigger } = useGetNote();

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [releaseNotes, setReleaseNotes] = useState({});

  const handleOpenDetail = async () => {
    setOpen(true);
    setLoading(true);
    try {
      const res = await trigger(params);
      setReleaseNotes(res.release_notes);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };
  return (
    <>
      <Flex align="center" gap="small">
        <Typography.Link
          onClick={handleOpenDetail}
          style={{ whiteSpace: "nowrap" }}
        >
          {version}
        </Typography.Link>
        <Space direction="vertical">
          {isLastestVersion && <Tag color="processing">最新全量版本</Tag>}
          {isForceUpdateVersion && <Tag color="error">最低可玩版本</Tag>}
          {isLastestGrayVersion && <Tag color="warning">最新灰度版本</Tag>}
        </Space>
      </Flex>
      <PackageDetailDrawer
        open={open}
        loading={loading}
        record={record}
        releaseNotes={releaseNotes}
        onClose={() => setOpen(false)}
      ></PackageDetailDrawer>
    </>
  );
}

const PackageDetailDrawer = ({
  open,
  loading,
  record,
  releaseNotes,
  onClose,
}: {
  open: boolean;
  loading: boolean;
  record: LauncherPackageConfig;
  releaseNotes: Record<string, string>;
  onClose: () => void;
}) => {
  const { version } = record;
  const title = `包体详情_${version}`;
  const labelStyle = { color: "#111111", width: "140px", paddingLeft: "30px" };
  const [publishDesc, baseDesc, packageDesc] = useDescriptions(
    record,
    releaseNotes,
  );

  return (
    <Drawer
      title={title}
      open={open}
      width={700}
      loading={loading}
      destroyOnClose
      onClose={onClose}
    >
      <Space direction="vertical" size={30}>
        <Descriptions
          title="发布信息"
          column={1}
          labelStyle={labelStyle}
          items={publishDesc}
        />
        <Descriptions
          title="基础信息"
          column={1}
          labelStyle={labelStyle}
          items={baseDesc}
        />
        <Descriptions
          title="包体信息"
          column={1}
          labelStyle={labelStyle}
          items={packageDesc}
        />
      </Space>
    </Drawer>
  );
};
