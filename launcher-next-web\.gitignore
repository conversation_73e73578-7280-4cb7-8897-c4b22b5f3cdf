# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# # dependencies
/node_modules

.vscode


# logs
/logs


# production
/dist
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# config
config/config.admin.json
config/config.web.json
config/_config.json
config/config.json

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json


apps/admin/node_modules
apps/admin/.lego-app