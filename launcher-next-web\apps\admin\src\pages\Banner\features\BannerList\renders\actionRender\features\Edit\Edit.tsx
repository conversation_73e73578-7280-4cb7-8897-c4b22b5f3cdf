import { EditForm } from "@/pages/Banner/features/EditForm";
import { useUpdateBanner } from "@/pages/Banner/hooks";
import {
  BannerConfig,
  BannerState,
} from "@/pages/Banner/hooks/useBanner/types";
import { Typography } from "antd";
import { BannerActionProps } from "../types";

export function Edit({ bannerConfig }: BannerActionProps) {
  const updateBanner = useUpdateBanner();
  return (
    <EditForm
      title="编辑 Banner"
      trigger={<Typography.Link>编辑</Typography.Link>}
      initialValues={bannerConfig}
      disableModify={bannerConfig.state !== BannerState.Unpublished}
      onFinish={async (value) => {
        updateBanner({ ...bannerConfig, ...value });
      }}
    />
  );
}
