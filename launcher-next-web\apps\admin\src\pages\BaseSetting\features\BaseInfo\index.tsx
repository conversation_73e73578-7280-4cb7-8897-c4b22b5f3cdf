import React from "react";
import { Space, Flex, Image, Row, Col, Descriptions } from "antd";
import {
  AppConfig,
  GeneralPlatformLabelMap,
  GameTypeLabelMap,
} from "../../hooks/types";

interface Props {
  config: AppConfig;
}

export const BaseInfo: React.FC<Props> = ({ config: appConfig }) => {
  const { icon_url, name, general_platform, game_type, slogan } = appConfig;
  return (
    <Flex gap={100} align="center">
      <div style={{ paddingLeft: "100px" }}>
        <Image width={140} height={140} src={icon_url} alt="logo Image"></Image>
      </div>
      <Descriptions column={2} contentStyle={{ paddingBottom: "15px" }}>
        <Descriptions.Item label="游戏名称">{name}</Descriptions.Item>
        <Descriptions.Item label="游戏平台">
          {GeneralPlatformLabelMap[general_platform]}
        </Descriptions.Item>
        <Descriptions.Item label="游戏类型">
          {GameTypeLabelMap[game_type]}
        </Descriptions.Item>
        <Descriptions.Item label="游戏宣传语">{slogan}</Descriptions.Item>
      </Descriptions>
    </Flex>
  );
};
