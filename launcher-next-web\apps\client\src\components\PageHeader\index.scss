@mixin header-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 0.5px solid rgba(255, 255, 255, 0.1);
    background: rgba(24, 23, 26, 0.95);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.55);
    box-sizing: content-box;
    cursor: pointer;

  
}

.header-container {
    .header-icon {
        @include header-icon;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        &:hover {
          background:
              linear-gradient(
                  0deg,
                  rgba(255, 255, 255, 0.15) 0%,
                  rgba(255, 255, 255, 0.15) 100%
              ),
              rgba(24, 23, 26, 0.95);
      }
    }
    .header-icon_login {
        @include header-icon;
        padding: 0px 16px 0px 12px;
        height: 2.5rem;
        border-radius: 1.25rem;
        font-size: 13px;
        line-height: 18px;
        color: white;
        &:hover {
          background:
              linear-gradient(
                  0deg,
                  rgba(255, 255, 255, 0.15) 0%,
                  rgba(255, 255, 255, 0.15) 100%
              ),
              rgba(24, 23, 26, 0.95);
      }
    }
    .header-icon_logout {
        @include header-icon;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
    }
       
}
