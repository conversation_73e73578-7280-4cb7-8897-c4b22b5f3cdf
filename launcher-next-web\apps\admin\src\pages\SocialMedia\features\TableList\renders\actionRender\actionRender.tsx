import {
  SidebarConfig,
  SidebarState,
} from "@/pages/SocialMedia/hooks/useSidebar/types";
import { Dropdown, Space, Typography } from "antd";
import { CancelPublish, Copy, Edit, Remove } from "./features";
import { Publish } from "@/pages/SocialMedia/features/Publish";

export function actionRender(_: any, sidebarConfig: SidebarConfig) {
  const showPublishButton = [
    SidebarState.Unpublished,
    // SidebarState.AutoOffLine,
    // SidebarState.ManualOffLine,
  ].includes(sidebarConfig.state!);
  const showCancelPublishButton = [SidebarState.Published].includes(
    sidebarConfig.state!,
  );
  const showEditButton =
    [SidebarState.OffLine].includes(sidebarConfig.state!) === false;
  return (
    <Space align="center">
      {showEditButton && <Edit sidebarConfig={sidebarConfig} />}

      {showPublishButton && (
        <Publish
          sidebarsToPublish={[sidebarConfig]}
          trigger={<Typography.Link type="success">发布</Typography.Link>}
        />
      )}
      {showCancelPublishButton && (
        <CancelPublish sidebarConfig={sidebarConfig} />
      )}
      <Dropdown
        menu={{
          items: [
            { key: "copy", label: <Copy sidebarConfig={sidebarConfig} /> },
            {
              key: "remove",
              label: <Remove sidebarConfig={sidebarConfig} />,
              danger: true,
            },
          ],
        }}
      >
        <Typography.Link style={{ whiteSpace: "nowrap" }}>...</Typography.Link>
      </Dropdown>
    </Space>
  );
}
