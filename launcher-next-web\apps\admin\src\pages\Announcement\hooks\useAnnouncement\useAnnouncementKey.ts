import { useAppCode } from "@/hooks";
import { DATA_KEY } from "./const";
import { usePageValue } from "../usePage";
import { useSearchValue } from "../useSearch";
import { useSort, useSortValue } from "../useSort";
import { useFilterValue } from "@/pages/Banner/hooks/useFilter";

export function useAnnouncementKey() {
  const page = usePageValue();
  const sort = useSortValue();
  // console.log(sort)
  const filter = useFilterValue();
  const searchVal = useSearchValue();
  const appCode = useAppCode();
  return [DATA_KEY, appCode, { page, searchVal, sort, filter }] as const;
}
