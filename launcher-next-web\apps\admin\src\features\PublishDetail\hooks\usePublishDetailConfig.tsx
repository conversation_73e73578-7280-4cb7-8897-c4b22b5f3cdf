import { useMemo } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import type { StepsProps } from "antd";
import { PublishStep, StepStatus } from "../config";

export const usePublishDetailConfig = (
  publish_stage: string,
  result: string,
  stepList: PublishStep[],
  PublishStepConfig: Record<string, { title: string; triggerName: string }>,
  StageConfigMap: Record<string, { stepType: PublishStep; status: StepStatus }>,
  resultConfigMap: Record<
    string,
    {
      icon: JSX.Element;
      color: string;
      textDom?: string | JSX.Element;
      extra?: string | JSX.Element;
      buttonText: string;
      buttonDisable: boolean;
    }
  >,
) => {
  const publishConfig = useMemo(() => {
    const items: StepsProps["items"] = stepList.map((step) => {
      return {
        title: PublishStepConfig[step]?.title,
      };
    });

    let status = StageConfigMap[publish_stage]?.status;
    const stepType = StageConfigMap[publish_stage]?.stepType || "";
    const current = stepList.findIndex((item) => item === stepType);
    let buttonConfig = {
      text: "",
      disabled: false,
      triggerName: PublishStepConfig[stepType]?.triggerName,
    };

    for (let i = 0; i < current; i++) {
      items[i].title = `${items[i].title}成功`;
    }
    if (status === StepStatus.loading) {
      status = StepStatus.process;
      const disabled =
        publish_stage === "PublishStage_UNPUBLISH" ? false : true; // 特殊逻辑：灰度发布中可以发布调整
      buttonConfig = {
        ...buttonConfig,
        text: stepType === PublishStep.PUBLISH ? "发布调整" : "执行",
        disabled,
      };
      items[current] = {
        ...items[current],
        title: `${items[current].title}中...`,
        icon: <LoadingOutlined />,
      };
    } else if (status === StepStatus.error) {
      buttonConfig = {
        ...buttonConfig,
        text: "重试",
        disabled: false,
      };
      items[current] = {
        ...items[current],
        title: `${items[current].title}失败`,
      };
    } else if (status === StepStatus.process) {
      const { buttonText, buttonDisable } = resultConfigMap[result] || {};
      buttonConfig = {
        ...buttonConfig,
        text: stepType === PublishStep.PUBLISH ? "发布调整" : buttonText,
        disabled: buttonDisable,
      };
    } else if (status === StepStatus.finish) {
      buttonConfig = {
        ...buttonConfig,
        text: "确定",
        disabled: false,
        triggerName: "handleFinish",
      };
      items[current] = {
        ...items[current],
        title: `${items[current].title}成功`,
      };
    }

    const stepConfig = {
      current,
      status,
      items,
    };

    return { stepConfig, buttonConfig };
  }, [stepList, publish_stage, result]);

  return publishConfig;
};
