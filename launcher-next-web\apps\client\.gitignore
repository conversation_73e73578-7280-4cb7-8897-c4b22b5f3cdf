# compiled output
dist
build
output
lib
node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
# !.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

*Zone.identifier

# scripts
_tmp

# config
config/*
!config/config.template.json
!config/config.*.template.json

# font
src/assets/fonts/*
!src/assets/fonts/origin
src/web/src/assets/fonts/*
!src/web/src/assets/fonts/origin
