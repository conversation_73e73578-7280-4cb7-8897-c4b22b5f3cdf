import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M15.018 7.481a7.344 7.344 0 0 1 7.127 5.581s1.914.339 1.854 2.251c-.061 1.912-2.134 2.154-2.134 2.154a7.365 7.365 0 0 1-4.48 4.301c-.114.038-.179.188-.231.309-.014.031-.026.06-.039.085-.223.447-.817.373-1.234.307-.324-.05-.441-.27-.462-.581-.017-.26.102-.557.38-.636.567-.161 1.017-.076 1.017-.076s.182.078.352.019a6.728 6.728 0 1 0-8.371-3.817 2.217 2.217 0 0 0-.108.023l-.067.015c-.19.043-.428.098-.446.051 0 0-2.086-.12-2.173-2.109-.087-1.987 1.89-2.3 1.89-2.3a7.34 7.34 0 0 1 7.125-5.577Zm0 1.738a5.6 5.6 0 0 1 5.6 5.6 5.6 5.6 0 0 1-7.217 5.363 12.25 12.25 0 0 1-.945-.44 7.023 7.023 0 0 1-1.46-1.027 5.599 5.599 0 0 1 4.02-9.496Zm3.051 6.73s-2.93 2.679-6.103 0c0 0 0 2.772 3.052 2.772 2.916 0 3.045-2.531 3.051-2.756v-.016Zm-5.099-3.647c-.372 0-.674.403-.674.9 0 .496.302.9.674.9.373 0 .675-.404.675-.9 0-.497-.302-.9-.675-.9Zm4.048 0c-.372 0-.674.403-.674.9 0 .496.302.9.674.9.373 0 .675-.404.675-.9 0-.497-.302-.9-.675-.9Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={30.038}
        x={-1.5}
        y={1.856}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50779"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50779"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
