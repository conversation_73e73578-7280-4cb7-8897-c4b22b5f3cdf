import useSWR from "swr";
import { fetcher } from "@/utils/fetcher";
import { useUrlParams } from "../useUrlParams";
import { URL_PREFIX } from "../../const";

interface ListResponse {
  versions: string[];
}

export function useDropOptions(options?: { platform?: string }) {
  const seachParams = useUrlParams();
  const appCode = seachParams.get("appCode");
  const URL = URL_PREFIX + "/drop_list_info";
  const key = [URL, appCode] as const;

  const { data, isLoading, mutate } = useSWR(
    key,
    ([url, appCode]) =>
      fetcher<ListResponse>(
        url,
        {
          appcode: appCode,
        },
        {
          method: "GET",
        },
      ),
    {
      onError(err, key, config) {
        console.error(err?.message || "请求错误");
      },
    },
  );
  const { versions } = data || {};
  const versionOptions = versions?.map((item) => {
    return {
      label: item,
      value: item,
    };
  });

  return {
    versionOptions,
  };
}
