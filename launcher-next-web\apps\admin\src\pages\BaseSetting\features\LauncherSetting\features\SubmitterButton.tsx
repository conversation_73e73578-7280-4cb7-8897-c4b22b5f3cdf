import { Button, Flex, Space } from "antd";

interface Props {
  loading?: boolean;
  readonly: boolean;
  onToggle: (value: boolean) => void;
  onSubmit: () => void;
  onReset: () => void;
}
export function SubmitterButton({
  loading,
  readonly,
  onToggle,
  onSubmit,
  onReset,
}: Props) {
  return (
    <Flex justify="center" align="center" gap="20">
      {readonly ? (
        <Button type="primary" onClick={() => onToggle(false)}>
          编辑
        </Button>
      ) : (
        <Space size="middle">
          <Button onClick={onReset}>取消</Button>
          <Button type="primary" loading={loading} onClick={onSubmit}>
            保存
          </Button>
        </Space>
      )}
    </Flex>
  );
}
