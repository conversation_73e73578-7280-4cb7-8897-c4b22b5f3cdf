{"name": "admin", "version": "0.0.1", "description": "b 端", "scripts": {"dev": "lego-app dev", "build": "lego-app build && pnpm run publish:web", "publish:web": "omc-cli release"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/pro-components": "^2.7.10", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hg/launcher-share": "workspace:*", "@hg-omc/bootstrap-app": "^2.1.4", "@hg-omc/cli": "^1.0.4", "@hg-omc/layouts": "^1.1.2", "@hg/lego-app": "^1.4.10", "ahooks": "^3.8.0", "antd": "^5.19.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "eventemitter3": "^5.0.1", "framer-motion": "^11.2.12", "jotai": "^2.8.4", "lodash": "^4.17.21", "popmotion": "^11.0.5", "react": "^18.3.1", "react-transition-group": "^4.4.5", "semver": "^7.6.2", "swr": "^2.2.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.5", "@types/react": "^18.3.3", "@types/react-transition-group": "^4.4.11", "@types/semver": "^7.5.8"}}