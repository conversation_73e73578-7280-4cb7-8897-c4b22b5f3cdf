// 通用config，后续若需要定制可将拆出部分config维护在对应流水线本身
import { Typography, Tooltip } from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { PublishStage } from "@/data/publishStage";

export enum StepStatus {
  wait = "wait",
  process = "process",
  loading = "loading",
  finish = "finish",
  error = "error",
}

export enum PublishStep {
  SYNC = 1,
  PREWARM,
  GRAYPUBLISH,
  PUBLISH,
}

export const PublishStepConfig = {
  [PublishStep.SYNC]: {
    title: "资源同步",
    triggerName: "handleSync",
  },
  [PublishStep.PREWARM]: {
    title: "资源预热",
    triggerName: "handlePrewarm",
  },
  [PublishStep.GRAYPUBLISH]: {
    title: "灰度发布",
    triggerName: "handleGrayPublish",
  },
  [PublishStep.PUBLISH]: {
    title: "全量发布",
    triggerName: "handlePublish",
  },
};

export const StageConfigMap: {
  [key in PublishStage]?: { stepType: PublishStep; status: StepStatus };
} = {
  [PublishStage.PublishStage_RESERVE]: {
    stepType: PublishStep.SYNC,
    status: StepStatus.wait,
  },
  [PublishStage.PublishStage_UNSYNC]: {
    stepType: PublishStep.SYNC,
    status: StepStatus.process,
  },
  [PublishStage.PublishStage_SYNCING]: {
    stepType: PublishStep.SYNC,
    status: StepStatus.loading,
  },
  [PublishStage.PublishStage_SYNC_FAIL]: {
    stepType: PublishStep.SYNC,
    status: StepStatus.error,
  },
  [PublishStage.PublishStage_UNPREWARM]: {
    stepType: PublishStep.PREWARM,
    status: StepStatus.process,
  },
  [PublishStage.PublishStage_PREWARMING]: {
    stepType: PublishStep.PREWARM,
    status: StepStatus.loading,
  },
  [PublishStage.PublishStage_PREWARM_FAIL]: {
    stepType: PublishStep.PREWARM,
    status: StepStatus.error,
  },
  [PublishStage.PublishStage_UNGRAYPUBLISH]: {
    stepType: PublishStep.PUBLISH,
    status: StepStatus.process,
  },
  [PublishStage.PublishStage_UNPUBLISH]: {
    stepType: PublishStep.PUBLISH,
    status: StepStatus.loading,
  },
  [PublishStage.PublishStage_PUBLISH_FINISH]: {
    stepType: PublishStep.PUBLISH,
    status: StepStatus.finish,
  },
};

export const resultConfigMap: {
  [key: string]: {
    icon: JSX.Element;
    color: string;
    textDom?: string | JSX.Element;
    extra?: string | JSX.Element;
    buttonText: string;
    buttonDisable: boolean;
  };
} = {
  error: {
    icon: <CloseCircleOutlined />,
    color: "error",
    buttonText: "执行",
    buttonDisable: true,
  },
  warn: {
    icon: <ExclamationCircleOutlined />,
    color: "warning",
    // extra: (
    //   <Tooltip title="洗包更新后的子渠道和官服子渠道">
    //     <Typography.Link style={{ fontSize: "12px" }}>
    //       什么是必发子渠道？
    //     </Typography.Link>
    //   </Tooltip>
    // ),
    buttonText: "忽略并执行",
    buttonDisable: false,
  },
  approve: {
    icon: <CheckCircleOutlined />,
    color: "success",
    buttonText: "执行",
    buttonDisable: false,
  },
};
