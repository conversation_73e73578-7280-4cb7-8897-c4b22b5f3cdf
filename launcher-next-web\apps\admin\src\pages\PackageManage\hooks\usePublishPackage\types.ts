import { PublishStage } from "@/data/publishStage";

export enum UpdateType {
  focus_update = 1,
  nofocus_tip_update = 2,
  nofocus_notip_update = 3,
}

export enum PublishType {
  PublishType_RESERVE = "PublishType_RESERVE",
  PublishType_Full = "PublishType_Full",
  PublishType_Gray = "PublishType_Gray",
}

export const UpdateTypeOptions = [
  {
    label: "强制更新",
    value: UpdateType.focus_update,
  },
  {
    label: "弹窗不强制",
    value: UpdateType.nofocus_tip_update,
  },
  {
    label: "不弹窗且不强制",
    value: UpdateType.nofocus_notip_update,
  },
];

export const PublishTypeOptions = [
  {
    label: "全量发布",
    value: PublishType.PublishType_Full,
  },
  {
    label: "灰度发布",
    value: PublishType.PublishType_Gray,
  },
];

export interface GetLauncherReportRequest {
  appcode: string;
  channel?: string;
  sub_channel?: string;
  platform?: string;
  version?: string;
}

export interface GetLauncherPublishReportReply {
  channel?: string;
  sub_channel?: string;
  version?: string;
  publish_stage: PublishStage;
  result: string; // 校验结果类型 error, warn, approve
  result_reason: string; // 校验内容
  update_type: UpdateType; // 包更新类型：1-强更，2-非强更提示，3-非强更不提示
  publish_type: PublishType; // 发布类型
  gray_ratio?: number; // 灰度发布比例
}

export type PublishRequest = GetLauncherReportRequest & {
  update_type: UpdateType; // 包更新类型：1-强更，2-非强更提示，3-非强更不提示
  publish_type: PublishType; // 发布类型
  gray_ratio?: number; // 灰度发布比例
};
