import { Language } from "@/data";

/**
 * bannerListBannerConfigsReq
 */
export interface ListRequest {
  appcode?: string;
  order_bys?: BannerOrderby[];
  filter?: AnnouncementFilter;
  page_num?: number;
  page_size?: number;
  search_keywords?: BannerSearchKeyword[];
}

/**
 * announcementAnnouncementFilter
 */
export interface AnnouncementFilter {
  states?: number[];
  tab_ids?: string[];
  languages?: string[];
}

/**
 * bannerOrderby
 */
export interface BannerOrderby {
  field?: string;
  order?: string;
}

/**
 * bannerSearchKeyword
 */
export interface BannerSearchKeyword {
  field?: string;
  keyword?: string;
}

/**
 * announcementListAnnouncementConfigsRsp
 */
export interface ListResponse {
  announcement_configs?: AnnouncementConfig[];
  total_count?: string;
}

/**
 * announcementAnnouncementConfig
 */
export interface AnnouncementConfig {
  appcode?: string;
  create_ts?: string;
  data?: AnnouncementData;
  description?: string;
  end_ts?: string;
  id?: string;
  language?: string;
  mversion?: string;
  operator?: string;
  start_ts?: string;
  state?: AnnouncementState;
  tab_id?: string;
  timer_kind?: number;
  update_ts?: string;
}

/**
 * announcementAnnouncementData
 */
export interface AnnouncementData {
  content?: string;
  jump_url?: string;
}

/**
 * bannerBannerConfig
 */
export interface BannerConfig {
  appcode?: string;
  create_ts?: string;
  data?: BannerData;
  description?: string;
  end_ts?: string;
  id?: string;
  languages?: string[];
  mversion?: string;
  operator?: string;
  start_ts?: string;
  state?: AnnouncementState;
  timer_kind?: TimerKind;
  update_ts?: string;
}

/**
 * bannerBannerData
 */
export interface BannerData {
  jump_url?: string;
  md5?: string;
  url?: string;
}

export enum AnnouncementState {
  Unpublished = 1,
  Timing,
  Published,
  AutoOffLine,
  ManualOffLine,
}

export enum TimerKind {
  ShortTime = 1,
  LongTime,
}

/**
 * announcementCreateAnnouncementConfigReq
 */
export interface CreateRequest {
  appcode?: string;
  data?: AnnouncementData;
  description?: string;
  end_ts?: string;
  language?: string;
  start_ts?: string;
  tab_id?: string;
  timer_kind?: number;
}

/**
 * announcementUpdateAnnouncementConfigReq
 */
export interface UpdateRequest {
  appcode?: string;
  data?: AnnouncementData;
  description?: string;
  end_ts?: string;
  id?: string;
  language?: string;
  start_ts?: string;
  tab_id?: string;
  timer_kind?: number;
}

/**
 * bannerDeleteBannerConfigReq
 */
export interface RemoveRequest {
  appcode?: string;
  id?: string;
}

/**
 * bannerGetBannerConfigOrdersRsp
 */
export interface GetOrdersResponse {
  orders?: AnnouncementConfigOrder[];
}

/**
 * announcementAnnouncementConfigOrder
 */
export interface AnnouncementConfigOrder {
  announcement_configs?: AnnouncementConfig[];
  language?: string;
  tab_id?: string;
}

/**
 * bannerPublishBannerConfigReq
 */
export interface PublishRequest {
  appcode?: string;
  orders?: BannerConfigIdOrder[];
}

/**
 * bannerBannerConfigIdOrder
 */
export interface BannerConfigIdOrder {
  tab_id?: string;
  language?: string;
  order_ids?: string[];
  publish_ids?: string[];
}

/**
 * bannerExpireBannerConfigReq
 */
export interface ExpireBannerRequest {
  appcode?: string;
  id?: string;
}
