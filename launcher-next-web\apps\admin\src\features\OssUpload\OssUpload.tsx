import { InboxOutlined } from "@ant-design/icons";
import { Image, message, UploadProps } from "antd";
import <PERSON><PERSON> from "antd/es/upload/Dragger";
import type { RcFile, UploadChangeParam } from "antd/es/upload/interface";
import { cloneDeep, isEmpty, last } from "lodash";
import { useEffect, useRef, useState } from "react";
import { getUploadData, UploadDataResponse } from "./request";
import { useTopic } from "@hg-omc/bootstrap-app";
import { FileData } from "@/types";
import { calculateMD5, checkImageAspectRatio } from "@/utils/file";
import { UploadFile } from "antd/lib";
import { getUrl } from "./utils";

function UploadButton({ description }: { description?: string }) {
  return (
    <div className={"uploadButton"}>
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">点击或拖动到此区域上传图片</p>
      {description && (
        <p style={{ color: "#999", fontSize: "12px" }}>{description}</p>
      )}
    </div>
  );
}
export type OssUploadProps = UploadProps & {
  children?: React.ReactNode;
  /**
   * TODO 目前还未实现 maxCount 多个的情况，所以此处目前必传1，否则需要修改部分代码
   *  */
  maxCount?: 1;
  description?: string;
  limit?: {
    size?: number;
    aspectRatio?: {
      width: number;
      height: number;
    };
  };
} & (
    | {
        md5: true;
        value?: FileData | FileData[];
        onChange?: (fileList?: FileData | FileData[]) => void;
      }
    | {
        md5?: false;
        value?: string | string[];
        onChange?: (fileList?: string | string[]) => void;
      }
  );

type OSSData = UploadDataResponse;

export const isValidImage = (suffix: string) => {
  return ["jpg", "jpeg", "png"].includes(suffix);
};

/** 目前还未实现 maxCount 多个的情况 */
export const OssUpload = ({
  md5,
  value,
  onChange,
  maxCount = 1,
  limit,
  description,
  ...otherProps
}: OssUploadProps) => {
  const OSSDataRef = useRef<OSSData>();
  const topic = useTopic();
  const url = getUrl(value);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  useEffect(() => {
    setFileList(
      url
        ? [
            {
              uid: "1",
              name: url,
              status: "done",
              url: url,
            },
          ]
        : [],
    );
  }, [url]);
  const handleChange = (
    info: UploadChangeParam<UploadFile & { md5?: string }>,
  ) => {
    if (info.file.status === "done") {
      if (maxCount === 1) {
        if (md5) {
          const { md5 = "", url = "" } = info.file;
          onChange?.({ ...value, md5, url });
        } else {
          onChange?.(info.file.url);
        }
      } else {
        if (md5) {
          onChange?.(
            info.fileList.map((file) => ({
              url: file.url || "",
              md5: file.md5 || "",
            })),
          );
        } else {
          onChange?.(info.fileList.map((file) => file.url || ""));
        }
      }
    } else if (info.file.status === "removed") {
      if (maxCount === 1) {
        onChange?.(undefined as any);
        setFileList([]);
      } else {
        if (md5) {
          onChange?.(
            info.fileList.map((file) => ({
              url: file.url || "",
              md5: file.md5 || "",
            })),
          );
        } else {
          onChange?.(info.fileList.map((file) => file.url || ""));
        }
      }
    } else if (info.file.status === "error") {
      message.error(`${info.file.name} 上传失败`);
    }
  };

  const beforeUpload = async (
    file: RcFile & { url?: string; md5?: string },
  ) => {
    const suffix = last(file.name.split("."));
    if (!suffix || !isValidImage(suffix)) {
      message.error("上传图片类型错误");
      return false;
    }
    console.log(file);
    if (limit?.size) {
      const isLtSize = file.size / 1024 / 1024 < limit.size;
      if (!isLtSize) {
        message.error(`图片大小不超过${limit.size}M`);
        return false;
      }
    }

    if (limit?.aspectRatio) {
      const { width, height } = limit.aspectRatio;
      const valid = await checkImageAspectRatio(file, width / height);
      if (valid === false) {
        message.error(
          "图片分辨率不符，需重新上传，要求" + `${width}:${height}`,
        );
        return false;
      }
    }
    if (md5) {
      file.md5 = await calculateMD5(file);
    }
    try {
      const res = await getUploadData({
        file_ext: suffix,
        appcode: topic?.key || "",
      });
      file.url = res.download_url;

      OSSDataRef.current = res;

      if (maxCount === 1) {
        setFileList([file]);
      }

      return file;
    } catch (error) {
      console.error(error);
      message.error("上传失败");
    }
  };

  const uploadProps: UploadProps = {
    ...otherProps,
    action: async () => {
      return OSSDataRef.current?.upload_domain || "";
    },
    accept: "image/png, image/jpeg, image/jpg",
    onChange: handleChange,
    data: async () => {
      const body: any = cloneDeep(OSSDataRef.current?.body || {});
      body.OSSAccessKeyId = body.oss_access_key_id;
      body.successActionStatus = body.success_action_status;
      delete body.oss_access_key_id;
      delete body.success_action_status;
      return body;
    },
    beforeUpload,
    fileList: fileList,
    maxCount,
    method: OSSDataRef.current?.method as any,
    headers: {
      // upload 组件自带的头，有这个头会跨域，cors 配置没 access 这个头
      "X-Requested-With": null,
    } as any,
  };

  return (
    <div className={"aliOssUpload"}>
      <Dragger {...uploadProps}>
        {url ? (
          <Image
            src={url}
            // onClick={(e) => e.stopPropagation()}
            preview={false}
            alt="picture"
            style={{ maxHeight: "200px" }}
          />
        ) : (
          <UploadButton description={description} />
        )}
      </Dragger>
    </div>
  );
};
