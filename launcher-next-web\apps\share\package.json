{"name": "@hg/launcher-share", "version": "0.0.2", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "publishConfig": {"@hg:registry": "https://ops-nexus.hypergryph.net/repository/npm-hosted/"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"core-js": "^3.33.2"}, "devDependencies": {"@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "react": "^18.3.1", "react-dom": "^18.2.0"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.2.0"}}