import {
  useCancelPublishMainData,
  useMainData,
} from "@/pages/MainSetting/hooks/useMainData";
import { Button, Popconfirm } from "antd";

export function CancelPublishButton() {
  const cancelPublish = useCancelPublishMainData();
  const { currentConfig } = useMainData();
  return (
    <Popconfirm
      title="是否确定要撤销定时？"
      onConfirm={() =>
        cancelPublish({
          id: currentConfig?.id,
          appcode: currentConfig?.appcode,
        })
      }
    >
      <Button danger>撤销定时</Button>
    </Popconfirm>
  );
}
