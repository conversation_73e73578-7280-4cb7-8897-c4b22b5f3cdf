import { DescriptionsProps } from "antd";

import { ChannelNameEnum } from "@/data/channel";
import { tsToDateTimeRender } from "@/features/renders";
import {
  LauncherPackageConfig,
  UpdateTypeOptions,
  PublishTypeOptions,
} from "@/pages/PackageManage/hooks";
import {
  versionRender,
  stateRender,
  packageUrlRender,
  actionRender,
  updateNoteRender,
} from "../renders";

export function useDescriptions(
  record: LauncherPackageConfig,
  releaseNotes: Record<string, string>,
) {
  const updateTypeOption = UpdateTypeOptions.find(
    (item) => item.value === record.update_type,
  );
  const publishDesc: DescriptionsProps["items"] = [
    {
      label: "发布状态",
      children: <>{stateRender(record.state, record)}</>,
    },
    {
      label: "发布时间",
      children: tsToDateTimeRender(record.publish_time),
    },
    {
      label: "更新日志",
      children: <>{updateNoteRender(releaseNotes)}</>,
    },
    {
      label: "更新方式",
      children: updateTypeOption?.label || "-",
    },
    {
      label: "灰度比例",
      children: record.gray_ratio ? `${record.gray_ratio}%` : "-",
    },
  ];

  const baseDesc: DescriptionsProps["items"] = [
    {
      label: "ID",
      children: record.id,
    },
    {
      label: "appcode",
      children: record.appcode,
    },
    {
      label: "渠道",
      children: ChannelNameEnum[record.channel || ""] || "",
    },
    {
      label: "平台",
      children: record.platform,
    },
    {
      label: "注册时间",
      children: tsToDateTimeRender(record.register_time),
    },
    {
      label: "最后更新时间",
      children: tsToDateTimeRender(record.modify_time),
    },
    {
      label: "更新人",
      children: record.muser,
    },
  ];

  const packageDesc: DescriptionsProps["items"] = [
    {
      label: "版本号",
      children: record.version,
    },
    {
      label: "包体大小",
      children: record.package_size_str,
    },
    {
      label: "下载链接",
      children: packageUrlRender(null, record),
    },
  ];

  return [publishDesc, baseDesc, packageDesc];
}
