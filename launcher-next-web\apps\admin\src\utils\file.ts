import { RcFile } from "antd/es/upload";
import CryptoJS from "crypto-js";

// 计算文件的 MD5 值
export const calculateMD5 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const arrayBuffer = event.target?.result;
      if (typeof arrayBuffer !== "object" || arrayBuffer === null) {
        resolve("");
        return;
      }
      const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer);
      resolve(CryptoJS.MD5(wordArray).toString());
    };
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

export function checkImageAspectRatio(file: RcFile, aspectRatio: number) {
  // 参数分别是上传的file，想要限制的宽，想要限制的高
  return new Promise(function (resolve, reject) {
    const fileReader = new FileReader();
    fileReader.onload = (e) => {
      const src = e.target?.result;
      if (typeof src !== "string") {
        return;
      }
      const image = new Image();
      image.onload = function () {
        const width = image.width;
        const height = image.height;
        const imgAspectRatio = width / height;
        if (aspectRatio !== imgAspectRatio) {
          // 上传图片的宽高与传递过来的限制宽高作比较，超过限制则调用失败回调
          resolve(false);
        } else {
          resolve(true);
        }
      };
      image.onerror = reject;
      image.src = src;
    };
    fileReader.readAsDataURL(file);
  });
}
