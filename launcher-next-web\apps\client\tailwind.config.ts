import type { Config } from "tailwindcss"
const defaultTheme = require("tailwindcss/defaultTheme")

const config: Config = {
    content: [
        "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    ],
    theme: {
        extend: {
            width: {
                15: "3.75rem",
                70: "17.5rem",
            },
            colors: {
                primary: "rgb(var(--color-primary) / <alpha-value>)",
                "primary-hover":
                    "rgb(var(--color-primary_hover) / <alpha-value>)",

                "primary-disabled":
                    "rgb(var(--color-primary_disabled) / <alpha-value>)",
                "primary-disabled-hover":
                    "rgb(var(--color-primary_disabled_hover) / <alpha-value>)",
                // 按下主色
                "primary-pressed":
                    "rgb(var(--color-primary_pressed) / <alpha-value>)",
                "text-in-primary":
                    "rgb(var(--color-text-in-primary) / <alpha-value>)",
            },

            fontFamily: {
                sans: ["Source Han Sans CN", ...defaultTheme.fontFamily.sans],
            },
        },
    },
    plugins: [],
}
export default config
