import { useRemoveBanner } from "@/pages/Banner/hooks";
import { Popconfirm, Typography } from "antd";
import { BannerActionProps } from "../types";

export function Remove({ bannerConfig }: BannerActionProps) {
  const removeBanner = useRemoveBanner();
  return (
    <Popconfirm
      title="是否确认删除此 banner ？"
      onConfirm={() =>
        removeBanner({ id: bannerConfig.id, appcode: bannerConfig.appcode })
      }
    >
      <Typography.Link>删除</Typography.Link>
    </Popconfirm>
  );
}
