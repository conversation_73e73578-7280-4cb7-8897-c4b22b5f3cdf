import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnouncementKey } from "../useAnnouncementKey";
import { CreateRequest } from "../types";

export function useCreate() {
  const key = useAnnouncementKey();
  const { trigger } = useSWRMutation(key, create);
  return trigger;
}

async function create(_: any, { arg }: { arg: CreateRequest }) {
  await fetcher(`${URL_PREFIX}/create`, arg);
}
