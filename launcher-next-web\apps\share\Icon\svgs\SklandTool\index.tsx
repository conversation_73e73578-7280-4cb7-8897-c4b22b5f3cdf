import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M10.617 10.486c0-.712.577-1.29 1.29-1.29h6.187c.712 0 1.289.578 1.289 1.29v1.551h-8.766v-1.551ZM9.07 12.037v-1.551a2.836 2.836 0 0 1 2.836-2.836h6.188a2.836 2.836 0 0 1 2.836 2.836v1.551h.82a1.5 1.5 0 0 1 1.5 1.5v7.313a1.5 1.5 0 0 1-1.5 1.5H8.25a1.5 1.5 0 0 1-1.5-1.5v-7.313a1.5 1.5 0 0 1 1.5-1.5h.82Zm6.989 4.717c.398 0 .77.15 1.045.426l.275.275c.563-.248.99-.743 1.141-1.348a2.01 2.01 0 0 0 0-.948c-.041-.152-.234-.207-.344-.097l-.962.963h-1.059V14.98l.962-.963c.11-.11.055-.302-.096-.343a2.012 2.012 0 0 0-.948 0 2.003 2.003 0 0 0-1.527 1.966l1.155 1.155a1.68 1.68 0 0 1 .358-.041Zm.729.756a1.028 1.028 0 0 0-1.197-.193l-1.498-1.498v-.867l-1.774-1.333-.894.88 1.334 1.773h.866l1.485 1.485c-.192.385-.124.867.193 1.197l1.636 1.636a.523.523 0 0 0 .742 0l.743-.743a.523.523 0 0 0 0-.742l-1.636-1.595Zm-2.187.399-.825-.825L11.7 19.16a.938.938 0 0 0 0 1.334.95.95 0 0 0 .66.275.95.95 0 0 0 .66-.275l1.664-1.664a1.613 1.613 0 0 1-.083-.921Zm-2.282 2.296a.335.335 0 0 1-.33-.33c0-.179.151-.33.33-.33.178 0 .************ 0 .179-.151.33-.33.33Z"
        clipRule="evenodd"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.5}
        height={29.7}
        x={-0.75}
        y={2.025}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_393_16250"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_393_16250"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
