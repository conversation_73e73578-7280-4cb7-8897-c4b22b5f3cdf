import { useState } from "react";
import {
  <PERSON>er<PERSON><PERSON>,
  <PERSON>Form,
  ProFormList,
  ProFormDateTimePicker,
  ProFormDateTimeRangePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormGroup,
} from "@ant-design/pro-components";
import { Form, message, Input } from "antd";
import { HolderOutlined } from "@ant-design/icons";
import {
  closestCenter,
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import type { DragEndEvent } from "@dnd-kit/core";
import {
  arrayMove,
  verticalListSortingStrategy,
  SortableContext,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  SidebarConfig,
  SidebarState,
  SidebarType,
  SidebarTypeLabels,
  SidebarLabel,
} from "../../hooks/useSidebar";
import { ProFormOssUpload } from "@/features";
import { mediaList } from "@/data/media";
import { getDateByTs, getUtcTs } from "@/utils/time";
import { useLangList } from "@/hooks";
import "./index.less";

// type ValueWithTs = Values & { start_ts?: string; end_ts?: string };

interface Values {
  languages: string;
  data: SidebarConfig["data"];
}

interface Params {
  languages: string[];
  data: SidebarConfig["data"];
}

interface Props {
  title: React.ReactNode;
  trigger: JSX.Element;
  onFinish: (values: Params) => Promise<void>;
  initialValues?: SidebarConfig;
  disableModify?: boolean;
}

type SidebarLabelItem = SidebarLabel & {
  key: string;
};

export function EditForm({
  trigger,
  title,
  onFinish,
  initialValues,
  disableModify,
}: Props) {
  const [form] = Form.useForm<Values>();
  const langList = useLangList();
  const sidebarTypeOption = Object.keys(SidebarTypeLabels).map((key) => {
    return {
      value: key,
      label: SidebarTypeLabels[key as SidebarType],
    };
  });

  const [sidebarLabel, setSidebarLabel] = useState<SidebarLabelItem[]>(
    initialValues?.data?.sidebar_labels?.map((item, index) => ({
      ...item,
      key: index.toString(), // 赋予初始key，否则排序无法识别
    })) || [],
  );
  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over) {
      return;
    }
    if (active.id !== over.id) {
      const oldIndex = sidebarLabel.findIndex((item) => item.key === active.id);
      const newIndex = sidebarLabel.findIndex((item) => item.key === over.id);
      const sortedKeys = arrayMove(sidebarLabel, oldIndex, newIndex);

      const sidebar_labels = form.getFieldValue(["data", "sidebar_labels"]);
      const newSidebarLabels = arrayMove(sidebar_labels, oldIndex, newIndex);

      form.setFieldValue(["data", "sidebar_labels"], newSidebarLabels);
      setSidebarLabel(sortedKeys);
    }
  };

  return (
    <DrawerForm<Values>
      className="launcher-editForm"
      layout={"horizontal"}
      labelAlign={"left"}
      labelCol={{ span: 4, offset: 1 }}
      title={title}
      resize={{
        maxWidth: window.innerWidth * 0.8,
        minWidth: 700,
      }}
      form={form}
      trigger={trigger}
      drawerProps={{
        destroyOnClose: true,
      }}
      initialValues={{
        languages: initialValues?.languages?.[0],
        data: initialValues?.data,
      }}
      onFinish={async (values) => {
        console.log("onFinish", values);
        const { display_type, pic, sidebar_labels } = values.data || {};
        if (display_type === SidebarType.DisplayType_LIST) {
          if (!pic?.url && !sidebar_labels?.length) {
            message.error("图片、标签列表至少填写一项");
            return;
          }
        }
        const sidebarConfig = {
          ...values,
          languages: [values.languages],
        };

        try {
          await onFinish(sidebarConfig);
          message.success("提交成功");
          return true;
        } catch (error: any) {
          // console.error(error);
          if (error?.reason === "LAUNCHER_MANAGER_CONFIG_SIDEBAR_DUPLICATE") {
            message.error("当前语种下已存在该社媒，无法创建");
          } else {
            message.error(error?.message || "提交失败");
          }
        }
        // 不返回不会关闭弹框
        // return true;
      }}
    >
      <h3>属性设置</h3>
      <ProFormSelect
        label="应用语种"
        name="languages"
        disabled={disableModify}
        // mode="multiple"
        options={langList}
        style={{ width: "100%" }}
        width={"sm"}
        placeholder="请选择应用语种"
        rules={[{ required: true }]}
      />

      <h3>内容设置</h3>
      <ProFormSelect
        label="社媒"
        name={["data", "media"]}
        options={mediaList}
        width={"sm"}
        rules={[{ required: true }]}
      />

      <ProFormRadio.Group
        label="应用类型"
        name={["data", "display_type"]}
        rules={[{ required: true }]}
        initialValue={SidebarType.DisplayType_DEFAULT}
        fieldProps={{
          options: sidebarTypeOption,
        }}
      />

      <ProFormDependency name={[["data", "display_type"]]}>
        {({ data }) => {
          if (data?.display_type === SidebarType.DisplayType_LIST) {
            return (
              <>
                <ProFormText
                  name={["data", "jump_url"]}
                  label="ICON跳转链接"
                  rules={[{ type: "url" }]}
                />
                <ProFormOssUpload
                  label="图片"
                  name={["data", "pic"]}
                  rules={[
                    // {
                    //   required: true,
                    //   message: "请上传图片",
                    // },
                    {
                      validator(_, value) {
                        if (value && typeof value === "object") {
                          if (!value.url) {
                            return Promise.reject(new Error("请上传图片"));
                          }
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  fieldProps={{
                    md5: true,
                    limit: { aspectRatio: { width: 1, height: 1 }, size: 1 },
                    description: "大小不超过1M，图片比例为1:1",
                  }}
                />
                <ProFormText
                  name={["data", "pic", "description"]}
                  label="图片描述"
                  rules={[{ required: false }]}
                />
                <DndContext
                  sensors={sensors}
                  onDragEnd={handleDragEnd}
                  collisionDetection={closestCenter}
                >
                  <SortableContext
                    items={sidebarLabel.map((item) => item?.key)}
                    strategy={verticalListSortingStrategy}
                  >
                    <ProFormList
                      name={["data", "sidebar_labels"]}
                      label="标签列表"
                      alwaysShowItemLabel={false}
                      creatorButtonProps={{
                        position: "bottom",
                        creatorButtonText: "创建标签",
                      }}
                      max={5}
                      onAfterAdd={(value, insertIndex, count) => {
                        setSidebarLabel((data) => [
                          ...data,
                          { key: String(Date.now()) },
                        ]);
                      }}
                      onAfterRemove={(value, removeIndex) => {
                        setSidebarLabel((data) => {
                          const newData = [...data];
                          newData.splice(removeIndex, 1);
                          return newData;
                        });
                      }}
                      itemRender={({ listDom, action }, { index }) => {
                        const {
                          attributes,
                          listeners,
                          setNodeRef,
                          transform,
                          transition,
                        } = useSortable({
                          id: sidebarLabel[index]?.key ?? index,
                        });
                        // console.log("sidebarLabel", sidebarLabel);
                        const style = {
                          transform: CSS.Transform.toString(transform),
                          transition,
                          paddingBottom: "10px",
                        };

                        return (
                          <>
                            {index === 0 && (
                              <div
                                style={{
                                  display: "flex",
                                  padding: "0 44px 10px 30px",
                                }}
                              >
                                <span style={{ flex: 1 }}>标签内容</span>
                                <span style={{ flex: 1 }}>跳转链接</span>
                              </div>
                            )}
                            <div ref={setNodeRef} style={style} {...attributes}>
                              <div
                                style={{ display: "flex", alignItems: "start" }}
                              >
                                <HolderOutlined
                                  style={{
                                    cursor: "grab",
                                    padding: "0 8px",
                                    color: "#999",
                                    marginTop: 10,
                                  }}
                                  {...listeners}
                                />
                                <div style={{ flex: 1 }}>{listDom}</div>
                                {action}
                              </div>
                            </div>
                          </>
                        );
                      }}
                    >
                      <ProFormGroup key="group">
                        <ProForm.Item
                          name="content"
                          layout="vertical"
                          rules={[
                            { required: true, message: "请输入标签内容" },
                          ]}
                        >
                          <Input></Input>
                        </ProForm.Item>
                        <ProForm.Item
                          name="jump_url"
                          layout="vertical"
                          rules={[
                            { required: true, message: "请输入跳转链接" },
                            { type: "url", message: "请输入正确的链接" },
                          ]}
                        >
                          <Input></Input>
                        </ProForm.Item>
                      </ProFormGroup>
                    </ProFormList>
                  </SortableContext>
                </DndContext>
              </>
            );
          } else if (data?.display_type === SidebarType.DisplayType_DEFAULT) {
            return (
              <ProFormText
                name={["data", "jump_url"]}
                label="ICON跳转链接"
                rules={[{ required: true }, { type: "url" }]}
              />
            );
          }
        }}
      </ProFormDependency>
    </DrawerForm>
  );
}
