import { atom, useAtom, useAtomValue, useSetAtom } from "jotai"
import { Channel } from "@/types/channel"

export const globalDataAtom = atom<{
    hg_channel_name: Channel
}>({
    hg_channel_name: Channel.official,
})

export function useGlobalData() {
    return useAtom(globalDataAtom)
}

export function useGlobalDataValue() {
    return useAtomValue(globalDataAtom)
}

export function useSetGlobalData() {
    return useSetAtom(globalDataAtom)
}
