import { tsToDateTimeRender } from "@/features/renders";
import { BannerConfig, BannerState } from "@/pages/Banner/hooks/useBanner";
import { TableColumnsType } from "antd";
import {
  actionRender,
  stateRender,
  validPeriodRender,
  languageRender,
} from "../renders";
import { useAnnounceTab } from "@/pages/AnnounceTab/hooks";
import {
  AnnouncementConfig,
  AnnouncementState,
} from "@/pages/Announcement/hooks";
import { useLangFiltersList } from "@/hooks/useLangList";
import { Language } from "@/data";

export function useColumns() {
  const { tabs } = useAnnounceTab();

  const columns: TableColumnsType<AnnouncementConfig> = [
    { dataIndex: "id", title: "ID", width: 80 },
    {
      dataIndex: ["data", "content"],
      title: "标题",
      ellipsis: true,
    },
    {
      dataIndex: "language",
      title: "地区",
      render: languageRender,
      filters: useLangFiltersList(),
      filterMultiple: false,
      // defaultFilteredValue: [Language.ZhCN],
    },
    { dataIndex: "description", title: "描述", ellipsis: true, width: 150 },
    {
      dataIndex: "tab_id",
      title: "类别",
      render: (id) => tabs?.find((tab) => tab.id === id)?.name,
      filters: tabs?.map((tab) => ({
        text: tab.name!,
        value: tab.id!,
      })),
      // render: stateRender,
    },
    {
      dataIndex: "create_ts",
      title: "创建时间",
      render: tsToDateTimeRender,
      sorter: true,
      defaultSortOrder: "descend",
    },
    {
      dataIndex: "state",
      title: "状态",
      render: stateRender,
      filters: [
        {
          text: "未发布",
          value: AnnouncementState.Unpublished,
        },
        {
          text: "定时中",
          value: AnnouncementState.Timing,
        },
        {
          text: "已发布",
          value: AnnouncementState.Published,
        },
        {
          text: "自动下线",
          value: AnnouncementState.AutoOffLine,
        },
        {
          text: "手动下线",
          value: AnnouncementState.ManualOffLine,
        },
      ],
    },
    {
      key: "validPeriod",
      title: "有效期",
      width: 200,
      render: validPeriodRender,
    },
    { key: "action", title: "操作", render: actionRender },
  ];
  return columns;
}
