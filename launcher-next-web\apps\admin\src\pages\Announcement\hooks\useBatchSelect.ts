import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { AnnouncementConfig } from "../hooks/useAnnouncement/types";

export const batchSelectAtom = atom<AnnouncementConfig[]>([]);

export function useBatchSelect() {
  return useAtom(batchSelectAtom);
}

export function useBatchSelectValue() {
  return useAtomValue(batchSelectAtom);
}

export function useSetBatchSelect() {
  return useSetAtom(batchSelectAtom);
}
