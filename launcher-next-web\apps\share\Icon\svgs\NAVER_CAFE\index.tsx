import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M6.75 16.814a6.528 6.528 0 0 0 12.663 2.254h.98a3.032 3.032 0 0 0 0-6.054H6.75v3.8Zm12.996.88c.041-.292.062-.586.061-.88V14.39h.586a1.657 1.657 0 0 1 0 3.304h-.647Zm-10.267-6.7h4.911a4.355 4.355 0 0 0 4.356-4.357h-4.922a4.356 4.356 0 0 0-4.345 4.356Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.5}
        height={31.725}
        x={-0.75}
        y={1.012}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50724"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50724"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
