import { atom, useAtom, useAtomValue, useSetAtom } from "jotai"

export const languageAtom = atom<{ lang: string; main_btn_width: number }>({
    lang: "zh-cn",
    main_btn_width: 218,
})

export function useLanguage() {
    return useAtom(languageAtom)
}

export function useLanguageValue() {
    return useAtomValue(languageAtom)
}

export function useSetLanguage() {
    return useSetAtom(languageAtom)
}
