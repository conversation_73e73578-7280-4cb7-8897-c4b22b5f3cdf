import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Flex, Space, Steps, Button } from "antd";
import type { StepsProps } from "antd";

import { InfoList, Title, Tip } from "./renders";
import "./PublishDetail.scss";

interface ButtonConfig {
  text: string;
  disabled: boolean;
  triggerName: string;
}

interface Props {
  infoList: Array<{ label: string; value: string }>;
  stepConfig: StepsProps;
  resultConfig: {
    icon: JSX.Element;
    color: string;
    result_reason: string | JSX.Element;
    extra?: string | JSX.Element;
  };
  buttonConfig: ButtonConfig;
  children?: React.ReactNode;
  onTrigger: (triggerName: string) => void;
  onExit: () => void;
}

export function PublishDetail({
  infoList,
  stepConfig,
  resultConfig,
  buttonConfig,
  children,
  onTrigger,
  onExit,
}: Props) {
  return (
    <div className="publishDetail-container">
      <Title>基础信息</Title>
      <InfoList list={infoList}></InfoList>

      <Title>发布操作</Title>
      <Steps
        style={{ justifyContent: "center" }}
        current={stepConfig.current}
        status={stepConfig.status}
        labelPlacement="vertical"
        items={stepConfig.items}
      />

      {!!resultConfig.result_reason && <Tip resultConfig={resultConfig}></Tip>}

      {children}

      <Flex justify="flex-end" style={{ marginTop: "1rem" }}>
        <Space>
          <Button onClick={onExit}>取消</Button>
          <TriggerButton
            buttonConfig={buttonConfig}
            onTrigger={onTrigger}
          ></TriggerButton>
        </Space>
      </Flex>
    </div>
  );
}

interface TriggerButtonProps {
  buttonConfig: ButtonConfig;
  onTrigger: (triggerName: string) => void;
}

function TriggerButton({ buttonConfig, onTrigger }: TriggerButtonProps) {
  const [loading, setLoading] = useState(false);

  const handleClick = useCallback(async () => {
    const triggerName = buttonConfig.triggerName;
    if (triggerName) {
      setLoading(true);
      await onTrigger(triggerName);
      setLoading(false);
    }
  }, [buttonConfig.triggerName, onTrigger]);

  return buttonConfig.text ? (
    <Button
      type="primary"
      disabled={buttonConfig.disabled}
      loading={loading}
      onClick={handleClick}
    >
      {buttonConfig.text}
    </Button>
  ) : null;
}
