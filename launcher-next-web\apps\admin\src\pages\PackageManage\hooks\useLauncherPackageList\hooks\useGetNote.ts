import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../../const";
import { useLauncherPackageListKey } from "../useLauncherPackageListKey";
import { GetNoteRequest, GetNoteReponse } from "./types";

export function useGetNote() {
  const key = `${URL_PREFIX}/release_notes/list`;
  const { trigger, isMutating } = useSWRMutation(key, getNote);
  return { trigger, isMutating };
}

async function getNote(
  url: any,
  { arg }: { arg: GetNoteRequest },
): Promise<GetNoteReponse> {
  return await fetcher(url, arg);
}
