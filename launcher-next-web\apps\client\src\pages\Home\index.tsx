import { useEffect, useRef, useState } from "react"
import clsx from "clsx"
import { motion, AnimatePresence } from "framer-motion"
import { useSelectedGameValue } from "@/store/useSelectedGame"
import { getQtPath } from "@/utils/misc"
import { GameContent } from "./components/GameContent"
import { usePrevBackgroundImage } from "@/store/usePrevBackgroundImage"
import { useLanguageValue } from "@/store/useLanguage"
import { useMainData } from "@/hooks/useMainData"
import { imagePreload } from "@/utils/imagePreload"

const Home: React.FC = () => {
    const qtPath = getQtPath()
    const [globlePrevBackgroundImage, setGloblePrevBackgroundImage] =
        usePrevBackgroundImage()
    const { lang } = useLanguageValue()
    const selectedGameValue = useSelectedGameValue()
    // console.log(">>>selectedGameValue", selectedGameValue)
    const themeValue = selectedGameValue?.theme || "theme_2"
    const fallbackImage = `${qtPath}/theme/${themeValue}/background.png`

    const { app_code, channel, sub_channel } = selectedGameValue || {}
    const commonParams = {
        appcode: app_code || "",
        language: lang || "zh-cn",
        channel: "1",
        sub_channel: "1",
        platform: "Windows",
    }
    const {
        get_hg_sidebar_rsp,
        get_banner_rsp,
        get_announcement_rsp,
        get_main_bg_image_rsp,
        get_single_ent_rsp,
    } = useMainData(commonParams)

    const mainBgImageUrl = get_main_bg_image_rsp?.main_bg_image?.url

    const [activeBackgroundImage, setActiveBackgroundImage] = useState<
        string | null
    >(navigator.onLine ? mainBgImageUrl || fallbackImage : fallbackImage)
    const [prevBackgroundImage, setPrevBackgroundImage] = useState<
        string | null
    >(globlePrevBackgroundImage)

    const firstRenderRef = useRef(true)
    const [hasAnimation, setHasAnimation] = useState(!!prevBackgroundImage)

    useEffect(() => {
        if (!themeValue) return
        // 是否为在线状态
        const isOnline = navigator.onLine
        if (mainBgImageUrl === activeBackgroundImage) {
            setGloblePrevBackgroundImage(mainBgImageUrl)
            return
        }

        let timeout: NodeJS.Timeout | null = null
        const nextFallback = `${qtPath}/theme/${themeValue}/background.png`
        if (mainBgImageUrl && isOnline) {
            imagePreload(mainBgImageUrl, nextFallback).then((res: string) => {
                console.log("===preload mainImage success===", res)
                if (!firstRenderRef.current && !hasAnimation)
                    setHasAnimation(true)
                if (timeout) {
                    clearTimeout(timeout)
                }
                setPrevBackgroundImage(activeBackgroundImage)
                setActiveBackgroundImage(res)
                setGloblePrevBackgroundImage(res)
            })
            // 短时间未加载成功，使用兜底图，避免无图渲染
            timeout = setTimeout(() => {
                console.log("===preload mainImage timeout===")
                setFallbackImage(nextFallback)
            }, 200)
        } else {
            setFallbackImage(nextFallback)
        }
    }, [themeValue, mainBgImageUrl])

    useEffect(() => {
        if (firstRenderRef.current) {
            firstRenderRef.current = false
        }
    }, [])

    const setFallbackImage = (nextFallback: string) => {
        if (!firstRenderRef.current && !hasAnimation) setHasAnimation(true)
        // 如果从全部游戏页切换到游戏主页，此时游戏没返回 mainBgImageUrl，需要执行这里，否则会导致背景图立即切换
        if (activeBackgroundImage === nextFallback) return
        // 针对切换游戏后接口还没返回响应的情况，先切换成兜底的背景图
        setPrevBackgroundImage(activeBackgroundImage)
        setActiveBackgroundImage(nextFallback)
        setGloblePrevBackgroundImage(nextFallback)
    }

    return (
        <div className="relative h-full">
            {/* 背景图层 - 旧图淡出 */}
            {prevBackgroundImage && (
                <div
                    key={`prev-${prevBackgroundImage}`}
                    onAnimationEnd={() => setPrevBackgroundImage(null)} // 动画结束后移除
                    className="prev-fade absolute inset-0 rounded-2xl z-[-10]"
                    style={{
                        backgroundImage: `url(${prevBackgroundImage})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                    }}
                />
            )}
            {activeBackgroundImage && (
                <div
                    key={`active-${activeBackgroundImage}`}
                    className={clsx(
                        "absolute inset-0 rounded-2xl z-[-10]",
                        hasAnimation && "active-fade"
                    )}
                    style={{
                        backgroundImage: `url(${activeBackgroundImage})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                    }}
                />
            )}
            <GameContent
                // key={selectedGameValue?.uuid}
                game={selectedGameValue}
                contentData={{
                    get_announcement_rsp,
                    get_banner_rsp,
                    get_single_ent_rsp,
                    get_hg_sidebar_rsp,
                }}
            />
        </div>
    )
}

export default Home
