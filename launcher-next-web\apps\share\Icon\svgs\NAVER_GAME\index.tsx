import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M23.556 11.573a5.814 5.814 0 0 0-5.375-3.59h-6.244a5.816 5.816 0 1 0 0 11.629h5.2v2.007a.397.397 0 0 0 .462.394.41.41 0 0 0 .206-.095l4.373-3.9a5.816 5.816 0 0 0 1.38-6.45l-.002.005Zm-10.337 3.01h-1.242v1.243a.754.754 0 0 1-1.059.798.752.752 0 0 1-.43-.798v-1.252H9.248a.752.752 0 0 1 0-1.49h1.242v-1.24a.753.753 0 1 1 1.488 0v1.24h1.242a.753.753 0 0 1 0 1.49v.01Zm4.516-.145a.841.841 0 0 1-1.424-.76.847.847 0 0 1 .23-.434.828.828 0 0 1 .424-.243.838.838 0 0 1 .865.361.84.84 0 0 1-.105 1.066l.01.01Zm.404-2.599a.846.846 0 0 1 .662-.665.84.84 0 0 1 1.008.738.838.838 0 0 1-.062.406.846.846 0 0 1-1.25.381.85.85 0 0 1-.358-.87v.01Zm1.608 4.16a.842.842 0 0 1-1.254.379.858.858 0 0 1-.374-.747.848.848 0 0 1 .68-.789.83.83 0 0 1 .486.05.82.82 0 0 1 .462 1.097v.01Zm1.646-1.56a.84.84 0 0 1-1.377-.276.85.85 0 0 1 .183-.921.831.831 0 0 1 .421-.241.84.84 0 0 1 1.009.83.846.846 0 0 1-.246.597l.01.01Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={29.037}
        x={-1.5}
        y={2.356}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50715"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50715"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
