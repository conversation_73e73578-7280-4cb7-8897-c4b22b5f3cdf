export function imagePreload(
    url?: string,
    fallbackImage?: string
): Promise<string>
export function imagePreload(
    url?: (string | undefined)[],
    fallbackImage?: string
): Promise<string[]>

export function imagePreload(
    url?: string | Array<string | undefined>,
    fallbackImage?: string
): Promise<string | (string | undefined)[] | undefined> {
    const urlList = typeof url === "string" ? [url] : url || []
    if (!urlList.length) return Promise.resolve(undefined)
    return Promise.all(
        urlList.map((item) => {
            return new Promise<string | undefined>((resolve, reject) => {
                if (!item) return resolve(undefined)
                const img = new Image()
                img.src = item
                img.onload = () => resolve(item)
                img.onerror = () => resolve(fallbackImage || item)
            })
        })
    ).then((resList) => {
        const res = typeof url === "string" ? resList[0] : resList
        console.log("preload image success", res)
        return res
    })
}
