// import { useAppCode } from "@/hooks";
import { useUrlParams } from "../useUrlParams";
import { URL_PREFIX } from "../../const";
import { usePageValue, usePageSizeValue, useSearchValue } from "../index";

export function useLauncherPackageListKey() {
  const URL = URL_PREFIX + "/list";
  const searchParams = useUrlParams();
  const appCode = searchParams.get("appCode");

  const page = usePageValue();
  const pageSize = usePageSizeValue();
  const searchVal: { [key: string]: any } = useSearchValue();

  return [URL, appCode, { page, pageSize, searchVal }] as const;
}
