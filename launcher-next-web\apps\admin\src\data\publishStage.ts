export enum PublishStage {
  PublishStage_RESERVE = "PublishStage_RESERVE",
  PublishStage_UNSYNC = "PublishStage_UNSYNC", // 未同步
  PublishStage_SYNCING = "PublishStage_SYNCING", // 同步中
  PublishStage_SYNC_FAIL = "PublishStage_SYNC_FAIL", // 同步失败
  PublishStage_UNPREWARM = "PublishStage_UNPREWARM", // 未预热
  PublishStage_PREWARMING = "PublishStage_PREWARMING", // 预热中
  PublishStage_PREWARM_FAIL = "PublishStage_PREWARM_FAIL", // 预热失败
  PublishStage_UNGRAYPUBLISH = "PublishStage_UNGRAYPUBLISH", // 未灰度发布
  PublishStage_UNPUBLISH = "PublishStage_UNPUBLISH", // 未上线
  PublishStage_PUBLISH_FINISH = "PublishStage_FINISH", // 发布完成
}
