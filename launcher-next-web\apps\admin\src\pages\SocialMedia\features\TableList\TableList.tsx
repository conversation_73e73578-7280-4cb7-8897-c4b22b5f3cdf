import { Table } from "antd";
import type { TableColumnsType, TableProps } from "antd";
import { SidebarConfig } from "../../hooks/useSidebar/types";
import { useColumns, useOnTableChange } from "./hooks";
import {
  useSidebar,
  usePage,
  useSetSort,
  useSort,
  useBatchSelect,
} from "../../hooks";
import { useCallback, useState } from "react";

export function TableList() {
  const [page, setPage] = usePage();
  const columns = useColumns();
  const { sidebars, total } = useSidebar();
  const onTableChange = useOnTableChange();
  const [batchSelecte, setBatchSelect] = useBatchSelect();

  const onSelectChange = useCallback(
    (selectedRowKeys: React.Key[], selectedRows: SidebarConfig[]) => {
      console.log("selectedRows changed: ", selectedRows);
      setBatchSelect(selectedRows);
    },
    [],
  );

  const rowSelection = {
    preserveSelectedRowKeys: true,
    selectedRowKeys: batchSelecte.map((item) => item.id!),
    onChange: onSelectChange,
  };

  return (
    <div>
      <Table<SidebarConfig>
        rowKey={(record) => record.id || ""}
        columns={columns}
        dataSource={sidebars}
        rowSelection={rowSelection}
        onChange={onTableChange}
        pagination={{
          current: page,
          total: Number(total),
          onChange(page) {
            setPage(page);
          },
        }}
      />
    </div>
  );
}
