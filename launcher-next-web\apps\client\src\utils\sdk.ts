import { adapter } from "@hg/adapt-sdk"
import { getSDKReadyFunc } from "@hg/hg-web-sdk"

adapter.init()
export { adapter }

export const source = {
    source_from: adapter.source.from || "",
    share_type: adapter.share.type || "",
    share_by: (() => {
        const share_by = adapter.share.by || ""
        if (!share_by) {
            return ""
        }

        try {
            return window.atob(share_by)
        } catch (e) {
            return ""
        }
    })(),
}

const ETL_OPTION = {
    domain: __SDK_DOMAIN,
    sub_domain: __SDK_SUB_DOMAIN,
    pageProperties: {
        language: document.documentElement.lang,
        source: adapter.source.from || "",
        share_type: adapter.share.type || "",
        share_by: adapter.share.by ? window.atob(adapter.share.by) : "",
    },
}

export const sdkReady = getSDKReadyFunc(__SDK_SRC, {
    etl: ETL_OPTION,
})

if (__SDK_HOST) {
    sdkReady((sdk) => {
        sdk.hostConfig.u8Server.setHost(__SDK_HOST)
        sdk.hostConfig.hgServer.setHost(__SDK_HOST)
    })
}

export async function collect(
    event: string,
    properties: Record<string, unknown>
) {
    const sdk = await sdkReady()
    sdk.ETL.event(event, properties)
}
