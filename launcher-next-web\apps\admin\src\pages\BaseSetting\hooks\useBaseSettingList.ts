import { fetcher } from "@/utils/fetcher";
import useS<PERSON> from "swr";
import { message } from "antd";
import { useAppCode } from "@/hooks";
import { ListLauncherAppReply } from "./types";
import { URL_PREFIX } from "./const";

export function useBaseSettingList() {
  const key = useBaseSettingListLey();
  const { data, isLoading } = useSWR(
    key,
    ([url, appCode]) => {
      return fetcher<ListLauncherAppReply>(
        url,
        { appcode: appCode },
        {
          method: "GET",
        },
      );
    },
    {
      onError(err, key, config) {
        message.error(err?.message || "请求错误");
      },
    },
  );

  return {
    data,
    isLoading,
  };
}

export function useBaseSettingListLey() {
  const url = URL_PREFIX + "/launcher/list";
  const appCode = useAppCode();
  return [url, appCode] as const;
}
