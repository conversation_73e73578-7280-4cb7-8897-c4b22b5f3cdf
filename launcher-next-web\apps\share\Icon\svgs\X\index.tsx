import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M19.745 7.543h2.53l-5.528 6.317 6.503 8.597h-5.092l-3.988-5.214-4.563 5.214H7.076l5.912-6.757L6.75 7.543h5.22l3.605 4.766 4.17-4.766Zm-.888 13.4h1.402l-9.05-11.965H9.705l9.152 11.965Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.5}
        height={29.915}
        x={-0.75}
        y={1.918}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50670"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50670"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
