import { Flex, Modal, Select, Space, message, Tabs, Typography } from "antd";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { cloneDeep } from "lodash";
import { Preview, Sort } from "./features";
import {
  usePublishedBanner,
  usePublishBanner,
  type BannerConfig,
  BannerConfigOrder,
  BannerState,
} from "../../hooks/useBanner";
import { useSetBatchSelect } from "../../hooks/useBatchSelect";
import { Language } from "@/data";
import { useAppCode, useLangList, useLangListMap } from "@/hooks";
import { compareOrder } from "@/utils/compareOrder";

interface Props {
  trigger: JSX.Element;
  bannersToPublish?: BannerConfig[];
}

// 根据选中发布的bananer锚定到对应的语言
function getInitalLanguage(bannersToPublish: BannerConfig[]) {
  return (bannersToPublish?.[0]?.languages?.[0] as Language) || Language.ZhCN;
}

export function Publish({ trigger, bannersToPublish }: Props) {
  const appcode = useAppCode();
  const [isOpen, setIsOpen] = useState(false);
  const [language, setLanguage] = useState(() =>
    getInitalLanguage(bannersToPublish || []),
  );
  const curOrderRef = useRef<any>();
  const publishBannersRef = useRef<Record<string, BannerConfig[]>>({});

  const [allBanners, setAllBanners] =
    useState<Record<Language, BannerConfig[]>>();

  const { orders, mutate, isLoading } = usePublishedBanner();
  const publishBanner = usePublishBanner();
  const langList = useLangList();
  const langTabsList = langList.map((item) => ({ ...item, key: item.value }));
  const langListMap = useLangListMap();
  const setBatchSelect = useSetBatchSelect();

  const getAllBanners = useCallback(
    (data: BannerConfigOrder[]) => {
      const bannersLangMap: Record<string, BannerConfig[]> = {};

      data?.forEach(
        (order) =>
          (bannersLangMap[order.language!] = order?.banner_configs || []),
      );
      curOrderRef.current = cloneDeep(bannersLangMap);
      if (!bannersToPublish) {
        return bannersLangMap;
      }

      bannersToPublish
        .filter((item) => {
          return item?.state === BannerState.Unpublished;
        })
        .forEach((banner) => {
          banner.languages?.forEach((lang) => {
            publishBannersRef.current[lang] = [
              banner,
              ...(publishBannersRef.current[lang] || []),
            ];
            bannersLangMap[lang] = [banner, ...(bannersLangMap[lang] || [])];
          });
        });
      return bannersLangMap;
    },
    [bannersToPublish],
  );

  const handleLanguageChange = useCallback(
    (value: Language) => {
      setLanguage(value);
    },
    [orders],
  );

  // console.log(allBanners);

  return (
    <div>
      {React.cloneElement(trigger, {
        onClick: () => {
          publishBannersRef.current = {};
          setAllBanners(getAllBanners(orders || []));
          setIsOpen(true);
        },
        loading: isLoading,
      })}
      <Modal
        width={1200}
        open={isOpen}
        onCancel={() => setIsOpen(false)}
        onOk={async () => {
          try {
            const compareRes = compareOrder(curOrderRef.current, allBanners!);
            const updateLangTextNode = compareRes.map((lang, index) => {
              const langText = langListMap[lang as Language];
              return (
                <Typography.Link key={lang}>
                  {langText}
                  {index < compareRes.length - 1 ? "、" : ""}
                </Typography.Link>
              );
            });

            Modal.confirm({
              title: "提示",
              content: updateLangTextNode.length ? (
                <Typography.Paragraph>
                  本次操作将涉及 {updateLangTextNode} 的内容变更，是否确认执行？
                </Typography.Paragraph>
              ) : (
                <span>本次操作无内容变更，是否确认执行？</span>
              ),
              onOk: async () => {
                const params = {
                  appcode: appcode,
                  orders: Object.keys(allBanners!)?.map((key) => ({
                    language: key,
                    publish_ids: publishBannersRef.current?.[key]?.map(
                      (ele) => ele.id!,
                    ),
                    order_ids: allBanners?.[key as Language]?.map(
                      (ele) => ele.id!,
                    ),
                  })),
                };
                try {
                  await publishBanner(params);
                  message.success("执行成功");
                  setBatchSelect([]);
                  setIsOpen(false);
                } catch (e: any) {
                  message.error(e.message || "请求错误");
                  return Promise.reject();
                }
              },
              onCancel() {},
            });
          } catch (error) {
            console.error(error);
            message.error("发布失败");
          }
        }}
        okText="发布"
        cancelText="取消"
        title={
          <Space>
            设置排序
            {/* <Select
              style={{ width: 200 }}
              options={langList}
              value={language}
              onChange={handleLanguageChange}
            /> */}
          </Space>
        }
      >
        <Tabs
          items={langTabsList}
          activeKey={language}
          onChange={(val) => handleLanguageChange(val as Language)}
        ></Tabs>
        <Flex justify="space-around">
          <Sort
            banners={allBanners?.[language] || []}
            setBanners={(val) =>
              setAllBanners((prev) => {
                const newData = cloneDeep(prev)!;
                newData[language] = val;
                return newData;
              })
            }
            language={language}
          />
          <Preview banners={allBanners?.[language] || []} />
        </Flex>
      </Modal>
    </div>
  );
}
