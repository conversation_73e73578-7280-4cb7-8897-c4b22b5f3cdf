import { tsToDateTimeRender } from "@/features/renders";
import { BannerConfig, BannerState } from "@/pages/Banner/hooks/useBanner";
import { TableColumnsType } from "antd";
import {
  actionRender,
  stateRender,
  validPeriodRender,
  languageRender,
} from "../renders";
import { useLangFiltersList } from "@/hooks/useLangList";

export function useColumns() {
  const columns: TableColumnsType<BannerConfig> = [
    { dataIndex: "id", title: "ID", width: 80 },
    { dataIndex: "description", title: "描述", ellipsis: true, width: 150 },
    {
      dataIndex: "create_ts",
      title: "创建时间",
      render: tsToDateTimeRender,
      sorter: true,
      defaultSortOrder: "descend",
    },
    {
      dataIndex: "languages",
      title: "地区",
      render: languageRender,
      filters: useLangFiltersList(),
    },
    {
      dataIndex: "state",
      title: "状态",
      render: stateRender,
      filters: [
        {
          text: "未发布",
          value: BannerState.Unpublished,
        },
        {
          text: "定时中",
          value: BannerState.Timing,
        },
        {
          text: "已发布",
          value: BannerState.Published,
        },
        {
          text: "自动下线",
          value: BannerState.AutoOffLine,
        },
        {
          text: "手动下线",
          value: BannerState.ManualOffLine,
        },
      ],
    },
    {
      key: "validPeriod",
      title: "有效期",
      width: 300,
      render: validPeriodRender,
    },
    { key: "action", title: "操作", render: actionRender },
  ];
  return columns;
}
