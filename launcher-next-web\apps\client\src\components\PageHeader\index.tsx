import React, { useEffect, useState } from "react"
import { useLocation } from "react-router-dom"
import { fetchQt, fetchQtCallback } from "@/utils/qwebchannel"
import { useSelectedGameValue } from "@/store/useSelectedGame"
import { useAllGameInfo } from "@/store/useAllGameInfo"
import { useGlobalDataValue } from "@/store/useGlobalData"
import { GameListResponse } from "@/types/games"
import { UserInfo } from "@/types/user"
import { Channel } from "@/types/channel"
import { Tooltip } from "../Tooltip"
import {
    CloseIcon,
    MinimizeIcon,
    SettingIcon,
    NotLoginIcon,
    LoginIcon,
} from "../Icon"
import { Dropdown, DropdownItem, DropdownSeparator } from "../Dropdown"
import "./index.scss"
import {
    eventBus,
    useOnUserInfoNotify,
    useOnUserLogoutNotify,
} from "@/utils/eventBus"
import { useLocale } from "@/hooks/useLocale"
import { logout_all_game_list_rsp } from "@/utils/mockData"
import bridge from "@/utils/bridge"

export const PageHeader: React.FC = () => {
    const t = useLocale()
    const location = useLocation()
    const globalData = useGlobalDataValue()
    const isOfficial = globalData.hg_channel_name === Channel.official
    const selectedGame = useSelectedGameValue()
    const [allGameInfo, setAllGameInfo] = useAllGameInfo()
    const isAllGamePage = location.pathname.includes("/all_games")
    const uuid = isAllGamePage ? allGameInfo.uuid : selectedGame?.uuid

    const [userInfo, setUserInfo] = useState<UserInfo | null>(null)

    const fetchAllGamesList = async () => {
        try {
            const res: GameListResponse = await fetchQtCallback({
                event: "all_game_list_req",
                callbackEvent: "all_game_list_rsp",
                mockData: logout_all_game_list_rsp,
            })
            // const allGameItem = res.items?.find((item) => !item.is_game)
            const allGamesList = res?.items?.filter((item) => item.is_game)
            console.log("allGamesList", allGamesList)
            setAllGameInfo((prev) => ({
                ...prev,
                firstTheme: allGamesList[0]?.theme || "",
                isSingleGame: allGamesList.length === 1,
                allGamesList,
            }))
        } catch (error) {
            console.error("fetchAllGamesList error", error)
        }
    }

    useOnUserInfoNotify((data) => {
        if (data?.hg_id) {
            setUserInfo(data)
        }
    })

    useOnUserLogoutNotify(() => {
        setUserInfo(null)
    })

    const handleLogin = () => {
        fetchQt({
            event: "open_dialog",
            data: { dialog_name: "login_dialog", uuid },
        })
        // Mock
        // eventBus.emit("user_info_notify", {
        //     phone: "130****8888",
        //     hg_id: "12345678901",
        // })
    }

    const handleLoginOut = async () => {
        try {
            const res: { result: boolean } = await fetchQtCallback({
                event: "logout_req",
                callbackEvent: "logout_rsp",
                mockData: { result: true },
            })
            if (res?.result) {
                setUserInfo(null)
            }
        } catch (error) {
            console.error("logout error", error)
        }
    }

    const handleSetting = () => {
        fetchQt({
            event: "open_dialog",
            data: { dialog_name: "launcher_setting", uuid },
        })
    }
    const handleMinimize = () => {
        fetchQt({ event: "min_mainwnd" })
    }
    const handleClose = () => {
        fetchQt({ event: "close_mainwnd" })
    }

    const handleJumpUserCenter = () => {
        bridge.open({ type: "personal_center" }, "user_center")
    }

    const handleToggleLanguage = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const lang = e.target.value
        eventBus.emit("lang_changed_notify", {
            lang: lang,
            main_btn_width: lang === "en-us" ? 260 : 218,
        })
    }

    const handleOpenDialog = () => {
        eventBus.emit("show_mark_wnd_notify", {
            show: true,
        })
    }

    const handleCloseDialog = () => {
        eventBus.emit("show_mark_wnd_notify", {
            show: false,
        })
    }

    const handleAllGamesListChange = () => {
        eventBus.emit("all_game_list_notify", logout_all_game_list_rsp)
    }

    return (
        <div className="header-container fixed top-3 right-3 p-4 flex items-center z-40">
            {/* <div className="header-icon_login gap-1" onClick={handleOpenDialog}>
                <span>打开弹窗蒙层</span>
            </div>
            <div
                className="header-icon_login gap-1"
                onClick={handleCloseDialog}
            >
                <span>关闭弹窗蒙层</span>
            </div> */}

            <select onChange={handleToggleLanguage}>
                <option value="zh-cn">简体中文</option>
                <option value="en-us">英文</option>
                <option value="zh-tw">繁體中文</option>
                <option value="ja-jp">日语</option>
                <option value="ko-kr">韩语</option>
            </select>

            {isOfficial ? (
                <>
                    {userInfo ? (
                        <Dropdown
                            placement="bottom-center"
                            trigger={
                                <div className="cursor-pointer bg-white rounded-full">
                                    <LoginIcon />
                                </div>
                            }
                        >
                            <DropdownItem className="py-2" closeOnClick={false}>
                                <div className="flex items-center gap-2 w-[188px] h-[42px]">
                                    <div className="w-[42px] h-[42px] rounded-full bg-white/[0.05] flex items-center justify-center">
                                        <NotLoginIcon />
                                    </div>
                                    <div>
                                        <p className="text-white text-[16px] font-medium font-sans leading-[24px] truncate">
                                            {userInfo?.phone}
                                        </p>
                                        <p className="text-white/[0.35] text-[12px] font-sans truncate">
                                            ID:{userInfo?.hg_id}
                                        </p>
                                    </div>
                                </div>
                            </DropdownItem>
                            <DropdownSeparator />
                            <DropdownItem onClick={handleJumpUserCenter}>
                                个人中心
                            </DropdownItem>
                            <DropdownItem onClick={handleLoginOut}>
                                退出登录
                            </DropdownItem>
                        </Dropdown>
                    ) : (
                        <div
                            className="header-icon_login gap-1"
                            onClick={handleLogin}
                        >
                            <NotLoginIcon />
                            <span>{t["header.login"]}</span>
                        </div>
                    )}
                    <div className="w-[1px] h-[1.5rem] bg-white/[0.35] mx-5"></div>
                </>
            ) : null}
            <div className="flex gap-3">
                <Tooltip
                    title={t["header.settings"]}
                    placement="bottom-center"
                    offset={8}
                    containerClassName="whitespace-nowrap"
                >
                    <div className="header-icon" onClick={handleSetting}>
                        <SettingIcon />
                    </div>
                </Tooltip>
                <Tooltip
                    title={t["header.minimize"]}
                    placement="bottom-center"
                    offset={8}
                    containerClassName="whitespace-nowrap"
                >
                    <div className="header-icon" onClick={handleMinimize}>
                        <MinimizeIcon />
                    </div>
                </Tooltip>
                <Tooltip
                    title={t["header.close"]}
                    placement="bottom-center"
                    offset={8}
                    containerClassName="whitespace-nowrap"
                >
                    <div className="header-icon" onClick={handleClose}>
                        <CloseIcon />
                    </div>
                </Tooltip>
            </div>
        </div>
    )
}
