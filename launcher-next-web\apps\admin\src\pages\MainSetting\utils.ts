import { type MainPageConfig, MainPageState } from "./hooks/useMainData";

export function getMainPageStatus(mainPage?: MainPageConfig) {
  switch (mainPage?.state) {
    case MainPageState.Timing:
      return { text: "定时中", color: "blue" };
    case MainPageState.Published:
      return { text: "已上线", color: "green" };
    case MainPageState.AutoOffLine:
      return { text: "自动下线", color: undefined };
    case MainPageState.ManualOffLine:
      return { text: "手动下线", color: undefined };
    case MainPageState.Unpublished:
    default:
      return { text: "未发布", color: undefined };
  }
}
