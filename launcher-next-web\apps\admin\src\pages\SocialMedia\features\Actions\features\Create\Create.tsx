import { PlusOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { EditForm } from "../../../EditForm";
import { useCreateSidebar } from "@/pages/SocialMedia/hooks";
import { useAppCode } from "@/hooks";
import { Language } from "@/data";

export function Create() {
  const createSidebar = useCreateSidebar();
  const appcode = useAppCode();
  return (
    <EditForm
      title="新建社媒配置"
      trigger={
        <Button icon={<PlusOutlined />} type="primary">
          新建
        </Button>
      }
      onFinish={async (values) => {
        await createSidebar({ appcode, ...values });
      }}
    />
  );
}
