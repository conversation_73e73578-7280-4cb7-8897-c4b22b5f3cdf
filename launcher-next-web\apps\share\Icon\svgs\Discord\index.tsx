import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M21.261 9.301a.046.046 0 0 0-.023-.021 14.848 14.848 0 0 0-3.664-1.136.055.055 0 0 0-.059.028c-.168.304-.32.617-.456.936a13.71 13.71 0 0 0-4.115 0 9.468 9.468 0 0 0-.464-.936.057.057 0 0 0-.058-.028c-1.266.218-2.497.6-3.664 1.136a.052.052 0 0 0-.024.02C6.4 12.785 5.76 16.184 6.074 19.541a.062.062 0 0 0 .024.043 14.93 14.93 0 0 0 4.494 2.27.058.058 0 0 0 .064-.02c.347-.472.654-.972.92-1.495a.056.056 0 0 0 .001-.047.055.055 0 0 0-.033-.032 9.834 9.834 0 0 1-1.404-.67.058.058 0 0 1-.006-.095 7.72 7.72 0 0 0 .279-.219.056.056 0 0 1 .058-.007c2.946 1.344 6.135 1.344 9.046 0a.056.056 0 0 1 .059.007c.091.075.185.148.28.219a.057.057 0 0 1 .014.076.058.058 0 0 1-.02.02 9.227 9.227 0 0 1-1.404.668.056.056 0 0 0-.02.013.056.056 0 0 0-.017.044c0 .008.003.016.006.023.27.52.577 1.02.919 1.494a.057.057 0 0 0 .063.021 14.88 14.88 0 0 0 4.502-2.27.058.058 0 0 0 .023-.042c.376-3.88-.628-7.252-2.66-10.24Zm-9.246 8.196c-.887 0-1.618-.814-1.618-1.813 0-1 .717-1.814 1.618-1.814.908 0 1.632.821 1.618 1.814 0 1-.717 1.813-1.618 1.813Zm5.981 0c-.887 0-1.618-.814-1.618-1.813 0-1 .717-1.814 1.618-1.814.908 0 1.632.821 1.618 1.814 0 1-.71 1.813-1.618 1.813Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={28.714}
        x={-1.5}
        y={2.518}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50703"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50703"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
