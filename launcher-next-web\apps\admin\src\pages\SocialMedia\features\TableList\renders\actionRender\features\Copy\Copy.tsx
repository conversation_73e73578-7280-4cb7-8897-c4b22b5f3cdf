import { Language } from "@/data";
import { useAppCode } from "@/hooks";
import { EditForm } from "@/pages/SocialMedia/features/EditForm";
import { useCreateSidebar } from "@/pages/SocialMedia/hooks";
import { Typography } from "antd";
import { SidebarActionProps } from "../types";

export function Copy({ sidebarConfig }: SidebarActionProps) {
  const createSidebar = useCreateSidebar();
  const appcode = useAppCode();
  return (
    <EditForm
      title="复制 Sidebar"
      trigger={<Typography.Link>复制</Typography.Link>}
      initialValues={sidebarConfig}
      onFinish={async (values) => {
        await createSidebar({ appcode, ...values });
      }}
    />
  );
}
