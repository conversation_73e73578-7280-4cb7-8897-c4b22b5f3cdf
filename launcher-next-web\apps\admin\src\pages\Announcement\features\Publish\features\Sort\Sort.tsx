import React, { useContext, useMemo } from "react";
import { HolderOutlined } from "@ant-design/icons";
import type { DragEndEvent } from "@dnd-kit/core";
import { DndContext } from "@dnd-kit/core";
import type { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import { Language } from "@/data";
import { stateRender } from "../../../AnnouncementList/renders";
import { AnnouncementConfig } from "@/pages/Announcement/hooks";

interface Props {
  announcementList: AnnouncementConfig[];
  setAnnouncementList: (values: AnnouncementConfig[]) => void;
  language: Language;
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: "move" }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

const columns: ColumnsType<AnnouncementConfig> = [
  { key: "sort", align: "center", width: 80, render: () => <DragHandle /> },
  { title: "", dataIndex: "description" },
  { title: "", dataIndex: "state", render: stateRender },
  // { title: "Address", dataIndex: "address" },
];

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}

const Row: React.FC<RowProps> = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

export function Sort({ announcementList, setAnnouncementList }: Props) {
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = announcementList.findIndex(
        (record) => record.id === active?.id,
      );
      const overIndex = announcementList.findIndex(
        (record) => record.id === over?.id,
      );
      const result = arrayMove(announcementList, activeIndex, overIndex);
      setAnnouncementList(result);
    }
  };

  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext
        items={announcementList?.map((i) => i.id!)}
        strategy={verticalListSortingStrategy}
      >
        <Table
          rowKey="id"
          title={() => "拖动以修改顺序"}
          showHeader={false}
          style={{ width: 600 }}
          components={{ body: { row: Row } }}
          columns={columns}
          dataSource={announcementList}
          pagination={false}
        />
      </SortableContext>
    </DndContext>
  );
}
