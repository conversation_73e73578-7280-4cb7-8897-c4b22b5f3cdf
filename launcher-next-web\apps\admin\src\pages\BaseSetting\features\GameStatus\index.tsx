import React from "react";
import { Space, Tag } from "antd";
import { AppGameState, AppGameStateTagConfig } from "../../hooks/types";

interface Props {
  state: AppGameState;
}

export const GameStatus: React.FC<Props> = ({ state }) => {
  const { text, color } = AppGameStateTagConfig?.[state] || {};
  return (
    <Space size={30}>
      <span>游戏状态：</span>
      <Tag
        bordered={false}
        color={color}
        style={{ fontSize: "15px", padding: "5px 10px" }}
      >
        {text}
      </Tag>
    </Space>
  );
};
