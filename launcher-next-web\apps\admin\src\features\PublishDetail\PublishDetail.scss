.publishDetail-container {
  padding: 0 2.5rem;
  .paragraph-title {
    margin: 1.5rem 0;
    font-size: 18px;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      left: -20px;
      width: 8px;
      height: 100%;
      background-color: #58afff;
    }
  }

  .info-label {
    flex: 1;
    color: rgba(0, 0, 0, 0.45);
    text-align: right;
  }

  .info-value {
    flex: 1
  }

  .publish-tip {
    width: 100%;
    padding: 0.8rem;
    margin: 1rem auto;
  }
}
