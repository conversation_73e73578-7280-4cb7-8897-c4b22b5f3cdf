import { Card, ConfigProvider, <PERSON>lex, But<PERSON> } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import zhCN from "antd/locale/zh_CN";
import { Provider } from "jotai";
import { Page } from "@hg-omc/layouts";
import { Search, TableList } from "./features";
import { useLocation, useNavigate } from "@hg/lego-app";

export function PackageManage() {
  const nav = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const caption = searchParams.get("caption") || "";
  return (
    <ConfigProvider locale={zhCN}>
      <Provider>
        <Page>
          <Flex align="center" gap={15}>
            <Button icon={<ArrowLeftOutlined />} onClick={() => nav(-1)} />
            <span
              style={{ color: "rgba(0,0,0,0.45)", fontWeight: 500 }}
            >{`启动器管理 > 包体管理>${caption}`}</span>
          </Flex>
          <br />
          <Card title="包体管理" style={{ marginBottom: "10px" }}>
            <Search />
          </Card>
          <Card>
            <TableList />
          </Card>
        </Page>
      </Provider>
    </ConfigProvider>
  );
}
