import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M24 15.055a9 9 0 1 0-10.407 8.89v-6.289H11.31v-2.602h2.284v-1.982c0-2.256 1.344-3.502 3.4-3.502.984 0 2.014.176 2.014.176v2.214h-1.134c-1.118 0-1.466.694-1.466 1.406v1.69h2.496l-.4 2.6h-2.096v6.29C20.709 23.27 24 19.546 24 15.055Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={32.89}
        x={-1.5}
        y={0.43}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50680"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50680"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
