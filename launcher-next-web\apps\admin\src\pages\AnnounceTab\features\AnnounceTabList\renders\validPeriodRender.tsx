import {
  BannerConfig,
  BannerState,
  TimerKind,
} from "@/pages/Banner/hooks/useBanner/types";
import { getDateByTs, isCurrentAfter, isCurrentBefore } from "@/utils/time";
import { Tag } from "antd";
import dayjs from "dayjs";

export function validPeriodRender(_: any, record: BannerConfig) {
  const color = getTagColor();
  if (record.timer_kind === TimerKind.LongTime) {
    return <Tag color={color}>{getDateByTs(record.start_ts || "")}-永久</Tag>;
  }
  return (
    <Tag color={color}>
      {getDateByTs(record.start_ts || "")}-{getDateByTs(record.end_ts || "")}
    </Tag>
  );

  function getTagColor() {
    if (isCurrentBefore(record.start_ts)) {
      return "blue";
    }
    if (isCurrentAfter(record.start_ts)) {
      if (
        isCurrentBefore(record.end_ts) ||
        record.timer_kind === TimerKind.LongTime
      ) {
        return "green";
      }
    }
    return "orange";
  }
}
