import { SorterResult } from "antd/es/table/interface";
import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";

export interface Sort {
  field: string;
  order: "asc" | "desc";
}

export const sortAtom = atom<Sort[]>([
  {
    field: "create_ts",
    order: "desc",
  },
]);

export function useSort() {
  return useAtom(sortAtom);
}

export function useSortValue() {
  return useAtomValue(sortAtom);
}

export function useSetSort() {
  return useSetAtom(sortAtom);
}
