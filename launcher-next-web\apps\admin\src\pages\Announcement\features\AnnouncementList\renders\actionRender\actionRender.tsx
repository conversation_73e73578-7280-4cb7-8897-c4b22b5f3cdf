import { Dropdown, Space, Typography } from "antd";
import { CancelPublish, Copy, Edit, Remove } from "./features";
import {
  AnnouncementConfig,
  AnnouncementState,
} from "@/pages/Announcement/hooks";
import { Publish } from "../../../Publish";

export function actionRender(_: any, announcement: AnnouncementConfig) {
  const showPublishButton = [
    AnnouncementState.Unpublished,
    // AnnouncementState.AutoOffLine,
    // AnnouncementState.ManualOffLine,
  ].includes(announcement.state!);
  const showCancelPublishButton = [
    AnnouncementState.Timing,
    AnnouncementState.Published,
  ].includes(announcement.state!);
  const showEditButton =
    [AnnouncementState.AutoOffLine, AnnouncementState.ManualOffLine].includes(
      announcement.state!,
    ) === false;
  return (
    <Space align="center">
      {showEditButton && <Edit announcement={announcement} />}

      {showPublishButton && (
        <Publish
          dataToPublish={[announcement]}
          trigger={<Typography.Link type="success">发布</Typography.Link>}
        />
      )}
      {showCancelPublishButton && <CancelPublish announcement={announcement} />}
      <Dropdown
        menu={{
          items: [
            { key: "copy", label: <Copy announcement={announcement} /> },
            {
              key: "remove",
              label: <Remove announcement={announcement} />,
              danger: true,
            },
          ],
        }}
      >
        <Typography.Link style={{ whiteSpace: "nowrap" }}>...</Typography.Link>
      </Dropdown>
    </Space>
  );
}
