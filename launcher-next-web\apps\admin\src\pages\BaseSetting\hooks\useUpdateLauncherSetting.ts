import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "./const";
import { useBaseSettingListLey } from "./useBaseSettingList";
import { UpdateLauncherSettingRequest } from "./types";

export function useUpdateLauncherSetting() {
  const key = useBaseSettingListLey();
  const { trigger, isMutating } = useSWRMutation(key, updateLauncherSetting);
  return { trigger, isMutating };
}

async function updateLauncherSetting(
  _: any,
  { arg }: { arg: UpdateLauncherSettingRequest },
): Promise<any> {
  return fetcher(`${URL_PREFIX}/launcher_setting/update`, arg);
}
