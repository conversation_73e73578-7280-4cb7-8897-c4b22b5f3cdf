import { Table } from "antd";
import type { TableColumnsType, TableProps } from "antd";
import { BannerConfig } from "../../hooks/useBanner/types";
import { useColumns, useOnTableChange } from "./hooks";
import {
  useBanner,
  usePage,
  useSetSort,
  useSort,
  useBatchSelect,
} from "../../hooks";
import { useCallback, useState } from "react";

export function BannerList() {
  const [page, setPage] = usePage();
  const columns = useColumns();
  const { banners, total } = useBanner();
  const onTableChange = useOnTableChange();

  // const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchSelecte, setBatchSelect] = useBatchSelect();

  const onSelectChange = useCallback(
    (selectedRowKeys: React.Key[], selectedRows: BannerConfig[]) => {
      console.log("selectedRows changed: ", selectedRows);
      setBatchSelect(selectedRows);
    },
    [],
  );

  const rowSelection = {
    preserveSelectedRowKeys: true,
    selectedRowKeys: batchSelecte.map((item) => item.id!),
    onChange: onSelectChange,
  };

  return (
    <div>
      <Table<BannerConfig>
        rowKey={(record) => record.id || ""}
        columns={columns}
        dataSource={banners}
        rowSelection={rowSelection}
        onChange={onTableChange}
        pagination={{
          current: page,
          total: Number(total),
          onChange(page) {
            setPage(page);
          },
        }}
      />
    </div>
  );
}
