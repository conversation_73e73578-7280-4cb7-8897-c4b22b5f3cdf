import { Button } from "antd";
import { Publish } from "@/pages/SocialMedia/features/Publish";
import { useBatchSelectValue, SidebarState } from "../../../../hooks";

export function BatchPublish() {
  const batchSidebarConfig = useBatchSelectValue();
  const batchEnable = batchSidebarConfig.some((item) => {
    return item.state === SidebarState.Unpublished;
  });

  return (
    <Publish
      sidebarsToPublish={batchSidebarConfig}
      trigger={<Button disabled={!batchEnable}>批量发布</Button>}
    />
  );
}
