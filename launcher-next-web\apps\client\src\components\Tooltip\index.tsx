import React, { useEffect, useState, useRef, useLayoutEffect } from "react"
import { createPortal } from "react-dom"
import { motion, AnimatePresence } from "framer-motion"
import clsx from "clsx"
import { twMerge } from "tailwind-merge"

interface TooltipProps {
    trigger?: React.ReactNode
    children?: React.ReactNode
    title: string
    show?: boolean
    placement?: string
    offset?: number
    containerClassName?: string
    triggerElement?: any
}

const getFixedPositionStyle = (
    triggerRect: DOMRect,
    placement: string,
    offset: number
) => {
    const { top, left, right, bottom, width, height } = triggerRect
    switch (placement) {
        case "top":
            return {
                top: top - offset,
                left: left,
                transform: "translateY(-100%)",
            }
        case "top-center":
            return {
                top: top - offset,
                left: left + width / 2,
                transform: "translate(-50%, -100%)",
            }
        case "left":
            return {
                top: top,
                left: left - offset,
                transform: "translateX(-100%)",
            }
        case "left-center":
            return {
                top: top + height / 2,
                left: left - offset,
                transform: "translate(-100%, -50%)",
            }
        case "right":
            return {
                top: top,
                left: right + offset,
            }
        case "right-center":
            return {
                top: top + height / 2,
                left: right + offset,
                transform: "translateY(-50%)",
            }
        case "bottom":
            return {
                top: bottom + offset,
                left: left,
            }
        case "bottom-center":
            return {
                top: bottom + offset,
                left: left + width / 2,
                transform: "translateX(-50%)",
            }
        default:
            return {
                top: bottom + offset,
                left: left,
            }
    }
}

const getPositionStyle = (placement: string, offset: number) => {
    switch (placement) {
        case "top":
            return { bottom: `calc(100% + ${offset}px)`, left: 0 }
        case "top-center":
            return {
                bottom: `calc(100% + ${offset}px)`,
                left: "50%",
                transform: "translateX(-50%)",
            }
        case "left":
            return { right: `calc(100% + ${offset}px)`, top: 0 }
        case "left-center":
            return {
                right: `calc(100% + ${offset}px)`,
                top: "50%",
                transform: "translateY(-50%)",
            }
        case "right":
            return { left: `calc(100% + ${offset}px)`, top: 0 }
        case "right-center":
            return {
                left: `calc(100% + ${offset}px)`,
                top: "50%",
                transform: "translateY(-50%)",
            }
        case "bottom":
            return { top: `calc(100% + ${offset}px)`, left: 0 }
        case "bottom-center":
            return {
                top: `calc(100% + ${offset}px)`,
                left: "50%",
                transform: "translateX(-50%)",
            }
        default:
            return { top: `calc(100% + ${offset}px)`, left: 0 }
    }
}

const arrowColor = "rgba(24, 23, 26, 0.78)"

const getArrowBorderStyle = (placement: string) => {
    const firstPlacement = placement.split("-")[0]
    switch (firstPlacement) {
        case "right":
            return {
                left: "0",
                top: "50%",
                transform: "translate(-100%, -50%)",
                borderTop: "6px solid transparent",
                borderBottom: "6px solid transparent",
                borderRight: `6px solid ${arrowColor}`,
            }
        case "left":
            return {
                right: "0",
                top: "50%",
                transform: "translate(100%, -50%)",
                borderTop: "6px solid transparent",
                borderBottom: "6px solid transparent",
                borderLeft: `6px solid ${arrowColor}`,
            }
        case "top":
            return {
                bottom: "0",
                left: "50%",
                transform: "translate(-50%, 100%)",
                borderLeft: "6px solid transparent",
                borderRight: "6px solid transparent",
                borderTop: `6px solid ${arrowColor}`,
            }
        case "bottom":
            return {
                top: "0",
                left: "50%",
                transform: "translate(-50%, -100%)",
                borderLeft: "6px solid transparent",
                borderRight: "6px solid transparent",
                borderBottom: `6px solid ${arrowColor}`,
            }

        default:
            return {}
    }
}

export const Tooltip: React.FC<TooltipProps> = ({
    trigger,
    children,
    title,
    show,
    placement = "top",
    offset = 0,
    containerClassName,
    triggerElement,
}) => {
    const [open, setOpen] = useState(false)
    const [position, setPosition] = useState<{
        top: number
        left: number
        transform?: string
    }>({ top: 0, left: 0, transform: "" })
    const triggerRef = useRef<HTMLElement>(null)

    const handleMouseEnter = () => {
        setOpen(true)
    }
    const handleMouseLeave = () => {
        setOpen(false)
    }
    useEffect(() => {
        setOpen(!!show)
    }, [show])

    useLayoutEffect(() => {
        if (!open || (!triggerRef.current && !triggerElement)) return
        const trigger = triggerRef.current
            ? triggerRef.current
            : triggerElement!
        const triggerRect = trigger.getBoundingClientRect()
        const newPosition = getFixedPositionStyle(
            triggerRect,
            placement,
            offset
        )
        setPosition(newPosition)
    }, [open, placement, offset])

    const positionStyle = position

    const contentNode = open ? (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className={twMerge(
                    clsx(
                        "fixed",
                        "left-full z-50 text-white px-3 py-[7px] rounded-lg max-w-[297px]",
                        containerClassName
                    )
                )}
                style={{
                    backgroundColor: "rgba(24, 23, 26, 0.78)",
                    boxShadow: `0px 2px 8px 0px rgba(0, 0, 0, 0.65)`,
                    backdropFilter: "blur(4px)",
                    wordWrap: "break-word",
                    ...positionStyle,
                }}
            >
                <div
                    className="absolute w-[2px] h-[2px] rounded-sm box-content"
                    style={{
                        // backgroundColor: "rgba(24, 23, 26, 0.78)",
                        // boxShadow: `0px 2px 8px 0px rgba(0, 0, 0, 0.65)`,
                        ...getArrowBorderStyle(placement),
                    }}
                ></div>
                <div className="font-sans text-[13px] leading-[18px] text-ellipsis overflow-hidden">
                    {title}
                </div>
            </motion.div>
        </AnimatePresence>
    ) : null

    return children ? (
        <>
            {React.isValidElement(children) &&
                React.cloneElement(children, {
                    ref: triggerRef,
                    onMouseEnter: handleMouseEnter,
                    onMouseLeave: handleMouseLeave,
                } as React.HTMLAttributes<any>)}
            {/* <div
                ref={triggerRef}
                className="inline-block"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                {children}
            </div> */}
            {createPortal(contentNode, document.body)}
        </>
    ) : (
        createPortal(contentNode, document.body)
    )
}
