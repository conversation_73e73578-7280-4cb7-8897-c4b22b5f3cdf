import React, { useRef, useState, useEffect } from "react"
import clsx from "clsx"
import { motion, AnimatePresence } from "framer-motion"
import dayjs from "dayjs"
import { GameState, DownloadState, DownloadStep } from "@/types/games"
import { SpeedIcon, TimeIcon } from "@/components/Icon"
import { CSSTransition } from "react-transition-group"
import { formatBytes, formatDuration } from "@/utils/format"
import "./index.scss"

interface GameProgressProps {
    gameState: GameState
    downloadState: DownloadState
    displayGameProgress: boolean
}

export const GameProgress: React.FC<GameProgressProps> = ({
    gameState,
    downloadState,
    displayGameProgress,
}: GameProgressProps) => {
    const nodeRef = useRef<HTMLDivElement>(null)

    const { step, progress } = downloadState || {}
    const [curProgress, setCurProgress] = useState(progress)
    const { from, to, value, speed, remaining_time } = curProgress || {}
    const downloadProgress = value ? Math.round((value / (to - from)) * 100) : 0

    useEffect(() => {
        if (progress) {
            const { from, to, value } = progress
            const progressValue = value / (to - from)
            // 客户端会有from, to, value为0的场景，导致progressValue为NaN，需要过滤
            if (!isNaN(progressValue)) {
                setCurProgress(progress)
            }
        }
    }, [progress])

    const isEmptySpeedStep = [
        GameState.Pause,
        GameState.Queue,
        GameState.Except,
    ].includes(gameState)

    const formatRemainingTime = isEmptySpeedStep
        ? "--:--:--"
        : formatDuration(remaining_time)
    const { value: format_speed, unit: speed_unit } = isEmptySpeedStep
        ? { value: 0, unit: "MB" }
        : formatBytes(speed, 1)

    const unit = step === DownloadStep.GameCompleteCheck ? "count" : "B"
    const { value: format_value, unit: value_unit } = formatBytes(
        value,
        1,
        unit
    )
    const { value: format_to, unit: to_unit } = formatBytes(to, 1, unit)

    // 隐藏下载大小状态
    const hiddenDownloadValList = [DownloadStep.Decompress, DownloadStep.Copy]
    // 隐藏下载速度状态
    const hiddenSpeedList = [...hiddenDownloadValList]
    // 隐藏剩余时间状态
    const hiddenTimeList = [
        DownloadStep.Copy,
        DownloadStep.Check,
        DownloadStep.GameCompleteCheck,
    ]

    const hiddenSpeed = hiddenSpeedList.includes(step)
    const hiddenTime = hiddenTimeList.includes(step)
    const hiddenDownloadValue = hiddenDownloadValList.includes(step)

    return (
        <CSSTransition
            in={displayGameProgress}
            classNames="slide-right"
            timeout={300}
            nodeRef={nodeRef}
            mountOnEnter
            unmountOnExit
        >
            <div
                ref={nodeRef}
                className="w-full h-[60px] rounded-l-[18px] overflow-x-hidden overflow-y-visible pointer-events-none"
                style={{
                    boxShadow: "0px 3px 10px 0px rgba(0, 0, 0, 0.55)",
                }}
            >
                <div
                    className={clsx(
                        "slide-right-div",
                        "w-full h-full pl-6 pb-[15px] pt-[10px] pr-[54px]",
                        "rounded-l-[18px] border-[0.5px] border-white/10",
                        "bg-[rgb(24,23,26)]/[0.78] backdrop-blur-[4px]"
                    )}
                    style={{
                        willChange: "transform",
                    }}
                >
                    <div className="flex justify-between items-baseline mb-[6px] h-[20px]">
                        <div className="text-white text-[16px] font-bold">
                            <span
                                className="text-primary text-[18px] font-bold"
                                style={{ fontFamily: "Novecento wide" }}
                            >
                                {downloadProgress}%
                            </span>
                            {hiddenDownloadValue ? null : (
                                <span className="text-white/55 text-[12px] font-[350] font-sans">
                                    （{format_value}
                                    {value_unit} / {format_to}
                                    {to_unit}）
                                </span>
                            )}
                        </div>
                        <div className="flex gap-2 items-center text-white/55 text-[12px] font-sans relative top-[1px]">
                            {hiddenSpeed ? null : (
                                <>
                                    <div className="flex items-center gap-1">
                                        <div>
                                            <SpeedIcon />
                                        </div>
                                        <span>
                                            {format_speed}
                                            {speed_unit}/S
                                        </span>
                                    </div>
                                </>
                            )}
                            {hiddenSpeed || hiddenTime ? null : (
                                <div className="w-[1px] h-[10px] bg-white/[0.55]"></div>
                            )}
                            {hiddenTime ? null : (
                                <div className="flex items-center gap-1">
                                    <div>
                                        <TimeIcon />
                                    </div>
                                    <span>{formatRemainingTime}</span>
                                </div>
                            )}
                        </div>
                    </div>
                    <ProgressBar progress={downloadProgress} step={step} />

                    {/* <div className="w-[1.875rem]"></div> */}
                </div>
            </div>
        </CSSTransition>
    )
}

const ProgressBar = ({
    progress,
    step,
}: {
    progress: number
    step: DownloadStep
}) => {
    const docWidth = document.documentElement.clientWidth
    const cellNum = docWidth < 1440 ? 150 : docWidth / 6 + 20

    const firstRenderRef = useRef(true)
    const [hasAnimation, setHasAnimation] = useState(false)

    useEffect(() => {
        if (firstRenderRef.current) {
            setHasAnimation(true)
            firstRenderRef.current = false
            return
        }
    }, [])

    return (
        <div
            id="progress-bar"
            className="relative w-full h-2 bg-white/10 px-[4px] py-[2px] rounded-[18px] overflow-hidden"
        >
            <div className="flex h-full gap-[3px] items-center overflow-hidden">
                {Array.from({ length: cellNum }).map((_, index) => (
                    <div
                        key={index}
                        className={clsx(
                            "w-[3px] h-[10px] bg-white/10 rotate-[15deg] shrink-0"
                        )}
                    />
                ))}
            </div>

            {/* <motion.div
                className="absolute left-0 top-0 bottom-0 h-full bg-white rounded-[18px]"
                animate={{ width: `${progress * 100}%` }}
                transition={{ duration: 0.3 }}
            /> */}
            <motion.div
                key={step} // 重置进度
                className={clsx(
                    "absolute top-0 left-0",
                    "w-full h-full bg-primary",
                    "rounded-[100px]"
                )}
                initial={{ x: "-100%" }}
                animate={{ x: `${-100 + progress}%` }}
                transition={hasAnimation ? { duration: 0.2 } : { duration: 0 }}
                style={{
                    // border: "1px solid rgba(255, 255, 255, 0.10)",
                    boxShadow: "0px 0px 12px 3px rgba(24, 23, 26, 0.35)",
                }}
            />
        </div>
    )
}
