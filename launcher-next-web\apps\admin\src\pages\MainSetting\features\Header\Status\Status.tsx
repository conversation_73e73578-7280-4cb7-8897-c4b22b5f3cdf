import { Tag } from "antd";
import { useMemo } from "react";
import { useMainData, MainPageState } from "@/pages/MainSetting/hooks";
import { getMainPageStatus } from "@/pages/MainSetting/utils";
import { getDateByTs } from "@/utils/time";

export function Status() {
  const { currentConfig } = useMainData();
  const start_ts = useMemo(() => {
    const isTiming = currentConfig?.state === MainPageState.Timing;
    if (isTiming) {
      return currentConfig?.start_ts
        ? getDateByTs(currentConfig?.start_ts)
        : "";
    }
  }, [currentConfig?.state, currentConfig?.start_ts]);

  const { color, text } = getMainPageStatus(currentConfig);
  return (
    <Tag color={color}>
      {text}
      {start_ts ? `: ${start_ts}` : ""}
    </Tag>
  );
}
