import { eventBus } from "@/utils/eventBus"
import { MainBtnState } from "@/types/games"

export const MockMainBtnData = ({ data }: { data: any }) => {
    const handleClick1 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.ContinueDownload,
            text_normal: "继续下载",
            text_hover: "继续下载",
            tooltip: "",
        }
        eventBus.emit("main_btn_state_notify", data)
    }
    const handleClick2 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.CheckingDownloadZip,
            text_normal: "校验中",
            text_hover: "校验中",
            tooltip: "",
        }
        eventBus.emit("main_btn_state_notify", data)
    }
    const handleClick3 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.Downloading,
            text_normal: "Downloading",
            text_hover: "暂停下载",
            tooltip: "",
        }
        eventBus.emit("main_btn_state_notify", data)
    }
    const handleClick4 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.DownloadExcept,
            text_normal: "下载异常",
            text_hover: "重试",
            tooltip: "下载异常，稍后再试，错误码：207",
        }
        eventBus.emit("main_btn_state_notify", data)
    }
    const handleClick5 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.StartGame,
            text_normal: "开始游戏",
            text_hover: "开始游戏",
            tooltip: "",
        }
        eventBus.emit("main_btn_state_notify", data)
    }
    const handleClick6 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.GameServiceStopped,
            text_normal: "不可用",
            text_hover: "不可用",
            tooltip: "",
        }
        eventBus.emit("main_btn_state_notify", data)
    }

    const handleClick7 = () => {
        const data = {
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            state: MainBtnState.DownloadPaused,
            text_normal: "normal暂停下载",
            text_hover: "继续下载",
            tooltip: "",
        }
        eventBus.emit("main_btn_state_notify", data)
    }

    const btnClassName = "rounded-xl p-2 text-white bg-black/50"
    return (
        <div className="flex fixed top-[120px] right-[10px] border  overflow-y-auto bg-white/80 ">
            <div className="flex flex-col gap-2">
                <button className={btnClassName} onClick={handleClick1}>
                    切换 继续下载
                </button>
                <button className={btnClassName} onClick={handleClick2}>
                    切换 校验中
                </button>
                <button className={btnClassName} onClick={handleClick3}>
                    切换 正在下载
                </button>
                <button className={btnClassName} onClick={handleClick4}>
                    切换 下载异常
                </button>
                <button className={btnClassName} onClick={handleClick5}>
                    切换 开始游戏
                </button>
                <button className={btnClassName} onClick={handleClick6}>
                    切换 不可用
                </button>
                <button className={btnClassName} onClick={handleClick7}>
                    切换 暂停下载
                </button>
            </div>
            <div className="w-[400px] h-[300px] border">
                <pre>{JSON.stringify(data, null, 2)}</pre>
            </div>
        </div>
    )
}
