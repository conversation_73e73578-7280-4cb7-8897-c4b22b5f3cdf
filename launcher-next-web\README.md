### env

#### repo
https://gitlab.hypergryph.net/web/hypergryph/launcher

#### apollo
https://ops-apollo.hypergryph.net/config.html?#/appid=1790

#### paas
https://paas.hypergryph.net/#/serviceCatalog/APP/APP_1790/cluster/ENV_dev/Group_14017

#### 流水线
https://devops.bk.hypergryph.net/console/pipeline/biz-platform-launcher/p-faa5ece32d394a57b138c92e19aff53a/history

#### urls
##### Staging

C 端

https://launcher-web-biz-platform-launcher-staging.hypergryph.net/zh-cn/CD9pKvK1Ne504BHd

B 端

https://omc-host-biz-platform-web-staging.hypergryph.net/#/app/launcher/topic

##### Prod
C 端

https://launcher-web-biz-platform-launcher-prod.hypergryph.net/zh-cn/CD9pKvK1Ne504BHd

B 端

https://omc.hypergryph.net/#/app/launcher/topic

### dev
#### 开发 admin
```
pnpm run dev:admin
## 打开 https://omc-host-biz-platform-web-staging.hypergryph.net/#/dev
## 配置 http://127.0.0.1:8002/meta.json
## 点击进入开发
```

#### 开发 client
```
pnpm run dev:client
```


#### config
配置在 config/config.admin.json 以及 config/config.web.json 中

