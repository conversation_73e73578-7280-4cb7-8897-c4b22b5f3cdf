import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="m23.438 16.688-5.063 5.062h-2.813L13.313 24h-2.25v-2.25h-4.5V8.812L8.25 6h15.188v10.688Zm-13.5 1.687h3.374v2.25l2.25-2.25h2.813l2.813-2.813V8.25H9.938v10.125ZM15.561 15h-1.687v-4.5h1.688V15Zm3.376 0H17.25v-4.5h1.688V15Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.875}
        height={33}
        x={-0.938}
        y={0.375}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50775"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50775"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
