import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useBannerKey } from "../useBannerKey";
import { UpdateBannerRequest } from "../types";

export function useUpdateBanner() {
  const key = useBannerKey();
  const { trigger } = useSWRMutation(key, updateBanner);
  return trigger;
}

async function updateBanner(_: any, { arg }: { arg: UpdateBannerRequest }) {
  await fetcher(`${URL_PREFIX}/update`, arg);
}
