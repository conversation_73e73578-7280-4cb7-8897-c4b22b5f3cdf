import useSWR from "swr";
import { fetcher } from "@/utils/fetcher";
import { isHG } from "@/utils/env";
import { Language } from "@/data";
import { useAppCode } from "@/hooks";

export type LangMap = { [key in Language]: string };

export interface Response {
  languages?: Array<Language>;
}

export const LabelList = [
  { label: "简体中文", value: Language.ZhCN },
  { label: "繁体中文", value: Language.ZhTW },
  { label: "日语", value: Language.JaJP },
  { label: "韩语", value: Language.KoKR },
  { label: "英语", value: Language.EnUS },
];

export const curEnvLabelList = isHG
  ? [{ label: "简体中文", value: Language.ZhCN }]
  : LabelList;

function requestLangList() {
  const url = "/admin/launcher/config/languages";
  const appCode = useAppCode();
  const DefaultLangList = LabelList?.map((item) => item.value);

  const { data, isLoading, error } = useSWR(
    [url, appCode],
    ([url, appCode]) =>
      fetcher<Response>(
        url + `?appcode=${appCode}`,
        {},
        {
          method: "GET",
        },
      ),
    { fallbackData: { languages: DefaultLangList } },
  );

  if (error) {
    return { data: { languages: DefaultLangList } };
  }

  return { data, error };
}
export function useLangList() {
  // 使用 hooks 实现返回，方便后续的权限以及多语言等功能
  const { data } = requestLangList();
  const LangList = data?.languages || [];

  return (
    LabelList.filter((item) => {
      return LangList?.includes(item.value);
    }) || []
  );
}

export function useLangFiltersList() {
  const langList = useLangList();

  return langList?.map((item) => {
    return {
      text: item.label,
      value: item.value,
    };
  });
}

export function useLangListMap() {
  return LabelList.reduce((prev, cur) => {
    prev[cur.value] = cur.label;
    return prev;
  }, {} as LangMap);
}

export function useCurEnvLangListMap() {
  return curEnvLabelList.reduce((prev, cur) => {
    prev[cur.value] = cur.label;
    return prev;
  }, {} as LangMap);
}
