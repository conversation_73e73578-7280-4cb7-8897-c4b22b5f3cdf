import { PlusOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { EditForm } from "../../../EditForm";
import { useCreateBanner } from "@/pages/Banner/hooks";
import { useAppCode } from "@/hooks";
import { Language } from "@/data";

export function Create() {
  const createBanner = useCreateBanner();
  const appcode = useAppCode();
  return (
    <EditForm
      title="新建 Banner"
      trigger={
        <Button icon={<PlusOutlined />} type="primary">
          新建
        </Button>
      }
      onFinish={async (values) => {
        await createBanner({ appcode, languages: [Language.ZhCN], ...values });
      }}
    />
  );
}
