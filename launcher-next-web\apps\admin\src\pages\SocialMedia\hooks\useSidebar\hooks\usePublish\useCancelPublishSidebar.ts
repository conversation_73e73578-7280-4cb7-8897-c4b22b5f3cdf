import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useSidebarKey } from "../../useSidebarKey";
import { ExpireSidebarRequest, PublishRequest } from "../../types";
import { useSWRConfig } from "swr";
import { useAppCode } from "@/hooks";

export function useCancelPublishSidebar() {
  const key = useSidebarKey();
  const appCode = useAppCode();
  const { mutate } = useSWRConfig();
  const { trigger } = useSWRMutation(key, cancelPublishSidebar, {
    onSuccess() {
      mutate([URL_PREFIX + "/get_orders", appCode]);
    },
  });
  return trigger;
}

async function cancelPublishSidebar(
  _: any,
  { arg }: { arg: ExpireSidebarRequest },
) {
  await fetcher(`${URL_PREFIX}/expire`, arg);
}
