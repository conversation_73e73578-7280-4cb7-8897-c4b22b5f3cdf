import {
  DrawerForm,
  ProFormText,
  ProFormSelect,
} from "@ant-design/pro-components";
import { Form, message } from "antd";
import { AnnouncementTabConfig } from "../../hooks/useAnnounceTab/types";
import { useLangList } from "@/hooks";

type ValueWithTs = Values & { start_ts?: string; end_ts?: string };

interface Props {
  title: React.ReactNode;
  trigger: JSX.Element;
  onFinish: (values: ValueWithTs) => Promise<void>;
  initialValues?: AnnouncementTabConfig;
}

interface Values {
  name?: string;
}

export function EditForm({ trigger, title, onFinish, initialValues }: Props) {
  const [form] = Form.useForm<{ name: string; company: string }>();
  const langList = useLangList();

  return (
    <DrawerForm<Values>
      title={title}
      resize={{
        maxWidth: window.innerWidth * 0.8,
        minWidth: 600,
      }}
      form={form}
      trigger={trigger}
      drawerProps={{
        destroyOnClose: true,
      }}
      initialValues={{
        name: initialValues?.name,
        language: initialValues?.language,
      }}
      onFinish={async (values) => {
        try {
          await onFinish({ ...initialValues, ...values });
          message.success("提交成功");
          return true;
        } catch (error) {
          console.error(error);
          message.success("提交失败");
        }
        // 不返回不会关闭弹框
        // return true;
      }}
    >
      <ProFormText
        name="name"
        // width="md"
        label="名称"
        // tooltip="最长为 24 位"
        placeholder="请输入名称"
        rules={[{ required: true, max: 200 }]}
      />
      <ProFormSelect
        label="应用语种"
        name="language"
        disabled={!!initialValues?.language} // 创建后不允许修改语种
        options={langList}
        style={{ width: "100%" }}
        width={"sm"}
        placeholder="请选择应用语种"
        rules={[{ required: true }]}
      />
    </DrawerForm>
  );
}
