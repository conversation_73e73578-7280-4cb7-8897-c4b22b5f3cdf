import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M8.21 17.934c-.008.024-.313 1.078-1.14 1.066h-.8l-.65-1.293h.96c.102 0 .1-.142.1-.104l.039-6.53h1.49v6.86Zm10.399-4.898h-.922v4.484h1.43v1.467H14.21l.642-1.455h1.26l.025-4.508-.885-.011-.036-1.444h3.394v1.467Zm3.393-1.467h.934c.02.003 1.514.203 1.502 1.443v1.357h.376c.023.001 1.299.06 1.299 1.274v2.06c0 .009.09 1.284-1.192 1.284H23.82l-.582-1.26h1.212c.009 0 .267.016.287-.34.022-.367-.057-1.224-.057-1.224-.001-.01-.03-.314-.34-.314h-2.338v3.137h-1.467V15.86l-1.502-.011V14.38h1.504v-1.37h-.994V11.57h1.03L20.549 11h1.454v.57Zm-10.29 5.818s.05.133.607.133h1.696l-.679 1.466H11.53s-.527.012-.512-.158l.694-1.441Zm-5.998-4.345s-.173 2.482-.219 2.918c-.045.437-.209 1.61-.854 2.354l-.755-1.645s.173-.173.346-3.627h1.482Zm4.956-.018c.001.018.194 3.538.348 3.558l-.748 1.69s-.691-.52-.873-2.624a80.416 80.416 0 0 1-.194-2.624h1.467Zm3.515-2.012-.752 1.696s-.133.34.085.352c.218.012 1.248 0 1.248 0l-1.043 2.338c-.002.007-.106.303.096.303h.777l-.517 1.212h-1.7c-.02-.003-.736-.118-.437-.776.303-.667.752-1.721.752-1.721l-.764.013s-.69-.147-.352-.849c.34-.703 1.188-2.57 1.188-2.57h1.419v.002Zm7.793 3.34h1.029v-1.229c-.002-.03-.023-.093-.15-.093h-.88v1.322Zm3.438-2.758c.384 0 .697.313.697.696a.7.7 0 0 1-.697.697h-.696v-.697a.7.7 0 0 1 .696-.696Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={37.228}
        height={23}
        x={-3.614}
        y={5.375}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_4606_102477"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_4606_102477"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
