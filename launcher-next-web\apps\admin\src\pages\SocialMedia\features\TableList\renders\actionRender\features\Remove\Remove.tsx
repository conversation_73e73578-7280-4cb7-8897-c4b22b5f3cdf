import { useRemoveSidebar } from "@/pages/SocialMedia/hooks";
import { Popconfirm, Typography } from "antd";
import { SidebarActionProps } from "../types";

export function Remove({ sidebarConfig }: SidebarActionProps) {
  const removeSidebar = useRemoveSidebar();
  return (
    <Popconfirm
      title="是否确认删除此 sidebar ？"
      onConfirm={() =>
        removeSidebar({ id: sidebarConfig.id, appcode: sidebarConfig.appcode })
      }
    >
      <Typography.Link>删除</Typography.Link>
    </Popconfirm>
  );
}
