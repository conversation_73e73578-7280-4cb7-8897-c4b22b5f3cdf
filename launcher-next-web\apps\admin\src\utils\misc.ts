import _ from "lodash";

export function noop() {}

export function tryParse<T>(
  json: string | undefined | null,
  dataWhenFail: any,
): T {
  if (!json) {
    return dataWhenFail;
  }
  try {
    return JSON.parse(json);
  } catch (error) {
    return dataWhenFail;
  }
}

export function download(url: string, filename: string) {
  const a = document.createElement("a");
  a.href = url;
  a.target = "_blank";
  a.download = filename;
  a.click();
  window.URL.revokeObjectURL(url);
}

export function randomString(e = 32) {
  const t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  const a = t.length;
  let n = "";
  for (let i = 0; i < e; i += 1) n += t.charAt(Math.floor(Math.random() * a));
  return n;
}

export function sleep(timeout: number) {
  return new Promise((res) => {
    setTimeout(() => {
      res(true);
    }, timeout);
  });
}

export const combineURLs = (baseURL: string, relativeURL: string) => {
  // 将baseURL最后的斜杠和relativeURL最前面的斜杠去掉
  return relativeURL
    ? `${baseURL.replace(/\/+$/, "")}/${relativeURL.replace(/^\/+/, "")}`
    : baseURL;
};

export const downloadImgByBase64 = (imgData: string, title: string) => {
  const aTag = document.createElement("a");
  aTag.download = title;
  aTag.href = imgData;
  aTag.click();
};
export function addParam(u: string, key: string, value: string) {
  const url = new URL(u);
  const params = new URLSearchParams(url.search);

  // 添加参数
  params.append(key, value);

  // 将更新后的参数设置回URL对象
  url.search = params.toString();

  return url.toString();
}

export function moveElement(array: any[], from: number, to: number) {
  if (from === to) {
    return array;
  }

  const valueToMove = array[from];
  const newArray = _.clone(array);
  newArray.splice(from, 1); // Remove the element at 'from' index
  newArray.splice(to, 0, valueToMove); // Insert the element at 'to' index

  return newArray;
}
