import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M24.343 10.29a2.45 2.45 0 0 0-1.724-1.735c-1.521-.41-7.619-.41-7.619-.41s-6.098 0-7.619.41a2.45 2.45 0 0 0-1.724 1.735c-.407 1.53-.407 4.724-.407 4.724s0 3.193.407 4.724a2.414 2.414 0 0 0 1.724 1.707c1.521.41 7.619.41 7.619.41s6.098 0 7.619-.41a2.414 2.414 0 0 0 1.724-1.707c.407-1.53.407-4.724.407-4.724s0-3.194-.407-4.724Zm-11.337 7.623v-5.799l5.096 2.9-5.096 2.9Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={34.5}
        height={28.711}
        x={-2.25}
        y={2.52}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50688"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50688"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
