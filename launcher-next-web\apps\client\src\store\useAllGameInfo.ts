import { atom, useAtom, useAtomValue, useSetAtom } from "jotai"
import { Game } from "@/types/games"

export const allGameInfoAtom = atom<{
    uuid: string | null
    firstTheme: string | null
    isSingleGame: boolean
    allGamesList: Game[]
}>({
    uuid: "",
    firstTheme: "",
    isSingleGame: false,
    allGamesList: [],
})

export function useAllGameInfo() {
    return useAtom(allGameInfoAtom)
}

export function useAllGameInfoValue() {
    return useAtomValue(allGameInfoAtom)
}

export function useSetAllGameInfo() {
    return useSetAtom(allGameInfoAtom)
}
