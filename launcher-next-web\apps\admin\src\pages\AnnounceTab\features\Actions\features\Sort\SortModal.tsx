// import { Flex, Modal, Select, Space, Tabs, message } from "antd";
// import React, { useState } from "react";
// import { Language } from "@/data";
// import { useAppCode, useLangList } from "@/hooks";
// import { useAnnounceTab } from "@/pages/AnnounceTab/hooks";
// import { cloneDeep } from "lodash";
// import { SortableTable } from "@/features";

// interface Props {
//   trigger: JSX.Element;
// }

// /** TODO: 暂未处理多语言情况 */
// export function SortModal({ trigger }: Props) {
//   const { tabs } = useAnnounceTab();
//   const appcode = useAppCode();
//   const [isOpen, setIsOpen] = useState(false);
//   const [language, setLanguage] = useState(Language.ZhCN);
//   const { orders } = usePublishedData({
//     onSuccess(data) {
//       setData(getInitialData(data));
//     },
//   });
//   const publish = usePublish();
//   const langList = useLangList();
//   function getInitialData(data: AnnouncementConfigOrder[]) {
//     const currentData: Record<string, AnnouncementConfig[]> = {};
//     tabs?.forEach((tab) => {
//       const targetAnnouncements = data?.filter(
//         (ele) => ele.language === language && ele.tab_id === tab.id
//       )[0]?.announcement_configs;
//       if (targetAnnouncements) {
//         currentData[tab.id!] = targetAnnouncements;
//       }
//     });
//     if (!dataToPublish) {
//       return currentData;
//     }
//     dataToPublish.forEach((ele) => {
//       currentData[ele.tab_id!] = [ele, ...(currentData[ele.tab_id!] || [])];
//     });
//     return currentData;
//   }
//   const [data, setData] = useState(getInitialData(orders || []));
//   return (
//     <div>
//       {React.cloneElement(trigger, {
//         onClick: () => {
//           setData(getInitialData(orders || []));
//           setIsOpen(true);
//         },
//       })}
//       <Modal
//         width={1200}
//         open={isOpen}
//         onCancel={() => setIsOpen(false)}
//         onOk={async () => {
//           try {
//             await publish({
//               appcode: appcode,
//               orders: Object.keys(data).map((tab_id) => ({
//                 tab_id,
//                 language: Language.ZhCN,
//                 publish_ids:
//                   dataToPublish
//                     ?.filter((ele) => ele.tab_id === tab_id)
//                     .map((ele) => ele.id!) || [],
//                 order_ids: data[tab_id].map((ele) => ele.id!),
//               })),
//             });
//             setIsOpen(false);
//           } catch (error) {
//             console.error(error);
//             message.error("发布失败");
//           }
//         }}
//         okText="发布"
//         cancelText="取消"
//         title={
//           <Space>
//             设置排序
//             <Select
//               options={langList}
//               value={language}
//               onChange={setLanguage}
//             />
//           </Space>
//         }
//       >
//         <SortableTable
//         //   announcementList={data[tab.id!]}
//         //   setAnnouncementList={(value) =>
//         //     setData((prev) => {
//         //       const newData = cloneDeep(prev);
//         //       newData[tab.id!] = value;
//         //       return newData;
//         //     })
//         //   }
//         />
//       </Modal>
//     </div>
//   );
// }
