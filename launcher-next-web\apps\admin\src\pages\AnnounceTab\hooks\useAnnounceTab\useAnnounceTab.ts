import useS<PERSON> from "swr";
import { fetcher } from "@/utils/fetcher";
import { ListResponse, BannerFilter } from "./types";
import { useAnnounceTabKey } from "./hooks";
import { message } from "antd";
import { Language } from "@/data";
/**
 *
 * @param filterLanguage 传参filterLanguage作为key，filterLanguage改变时重新触发useAnnounceTab
 */
export function useAnnounceTab(filterLanguage?: string) {
  const key = useAnnounceTabKey(filterLanguage);
  const { data, isLoading, mutate } = useSWR(
    key,
    ([url, appCode, { page, searchVal, sort, filter }, filterLanguage]) =>
      fetcher<ListResponse>(url, {
        appcode: appCode,
        language: filterLanguage || getFilter(filter)?.language,
        // page_num: page,
        // page_size: 10,
        // search_keywords: getSearchKeywords({ searchVal }),
        // order_bys: sort,
      }),
    {
      onError(err, key, config) {
        message.error(err?.message || "请求错误");
      },
    },
  );
  const tabs = data?.announcement_tab_configs;
  const total = tabs?.length;
  return {
    data,
    tabs,
    isLoading,
    total,
    mutate,
  };
}

function getSearchKeywords({ searchVal }: { searchVal: string }) {
  const keywords = [];
  if (searchVal) {
    keywords.push({ field: "description", keyword: searchVal });
  }
  return keywords;
}

function getFilter(filter?: { language?: string[] }) {
  const filters: BannerFilter = {};
  if (filter?.language) {
    filters.language = filter.language?.[0];
  }
  return filters;
}
