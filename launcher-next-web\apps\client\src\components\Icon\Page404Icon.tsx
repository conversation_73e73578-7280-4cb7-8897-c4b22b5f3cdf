import * as React from "react"

export const Page404Icon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={220}
        height={140}
        fill="none"
    >
        <mask
            id="a"
            width={220}
            height={140}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: "luminance",
            }}
        >
            <path fill="#fff" d="M0 0h220v140H0z" />
        </mask>
        <g mask="url(#a)">
            <g opacity={0.698}>
                <circle
                    cx={161.004}
                    cy={17.369}
                    r={12.369}
                    fill="#393939"
                    fillOpacity={0.35}
                />
                <mask
                    id="b"
                    width={26}
                    height={25}
                    x={148}
                    y={5}
                    maskUnits="userSpaceOnUse"
                    style={{
                        maskType: "luminance",
                    }}
                >
                    <circle cx={161.004} cy={17.369} r={12.369} fill="#fff" />
                </mask>
                <g
                    fill="#161616"
                    fillOpacity={0.27}
                    fillRule="evenodd"
                    clipRule="evenodd"
                    mask="url(#b)"
                >
                    <path d="M154.779 7.013s.562 1.026.814 1.397c.612.899 1.113 1.244 1.504 1.035.829-.443 2.612-1.232 3.551 0 .939 1.232 1.307 1.939 2.269 1.939.962 0 2.187 1.463 2.187 1.463s.531.641.984 1.227c.454.586.258 1.118 1.351.382 1.093-.737.553-.737 1.533-.737.98 0 2.665 2.455 1.909.737-.757-1.719.52.158.847.566.327.409 1.728 0 1.728 0l.715 1.682h-3.29c-.292 0-.559.012-.559-.294 0-.307.342-.706 0-.706h-4.684c-.652 0-1.271.27-1.7-.682-.428-.951-.574-1.159-1.021-1.429-.447-.27-.021-.135-1.547-1.007-1.525-.871-1.024-.871-2.138-.871-1.114 0-1.001.224-1.602 0-.601-.224-1.456-1.067-1.456-1.067s-.545-.534-.963-1.236c-.418-.703-.272-.84-.853-1.477-.581-.636-1.081-1.097-1.081-1.377 0-.281-.269-.762 0-.762.268 0 1.081-.264 1.081-.264l.421 1.481ZM164.875 27s-2.622.46-2.954-.372c-.331-.833-1.135-2.537-2.468-2.557-1.333-.02-1.979.092-2.648-.6-.669-.693-2.099-.996-2.099-.996s-.724-.04-1.362-.055c-.638-.014-.796.41-1.15-.77-.353-1.179.023-.79-.659-1.495-.681-.706-.819-1.176-1.411-1.07-.593.106-.909.28-1.362.262-.452-.017-.988-.398-.988-.398l-1.426.382 1.699 1.76.589.61c.203.209.382.408.551.245.17-.164.151-.623.39-.376a13768.75 13768.75 0 0 0 2.004 2.075l1.252 1.297c.453.47.735 1.058 1.558.86.823-.198 1.039-.204 1.499-.026.46.178.089-.057 1.631.577 1.541.634 1.193.273 1.967 1.075.774.802.572.84 1.114 1.153.542.314 1.601.48 1.601.48s.674.108 1.352.035c.679-.074.653-.252 1.408-.174.755.08 1.357.194 1.512.045.155-.15.607-.212.42-.406-.187-.193-.606-.92-.606-.92l-1.414-.64ZM151.285 11.6s1.493 2.885 2.323 2.44c.831-.444 2.618-1.236 3.559 0 .942 1.237 1.311 1.946 2.275 1.946.965 0 2.179 1.071 2.179 1.071s.532.644.987 1.231c.454.588.258 1.122 1.354.383 1.096-.74.554-.74 1.536-.74.983 0 1.417-.297 1.753.357.336.654.431 1.11.758 1.52.328.41 4.647 2.124 4.647 2.124l.717 1.688h-2.45s-4.229-1.556-4.521-1.556c-.292 0-.56.013-.56-.295s.343-.708 0-.708h-4.696c-.653 0-1.274.27-1.704-.685-.429-.954-.575-1.163-1.023-1.434-.449-.27-.021-.136-1.551-1.01-1.529-.875-1.027-.875-2.143-.875-1.117 0-1.004.225-1.607 0-.602-.225-1.459-1.07-1.459-1.07s-.547-.536-.966-1.241c-.419-.706-.272-.843-.854-1.482-.583-.639-1.085-1.1-1.085-1.382 0-.282-.269-.765 0-.765.27 0 1.085-.265 1.085-.265l1.446.748Z" />
                </g>
                <path
                    stroke="#D2D2D2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.935}
                    d="M150.285 8.242c-4.831-.142-8.283.905-8.997 3.102-1.264 3.89 6.538 9.91 17.427 13.449v0c10.888 3.538 20.74 3.253 22.004-.637.708-2.181-1.434-5.033-5.372-7.74"
                    opacity={0.709}
                />
            </g>
            <path
                fill="#D8D8D8"
                d="m59.958 70.607 8.928-.78L69.9 81.433l-8.927.782.681 7.801a201.545 201.545 0 0 0-12.433 2.722l-.825-9.423-29.592 2.59-1.121-12.815 21.322-37.608 17.743-1.552 3.209 36.677Zm-14.929-22.38L31.232 72.602l.045.514 16.106-1.41-1.792-23.53-.562.05ZM197.785 40.587l-4.487 36.544 8.894 1.092-1.42 11.565-8.894-1.092-1.27 10.337c-3.775-1.538-7.852-2.965-12.194-4.273l.934-7.603-29.485-3.62 1.568-12.767 28.675-32.353 17.679 2.17Zm-33.001 32.524-.062.512 16.046 1.97 3.139-23.39-.559-.068-18.564 20.976Z"
            />
            <path
                fill="#4D4D4D"
                d="M111.593 27.7c15.399.001 27.882 12.485 27.882 27.884v31.66c-4.199-.44-8.498-.79-12.882-1.04V58.607c0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15v27.451c-4.38.205-8.679.51-12.883.904V55.584c0-15.4 12.484-27.883 27.883-27.883Z"
            />
            <ellipse
                cx={110.16}
                cy={127.542}
                fill="url(#c)"
                rx={110}
                ry={41.8}
            />
            <ellipse
                cx={193.628}
                cy={111.266}
                fill="url(#d)"
                rx={8.415}
                ry={2.258}
                transform="rotate(7 193.628 111.266)"
            />
            <ellipse
                cx={120.233}
                cy={118.347}
                fill="url(#e)"
                rx={10.858}
                ry={2.895}
                transform="rotate(13.481 120.233 118.347)"
            />
            <ellipse
                cx={148.107}
                cy={140.018}
                fill="url(#f)"
                rx={33.856}
                ry={9.028}
                transform="rotate(-1.682 148.107 140.018)"
            />
            <ellipse
                cx={29.689}
                cy={113.135}
                fill="url(#g)"
                rx={13.115}
                ry={3.51}
                transform="rotate(-8 29.689 113.135)"
            />
            <ellipse
                cx={92.001}
                cy={117.374}
                fill="url(#h)"
                rx={4.064}
                ry={1.108}
                transform="rotate(-8 92 117.374)"
            />
            <ellipse
                cx={156.866}
                cy={105.966}
                fill="url(#i)"
                rx={6.835}
                ry={1.847}
                transform="rotate(-1 156.866 105.966)"
            />
            <ellipse
                cx={123.441}
                cy={91.63}
                fill="url(#j)"
                rx={17.857}
                ry={1.847}
            />
            <path
                fill="#A8A8A8"
                d="M154.508 79.047a12.88 12.88 0 0 0-9.363 3.534c-2.559 2.402-4.026 5.668-4.149 9.194-.123 3.526 1.112 6.886 3.497 9.46a12.882 12.882 0 0 0 9.094 4.179 12.815 12.815 0 0 0 7.48-2.065l6.789 7.363a2.507 2.507 0 0 0 3.559.126l.173-.162c1-.941 1.055-2.513.124-3.523l-6.887-7.468a13.179 13.179 0 0 0 2.273-6.982c.123-3.526-1.112-6.886-3.497-9.46-2.385-2.592-5.616-4.074-9.093-4.196Zm-7.108 6.046c1.909-1.782 4.384-2.691 6.988-2.6a9.736 9.736 0 0 1 6.788 3.081c1.781 1.89 2.7 4.38 2.609 6.997a9.663 9.663 0 0 1-3.091 6.798 9.738 9.738 0 0 1-6.987 2.599 9.735 9.735 0 0 1-6.788-3.08 9.665 9.665 0 0 1-2.609-6.997 9.661 9.661 0 0 1 3.09-6.798Z"
            />
            <foreignObject
                width={27.488}
                height={27.488}
                x={140.303}
                y={78.487}
            >
                <div
                    xmlns="http://www.w3.org/1999/xhtml"
                    style={{
                        backdropFilter: "blur(2px)",
                        clipPath: "url(#k)",
                        height: "100%",
                        width: "100%",
                    }}
                />
            </foreignObject>
            <path
                fill="#fff"
                fillOpacity={0.5}
                fillRule="evenodd"
                d="M146.918 98.888a9.665 9.665 0 0 1-2.609-6.997 9.666 9.666 0 0 1 3.091-6.798c1.909-1.782 4.384-2.69 6.987-2.6a9.735 9.735 0 0 1 6.789 3.08c1.781 1.891 2.7 4.38 2.609 6.998a9.666 9.666 0 0 1-3.091 6.798 9.735 9.735 0 0 1-6.987 2.599 9.73 9.73 0 0 1-6.789-3.08Z"
                clipRule="evenodd"
                data-figma-bg-blur-radius={4}
            />
            <path
                fill="#A8A8A8"
                fillRule="evenodd"
                d="m84.994 126.117-6.011 1.979v2.331l6.011-2.331v-1.979Z"
                clipRule="evenodd"
            />
            <path
                fill="#C3C3C3"
                fillRule="evenodd"
                d="m77.703 127.791 1.28.305v2.331l-1.28-.311v-2.325Z"
                clipRule="evenodd"
            />
            <path
                fill="#C3C3C3"
                fillRule="evenodd"
                d="m83.645 125.835-5.942 1.956 1.28.306 6.011-1.98-1.35-.282Z"
                clipRule="evenodd"
            />
            <path
                fill="#A8A8A8"
                fillRule="evenodd"
                d="m66.111 121.191-6.011 1.98v2.33l6.011-2.33v-1.98Z"
                clipRule="evenodd"
            />
            <path
                fill="#CBCBCB"
                fillRule="evenodd"
                d="m58.82 122.865 1.28.306v2.33l-1.28-.311v-2.325Z"
                clipRule="evenodd"
            />
            <path
                fill="#C3C3C3"
                fillRule="evenodd"
                d="m64.762 120.909-5.942 1.956 1.28.306 6.011-1.98-1.349-.282Z"
                clipRule="evenodd"
            />
            <path
                fill="#A8A8A8"
                fillRule="evenodd"
                d="m81.76 110.846-1.135.277v16.43l1.136-.278v-16.429Z"
                clipRule="evenodd"
            />
            <path
                fill="#C3C3C3"
                fillRule="evenodd"
                d="m79.345 111.146 1.28.305v16.102l-1.28-.311v-16.096Z"
                clipRule="evenodd"
            />
            <path
                fill="#A8A8A8"
                fillRule="evenodd"
                d="m62.928 105.871-1.185.326v16.43l1.185-.327v-16.429Z"
                clipRule="evenodd"
            />
            <path
                fill="#9C9C9C"
                fillRule="evenodd"
                d="m60.464 106.22 1.28.305v16.102l-1.28-.311V106.22Z"
                clipRule="evenodd"
            />
            <path
                fill="#C3C3C3"
                fillRule="evenodd"
                d="m58 109.244 23.76 5.717v3.694L58 112.938v-3.694Z"
                clipRule="evenodd"
            />
            <mask
                id="l"
                width={24}
                height={10}
                x={58}
                y={109}
                maskUnits="userSpaceOnUse"
                style={{
                    maskType: "luminance",
                }}
            >
                <path
                    fill="#fff"
                    fillRule="evenodd"
                    d="m58 109.244 23.76 5.717v3.694L58 112.938v-3.694Z"
                    clipRule="evenodd"
                />
            </mask>
            <g
                fill="#9C9C9C"
                fillRule="evenodd"
                clipRule="evenodd"
                mask="url(#l)"
            >
                <path d="M61.03 110.065h1.515l-4.837 4.138h-1.35l4.672-4.138ZM65.135 110.065h1.514l-4.837 4.138h-1.35l4.673-4.138ZM69.24 111.96h1.515l-4.837 4.139h-1.35l4.672-4.139ZM73.345 111.96h1.514l-4.837 4.139h-1.35l4.673-4.139ZM77.45 114.013h1.515l-4.837 4.138h-1.35l4.672-4.138ZM81.555 114.013h1.514l-4.837 4.138h-1.35l4.673-4.138Z" />
            </g>
            <path
                fill="#A8A8A8"
                fillRule="evenodd"
                d="m83.178 114.42-1.417.541v3.694l1.417-.475v-3.76Z"
                clipRule="evenodd"
            />
            <path
                fill="#9C9C9C"
                fillRule="evenodd"
                d="m59.88 108.701 23.298 5.721-1.417.539L58 109.244l1.88-.543Z"
                clipRule="evenodd"
            />
            <path
                fill="#C3C3C3"
                fillRule="evenodd"
                d="m58 102.543 23.76 5.717v3.695L58 106.238v-3.695Z"
                clipRule="evenodd"
            />
            <mask
                id="m"
                width={24}
                height={10}
                x={58}
                y={102}
                maskUnits="userSpaceOnUse"
                style={{
                    maskType: "luminance",
                }}
            >
                <path
                    fill="#fff"
                    fillRule="evenodd"
                    d="m58 102.543 23.76 5.717v3.695L58 106.238v-3.695Z"
                    clipRule="evenodd"
                />
            </mask>
            <g
                fill="#9C9C9C"
                fillRule="evenodd"
                clipRule="evenodd"
                mask="url(#m)"
            >
                <path d="M61.03 103.364h1.515l-4.837 4.139h-1.35l4.672-4.139ZM65.135 103.364h1.514l-4.837 4.139h-1.35l4.673-4.139ZM69.24 105.26h1.515l-4.837 4.138h-1.35l4.672-4.138ZM73.345 105.26h1.514l-4.837 4.138h-1.35l4.673-4.138ZM77.45 107.312h1.515l-4.837 4.139h-1.35l4.672-4.139ZM81.555 107.312h1.514l-4.837 4.139h-1.35l4.673-4.139Z" />
            </g>
            <path
                fill="#A8A8A8"
                fillRule="evenodd"
                d="m83.178 107.716-1.417.544v3.694l1.417-.475v-3.763Z"
                clipRule="evenodd"
            />
            <path
                fill="#9C9C9C"
                fillRule="evenodd"
                d="m59.88 102 23.298 5.721-1.417.539L58 102.543l1.88-.543Z"
                clipRule="evenodd"
            />
            <path
                fill="#7D7F82"
                fillRule="evenodd"
                d="m17.618 34.077-2.095-.829.317-.239 1.778 1.068Z"
                clipRule="evenodd"
            />
            <path
                fill="#7D7F82"
                fillRule="evenodd"
                d="M18.965 29.7h-9.04l3.51 5.848 5.53-5.848Z"
                clipRule="evenodd"
            />
            <path
                fill="#4D4D4D"
                fillRule="evenodd"
                d="m9.916 29.7 7.013 2.077.69 2.3L9.915 29.7Z"
                clipRule="evenodd"
            />
            <path
                stroke="#7D7F82"
                strokeDasharray="4 2"
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M47.92 85.927s15.952-2.202 15.952-11.45c-.196-5.67-1.475-6.59-3.7-7.386"
            />
            <path
                stroke="#7D7F82"
                strokeDasharray="4 2"
                strokeLinecap="round"
                d="M46.375 68.515s-31.693-.685-29.972-7.9c1.721-7.213 18.412-9.47 29.972-7.213M59.01 54.549s3.494-.458 3.494-3.504 1.046-7.757-15.953-12.158c-16.999-4.4-29.242-5.211-29.242-5.211"
            />
            <defs>
                <linearGradient
                    id="c"
                    x1={116}
                    x2={115.5}
                    y1={139.5}
                    y2={86}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.01} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient
                    id="d"
                    x1={187.305}
                    x2={190.388}
                    y1={111.587}
                    y2={105.828}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#404242" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#1E1F1F" stopOpacity={0.28} />
                </linearGradient>
                <linearGradient
                    id="e"
                    x1={112.075}
                    x2={116.016}
                    y1={118.758}
                    y2={111.353}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.28} />
                </linearGradient>
                <linearGradient
                    id="f"
                    x1={122.668}
                    x2={134.956}
                    y1={141.301}
                    y2={118.209}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.28} />
                </linearGradient>
                <linearGradient
                    id="g"
                    x1={19.835}
                    x2={24.621}
                    y1={113.633}
                    y2={104.67}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.28} />
                </linearGradient>
                <linearGradient
                    id="h"
                    x1={88.947}
                    x2={90.475}
                    y1={117.531}
                    y2={114.725}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.28} />
                </linearGradient>
                <linearGradient
                    id="i"
                    x1={151.731}
                    x2={154.263}
                    y1={106.229}
                    y2={101.532}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.28} />
                </linearGradient>
                <linearGradient
                    id="j"
                    x1={110.024}
                    x2={111.224}
                    y1={91.893}
                    y2={86.078}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#fff" stopOpacity={0.12} />
                    <stop offset={1} stopColor="#fff" stopOpacity={0.28} />
                </linearGradient>
                <clipPath id="k" transform="translate(-140.303 -78.487)">
                    <path
                        fillRule="evenodd"
                        d="M146.918 98.888a9.665 9.665 0 0 1-2.609-6.997 9.666 9.666 0 0 1 3.091-6.798c1.909-1.782 4.384-2.69 6.987-2.6a9.735 9.735 0 0 1 6.789 3.08c1.781 1.891 2.7 4.38 2.609 6.998a9.666 9.666 0 0 1-3.091 6.798 9.735 9.735 0 0 1-6.987 2.599 9.73 9.73 0 0 1-6.789-3.08Z"
                        clipRule="evenodd"
                    />
                </clipPath>
            </defs>
        </g>
    </svg>
)
