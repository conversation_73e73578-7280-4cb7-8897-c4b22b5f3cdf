import { Tag, Flex } from "antd";

interface Props {
  resultConfig: {
    icon: JSX.Element;
    color: string;
    result_reason?: string | JSX.Element;
    extra?: string | JSX.Element;
  };
}
export const Tip = ({ resultConfig }: Props) => {
  return (
    <Tag color={resultConfig.color} className="publish-tip">
      <Flex gap={10} align="flex-start">
        <div style={{ fontSize: "16px" }}>{resultConfig.icon}</div>
        <div>
          <div>{resultConfig.result_reason || ""}</div>
          {!!resultConfig.extra && <div>{resultConfig.extra}</div>}
        </div>
      </Flex>
    </Tag>
  );
};
