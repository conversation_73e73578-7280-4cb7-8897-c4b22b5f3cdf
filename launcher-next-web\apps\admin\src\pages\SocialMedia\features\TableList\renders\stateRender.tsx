import { Badge } from "antd";
import { SidebarState } from "../../../hooks/useSidebar";

export function stateRender(value: SidebarState) {
  if (value === SidebarState.Unpublished) {
    return <Badge text="未发布" color="pink" />;
  }
  if (value === SidebarState.Published) {
    return <Badge text="已发布" color="green" />;
  }
  if (value === SidebarState.OffLine) {
    return <Badge text="已下线" color="red" />;
  }
  return <div>{value}</div>;
}
