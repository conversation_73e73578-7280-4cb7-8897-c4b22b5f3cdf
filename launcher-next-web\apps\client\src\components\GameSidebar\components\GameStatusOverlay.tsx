import { useRef, useState, useEffect, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Game, GameState, DownloadState, DownloadStep } from "@/types/games"
import {
    DownloadIcon,
    QueueIcon,
    PauseIcon,
    ExceptIcon,
    LoadingIcon,
} from "../../Icon"

interface GameStatusOverlayProps {
    game_state: GameState
    download_state?: DownloadState
    onAnimationComplete?: () => void
}

export const GameStatusOverlay: React.FC<GameStatusOverlayProps> = ({
    game_state,
    download_state,
    onAnimationComplete,
}) => {
    const { step, progress } = download_state || {}
    const [overlayShow, setOverlayShow] = useState(false)
    const [progressValue, setProgressValue] = useState(0)
    const circleLength = 2 * Math.PI * 16

    useEffect(() => {
        if (progress) {
            const { from, to, value } = progress
            const progressValue = value / (to - from)
            // 客户端会有from, to, value为0的场景，导致progressValue为NaN，需要过滤
            if (!isNaN(progressValue)) {
                setProgressValue(progressValue)
            }
        }
    }, [progress])

    const statusIconElement = useMemo(() => {
        if (game_state === GameState.Download) {
            if (step === DownloadStep.Download) {
                return <DownloadIcon />
            }
            return null
        }
        switch (game_state) {
            case GameState.Queue:
                return <QueueIcon />
            case GameState.Pause:
                return <PauseIcon />
            case GameState.Except:
                return <ExceptIcon />
            default:
                return null
        }
    }, [game_state, download_state])

    return (
        <div className="absolute inset-0 flex items-center justify-center rounded-lg">
            <motion.div
                className="flex items-center justify-center rounded-lg bg-[#18171A]/[0.78] z-10"
                initial={{ width: "0", height: "0", opacity: 0 }}
                animate={{ width: "3rem", height: "3rem", opacity: 1 }}
                exit={{ width: "0", height: "0", scale: 0, opacity: 0 }}
                transition={{ delay: 0.5, duration: 0.8 }}
                onAnimationComplete={() => {
                    onAnimationComplete?.()
                    setOverlayShow(true) // 蒙层动画完成后显示游戏状态
                }}
            >
                {overlayShow && (
                    <motion.div
                        className="flex items-center justify-center absolute inset-0 z-10"
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0, opacity: 0 }}
                        transition={{ delay: 0.3, duration: 0.3 }}
                    >
                        {statusIconElement}
                    </motion.div>
                )}

                {/* 进度环 */}
                {overlayShow && progress && (
                    <motion.div
                        key={step}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ delay: 0.3, duration: 0.3 }}
                    >
                        <svg className="w-[3rem] h-[3rem]" viewBox="0 0 48 48">
                            {/* 背景圆环 */}
                            <circle
                                cx="24"
                                cy="24"
                                r="16"
                                fill="none"
                                stroke="white"
                                strokeOpacity="0.35"
                                strokeWidth="4"
                            />

                            {/* 进度圆环 */}
                            <motion.circle
                                cx="24"
                                cy="24"
                                r="16"
                                fill="none"
                                stroke="white"
                                strokeWidth="4"
                                strokeLinecap="round"
                                strokeDasharray={circleLength} // 2 * PI * r
                                strokeDashoffset={
                                    circleLength * (1 - progressValue)
                                }
                                animate={{
                                    strokeDashoffset:
                                        circleLength * (1 - progressValue),
                                }}
                                transition={{ duration: 0.3 }}
                                style={{
                                    transform: "rotate(-90deg)",
                                    transformOrigin: "50% 50%",
                                }}
                            />
                        </svg>
                    </motion.div>
                )}
            </motion.div>
        </div>
    )
}
