import { Badge } from "antd";
import {
  PackagePublishStateConfig,
  PackagePublishState,
  LauncherPackageConfig,
} from "@/pages/PackageManage/hooks/useLauncherPackageList/types";

export function stateRender(value: any, record: LauncherPackageConfig) {
  const gray_ratio = record.gray_ratio;
  const state = value as PackagePublishState;

  let config = PackagePublishStateConfig[state];
  if (config) {
    if (state === PackagePublishState.LauncherState_Gray) {
      config = { ...config, text: `${config.text} (${gray_ratio}%)` };
    }
    return <Badge {...config} />;
  }
}
