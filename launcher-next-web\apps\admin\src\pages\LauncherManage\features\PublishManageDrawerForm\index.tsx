import { useCallback, useEffect, useState } from "react";
import {
  DrawerForm,
  ProForm,
  ProFormItem,
  ProFormDateTimePicker,
  ProFormDateTimeRangePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from "@ant-design/pro-components";
import { Form, message } from "antd";
import { CloudTypeMap } from "@/data/cloudType";
import { isHG } from "@/utils/env";
import {
  HgLauncherBriefConfig,
  HgLauncherApp,
  ReleaseState,
  AppEnvType,
} from "../../types";
import { requestHgLauncherApp, saveHgLauncherApp } from "../../apis";
import { ReleaseStateOptions, AppEnvTypeOptions } from "../../constants";

interface Props {
  open: boolean;
  currentRow?: HgLauncherBriefConfig;
  onOpenChange?: (open: boolean) => void;
  onFinish?: (values?: any) => Promise<void>;
}

export function PublishManageDrawerForm({
  open,
  currentRow,
  onOpenChange,
  onFinish,
}: Props) {
  const appcode = currentRow?.appcode;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [launcherConfig, setLauncherConfig] = useState<HgLauncherApp>();

  const cloudTypeOptions = Object.entries(CloudTypeMap).reduce(
    (prev: any, [key, value]: [string, string]) => {
      const item = {
        label: value,
        value: key,
      };
      return prev.concat(item);
    },
    [],
  );

  const handleFinish = useCallback(async (values: HgLauncherApp) => {
    console.log(">>>handleFinish", values);
    try {
      const payload = {
        launcher_app: values,
      };
      await saveHgLauncherApp?.(payload);
      onFinish?.();
      message.success("提交成功");
      return true;
    } catch (error: any) {
      console.error(error);
      message.success(error.message || "提交失败");
    }
  }, []);

  useEffect(() => {
    let didCancel = false;

    const fetchData = async (appcode: string) => {
      try {
        setLoading(true);
        const params = {
          appcode,
        };
        const res = await requestHgLauncherApp(params);
        console.log("requestHgLauncherApp res", res);
        if (!didCancel) {
          form.setFieldsValue(res.launcher_app);
          setLauncherConfig(res.launcher_app);
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
    if (appcode && open) {
      fetchData(appcode);
    }
    return () => {
      didCancel = true;
    };
  }, [appcode, open]);

  return (
    <DrawerForm
      form={form}
      open={open}
      layout={"horizontal"}
      labelAlign={"left"}
      labelCol={{ span: 6 }}
      title={currentRow ? "编辑启动器" : "新建启动器"}
      width={650}
      clearOnDestroy
      drawerProps={{
        destroyOnClose: true,
        loading,
        afterOpenChange: (open) => {
          if (!open) {
            setLauncherConfig(undefined);
          }
        },
      }}
      onOpenChange={onOpenChange}
      onFinish={handleFinish}
    >
      {launcherConfig?.id && (
        <ProFormItem name="appcode" label="ID">
          {launcherConfig?.id}
        </ProFormItem>
      )}
      <ProFormText
        name="caption"
        // width="md"
        label="标识名"
        placeholder="请输入"
        rules={[{ required: true }]}
      />
      <ProFormRadio.Group
        label="启动器类型"
        name="launcher_env"
        rules={[{ required: true }]}
        disabled={!!launcherConfig?.launcher_env}
        options={AppEnvTypeOptions}
      />

      {/* 仅当应用类型为正式应用时展示 */}
      <ProFormDependency name={["launcher_env"]}>
        {({ launcher_env }) => {
          if (launcher_env !== AppEnvType.AppEnvType_PROD) return null;
          return (
            <ProFormRadio.Group
              label="访问环境"
              name="launcher_release_state"
              rules={[{ required: true }]}
              initialValue={ReleaseState.ReleaseState_INNER}
              disabled={
                launcherConfig?.launcher_release_state ===
                ReleaseState.ReleaseState_RELEASE
              }
              options={ReleaseStateOptions}
            />
          );
        }}
      </ProFormDependency>
      <ProFormText
        width="xl"
        name={["publish_setting", "inner_domain"]}
        label="nonprod访问域名"
        rules={[{ required: true }, { type: "url" }, { max: 200 }]}
      />

      <ProFormDependency name={["launcher_env", "launcher_release_state"]}>
        {({ launcher_env, launcher_release_state }) => {
          if (
            launcher_release_state !== ReleaseState.ReleaseState_RELEASE ||
            launcher_env !== AppEnvType.AppEnvType_PROD
          )
            return null;
          return (
            <>
              <ProFormText
                width="xl"
                name={["publish_setting", "public_cdn_domin"]}
                label="prod公开访问域名"
                tooltip="资源下载时，可访问的地址域名"
                rules={[{ required: true }, { type: "url" }, { max: 200 }]}
              ></ProFormText>
              {isHG && (
                <ProFormText
                  width="xl"
                  name={["publish_setting", "sign_cdn_domin"]}
                  label="prod安全访问域名"
                  tooltip="资源下载时，可访问的地址域名，资源进行了签名加密"
                  rules={[{ required: true }, { type: "url" }, { max: 200 }]}
                ></ProFormText>
              )}
              <ProFormSelect
                name={["publish_setting", "cloud_types"]}
                label="预热配置-云商列表"
                mode="multiple"
                options={cloudTypeOptions}
                width="xl"
                placeholder="请选择云商列表"
                rules={[{ required: true }]}
              />
            </>
          );
        }}
      </ProFormDependency>
    </DrawerForm>
  );
}
