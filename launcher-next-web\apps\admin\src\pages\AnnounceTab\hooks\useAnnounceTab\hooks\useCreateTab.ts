import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnounceTabKey } from "../useAnnounceTabKey";
import { CreateRequest } from "../types";

export function useCreateTab() {
  const key = useAnnounceTabKey();
  const { trigger } = useSWRMutation(key, create);
  return trigger;
}

async function create(_: any, { arg }: { arg: CreateRequest }) {
  await fetcher(`${URL_PREFIX}/create`, arg);
}
