import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { DATA_KEY, URL_PREFIX } from "../const";
import { UpdateRequest } from "../types";
import { useAppCode } from "@/hooks";

export function useMutateMainData() {
  const appCode = useAppCode();
  const { trigger } = useSWRMutation([DATA_KEY, appCode], updateMainData);
  return trigger;
}

async function updateMainData(
  _: [string, string | undefined],
  { arg }: { arg: UpdateRequest },
) {
  await fetcher(`${URL_PREFIX}/update`, arg);
}
