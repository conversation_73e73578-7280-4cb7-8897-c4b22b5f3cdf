import { useEffect, useRef } from "react"
import useEvent from "./useEvent"

export function useInterval(
    fn: () => void,
    delay: number,
    options?: {
        immediate?: boolean
    }
) {
    const callback = useEvent(fn)
    const timerRef = useRef<NodeJS.Timeout>()
    const lastRunTime = useRef(new Date().getTime())

    function reClock() {
        lastRunTime.current = new Date().getTime()
    }
    useEffect(() => {
        function check() {
            const now = new Date().getTime()
            if (now - lastRunTime.current >= delay) {
                callback()
                lastRunTime.current = now
            }
            // requestAnimationFrame(check)
            timerRef.current = setTimeout(check, delay)
        }
        check()
        if (options?.immediate) {
            callback()
        }
        return () => {
            clearTimeout(timerRef.current)
        }
    }, [callback, delay, options?.immediate])

    return {
        reClock,
    }
}
