const HG_media_list = [
  "Skland",
  "SklandTool",
  "Bilibili",
  "Weibo",
  "<PERSON>uyin",
  "Taptap",
  "Wechat",
  "Xiaohongshu",
  "CS",
] as const;
const GL_media_list = [
  "X",
  "Facebook",
  "YouTube",
  "Instagram",
  "Discord",
  "NAVER_GAME",
  "NAVER_CAFE",
  "Line",
  "Reddit",
  "VK",
  "TikTok",
  "Twitch",
  "CS_gl",
] as const;
const media_list = [...HG_media_list, ...GL_media_list];

export const Media = Object.fromEntries(
  media_list.map((key) => [key, key]),
) as {
  [K in (typeof media_list)[number]]: K;
};

export const MediaConfig = {
  [Media.Skland]: {
    label: "森空岛",
    icon: "Skland",
  },
  [Media.SklandTool]: {
    label: "森空岛工具箱",
    icon: "SklandTool",
  },
  [Media.Bilibili]: {
    label: "<PERSON><PERSON>bili",
    icon: "Bilibili",
  },
  [Media.Weibo]: {
    label: "微博",
    icon: "Weibo",
  },
  [Media.Douyin]: {
    label: "抖音",
    icon: "Douyin",
  },
  [Media.Taptap]: {
    label: "Taptap",
    icon: "Taptap",
  },
  [Media.Wechat]: {
    label: "微信",
    icon: "Wechat",
  },
  [Media.Xiaohongshu]: {
    label: "小红书",
    icon: "Xiaohongshu",
  },
  [Media.CS]: {
    label: "客服",
    icon: "CS",
  },

  [Media.X]: {
    label: "X",
    icon: "X",
  },
  [Media.CS_gl]: {
    label: "客服",
    icon: "CS_gl",
  },
  [Media.Discord]: {
    label: "Discord",
    icon: "Discord",
  },

  [Media.Facebook]: {
    label: "Facebook",
    icon: "Facebook",
  },
  [Media.Instagram]: {
    label: "Instagram",
    icon: "Instagram",
  },
  [Media.Line]: {
    label: "Line",
    icon: "Line",
  },
  [Media.NAVER_CAFE]: {
    label: "NAVER_CAFE",
    icon: "NAVER_CAFE",
  },
  [Media.NAVER_GAME]: {
    label: "NAVER_GAME",
    icon: "NAVER_GAME",
  },
  [Media.Reddit]: {
    label: "Reddit",
    icon: "Reddit",
  },

  [Media.TikTok]: {
    label: "TikTok",
    icon: "TikTok",
  },
  [Media.Twitch]: {
    label: "Twitch",
    icon: "Twitch",
  },
  [Media.VK]: {
    label: "VK",
    icon: "VK",
  },

  [Media.YouTube]: {
    label: "YouTube",
    icon: "YouTube",
  },
};

const isHG = /hypergryph\.net/.test(location.host);
const curMediaList = isHG ? HG_media_list : GL_media_list;

export const mediaList = curMediaList.map((item) => {
  return {
    value: item,
    label: MediaConfig[item].label,
  };
});
