import React, { useRef, useState } from "react";
import { Page } from "@hg-omc/layouts";
import {
  Card,
  Flex,
  Input,
  Select,
  DatePicker,
  Tag,
  Button,
  TableColumnsType,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { DrawerForm } from "@ant-design/pro-components";
import { Provider } from "jotai";
import { useNavigate } from "@hg/lego-app";
import { SearchTable, SearchTableProps } from "@/features/SearchTable";

import { requestHgLauncherAppList } from "./apis";
import { HgLauncherBriefConfig } from "./types";
import { useColumns } from "./hooks/useColumns";
import { ReleaseStateOptions, AppEnvTypeOptions } from "./constants";
import { PublishManageDrawerForm } from "./features/PublishManageDrawerForm";
import styles from "./index.module.less";

function LauncherManage() {
  const nav = useNavigate();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<
    HgLauncherBriefConfig | undefined
  >();
  const tableRef = useRef<React.ElementRef<typeof SearchTable>>(null);

  const actionCallback = (record: HgLauncherBriefConfig, action: string) => {
    console.log(record, action);
    if (action === "edit") {
      setCurrentRow(record);
      setModalOpen(true);
    } else if (action === "publishManage") {
      const path = `/package-manage/?appCode=${record.appcode}&caption=${record.caption}`;
      nav(path);
    }
  };

  const handleAdd = () => {
    setCurrentRow(undefined);
    setModalOpen(true);
  };

  const columns = useColumns(actionCallback);

  const searchFormConfig: SearchTableProps["searchForm"] = {
    items: [
      {
        label: "标识名",
        name: "caption",
        children: <Input placeholder="请输入" />,
      },
      {
        label: "启动器类型",
        name: "launcher_envs",
        children: (
          <Select
            options={AppEnvTypeOptions}
            mode="multiple"
            placeholder="请选择"
            allowClear
          ></Select>
        ),
      },
      {
        label: "访问环境",
        name: "launcher_release_states",
        children: (
          <Select
            options={ReleaseStateOptions}
            mode="multiple"
            placeholder="请选择"
            allowClear
          ></Select>
        ),
      },
    ],
  };

  const actionBar = (
    <Flex justify="start" style={{ marginBottom: 16 }}>
      <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
        新增
      </Button>
    </Flex>
  );

  return (
    <Provider>
      <Page>
        <SearchTable
          ref={tableRef}
          title="启动器列表"
          service={requestHgLauncherAppList}
          searchForm={searchFormConfig}
          tableProps={{
            columns,
            rowKey: "id",
            rowClassName: (_, index) => (index % 2 === 0 ? "" : styles.rowDark),
            pagination: {
              showSizeChanger: true,
            },
          }}
          actionBar={actionBar}
          paginationParamsMapping={{
            current: "page_num",
            size: "page_size",
          }}
          responseParamsMapping={{
            launcher_briefs: "records",
          }}
        ></SearchTable>
        <PublishManageDrawerForm
          open={modalOpen}
          currentRow={currentRow}
          onOpenChange={(open: boolean) => {
            setModalOpen(open);
            if (open === false) {
              setCurrentRow(undefined);
            }
          }}
          onFinish={async () => {
            console.log("提交成功");
            setModalOpen(false);
            tableRef.current?.request();
          }}
        />
      </Page>
    </Provider>
  );
}

export default LauncherManage;
