import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { DATA_KEY, URL_PREFIX } from "../const";
import { PublishRequest } from "../types";
import { useAppCode } from "@/hooks";

export function usePublishMainData() {
  const appCode = useAppCode();
  const { trigger } = useSWRMutation([DATA_KEY, appCode], publishMainData);
  return trigger;
}

async function publishMainData(
  _: [string, string | undefined],
  { arg }: { arg: PublishRequest },
) {
  await fetcher(`${URL_PREFIX}/publish`, arg);
}
