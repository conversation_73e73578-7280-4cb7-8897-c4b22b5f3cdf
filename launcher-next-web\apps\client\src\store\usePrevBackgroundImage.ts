import { atom, useAtom, useAtomValue, useSetAtom } from "jotai"

export const prevBackgroundImageAtom = atom<string | null>(null)

export function usePrevBackgroundImage() {
    return useAtom(prevBackgroundImageAtom)
}

export function usePrevBackgroundImageValue() {
    return useAtomValue(prevBackgroundImageAtom)
}

export function useSetPrevBackgroundImage() {
    return useSetAtom(prevBackgroundImageAtom)
}
