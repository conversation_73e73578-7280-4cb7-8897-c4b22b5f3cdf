import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useBannerKey } from "../useBannerKey";
import { CreateBannerRequest } from "../types";

export function useCreateBanner() {
  const key = useBannerKey();
  const { trigger } = useSWRMutation(key, createBanner);
  return trigger;
}

async function createBanner(_: any, { arg }: { arg: CreateBannerRequest }) {
  await fetcher(`${URL_PREFIX}/create`, arg);
}
