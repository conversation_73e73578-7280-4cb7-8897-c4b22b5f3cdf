import React, { useState, useRef } from "react"
import clsx from "clsx"
import { motion, AnimatePresence } from "framer-motion"
import { Game, GameState } from "@/types/games"
import { fetchQtCallback } from "@/utils/qwebchannel"
import { downloaded_game_list_rsp } from "@/utils/mockData"
import { Tooltip } from "@/components/Tooltip"
import { useGlobalDataValue } from "@/store/useGlobalData"
import { getQtPath } from "@/utils/misc"
import { FallbackImage } from "@/components/FallbackImage"
import { Channel } from "@/types/channel"
import { GameStatusOverlay } from "./GameStatusOverlay"

interface GameIconItemProps {
    game: Game
    isSelected: boolean
    isNewGame: boolean
    onSelect: (uuid: string) => void
    onAnimationComplete?: (uuid: string) => void
}

export const GameIconItem: React.FC<GameIconItemProps> = ({
    game,
    isSelected,
    isNewGame,
    onSelect,
    onAnimationComplete,
}) => {
    const qtPath = getQtPath()
    const globalData = useGlobalDataValue()
    const hgChannel = globalData.hg_channel_name
    const containerRef = useRef<HTMLDivElement>(null)
    // console.log(">>>GameIconItem", game)
    const {
        game_name,
        app_code,
        channel,
        sub_channel,
        region,
        uuid,
        is_user_visible,
        is_stop_service,
        theme,
        game_state,
        download_state,
    } = game

    const [showTooltip, setShowTooltip] = useState(false)
    const icon_url = `${qtPath}/theme/${theme}/channel/${hgChannel}/game_icon.png`
    return (
        <div ref={containerRef} className="relative">
            <motion.div
                className={clsx(
                    "flex justify-center items-center",
                    "p-[2.5px] rounded-[12px] border border-[2px]",
                    "cursor-pointer"
                )}
                whileHover={{
                    borderColor: isSelected
                        ? "rgba(255, 255, 255, 0.75)"
                        : "rgba(255, 255, 255, 0.35)",
                }}
                // whileTap={{ borderColor: "rgba(255, 255, 255, 0.75)" }}
                initial={{
                    borderColor: "rgba(255, 255, 255, 0)",
                }}
                animate={{
                    borderColor: isSelected
                        ? "rgba(255, 255, 255, 0.75)"
                        : "rgba(255, 255, 255, 0)",
                }}
                transition={{ duration: 0.3, delay: isNewGame ? 1.6 : 0 }}
                onClick={() => onSelect(game.uuid)}
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
            >
                <div
                    className="relative box-content w-[3rem] h-[3rem] border-[0.5px] border-white/10 rounded-[8px]"
                    style={{
                        boxShadow: "0px 2px 6px 0px rgba(0, 0, 0, 0.55)",
                    }}
                >
                    <FallbackImage
                        src={icon_url}
                        fallback={`${qtPath}/theme/${theme}/channel/${Channel.official}/game_icon.png`}
                        alt="logo"
                        className="w-full h-full rounded-[8px] object-cover"
                    />
                    <AnimatePresence>
                        {game_state !== GameState.Installed &&
                            game_state !== GameState.Uninstalled && (
                                <GameStatusOverlay
                                    game_state={game_state}
                                    download_state={download_state}
                                    onAnimationComplete={() => {
                                        onAnimationComplete?.(uuid)
                                    }}
                                ></GameStatusOverlay>
                            )}
                    </AnimatePresence>
                </div>
            </motion.div>

            <Tooltip
                triggerElement={containerRef.current}
                title={
                    window.isDebug ? `${game_name}｜uuid: ${uuid}` : game_name
                }
                show={showTooltip}
                placement="right-center"
                offset={8}
            />
        </div>
    )
}
