import { useEffect, useState } from "react"
import { SingleEntImage } from "@/types/mainData"
import { imagePreload } from "@/utils/imagePreload"
import bridge from "@/utils/bridge"

export const SingleEnt = ({
    data,
    appcode,
}: {
    data?: SingleEntImage
    appcode?: string
}) => {
    const { version_url, button_url, button_hover_url, jump_url } = data || {}
    const [isHover, setIsHover] = useState(false)
    const handleJump = () => {
        if (jump_url) {
            bridge.open(
                { url: jump_url, isNeedToken: true },
                "single_ent",
                {},
                "launcher_hotspot_jump_event",
                {
                    appcode,
                    jump_url,
                }
            )
        }
    }
    useEffect(() => {
        if (button_hover_url && version_url && button_url) {
            imagePreload(button_hover_url)
        }
    }, [button_hover_url])

    if (!version_url || !button_url) return null

    return (
        <div className="w-[518px] flex flex-col items-start gap-4 z-10">
            <img src={version_url} alt="image" className="max-h-[180px]" />
            <img
                src={isHover ? button_hover_url : button_url}
                alt="image"
                className="max-h-[40px] ml-[2.5rem] cursor-pointer"
                onMouseEnter={() => setIsHover(true)}
                onMouseLeave={() => setIsHover(false)}
                onClick={handleJump}
            />
        </div>
    )
}
