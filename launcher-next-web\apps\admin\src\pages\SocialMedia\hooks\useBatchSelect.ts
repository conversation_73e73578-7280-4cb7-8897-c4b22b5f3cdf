import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { SidebarConfig } from "./useSidebar/types";

export const batchSelectAtom = atom<SidebarConfig[]>([]);

export function useBatchSelect() {
  return useAtom(batchSelectAtom);
}

export function useBatchSelectValue() {
  return useAtomValue(batchSelectAtom);
}

export function useSetBatchSelect() {
  return useSetAtom(batchSelectAtom);
}
