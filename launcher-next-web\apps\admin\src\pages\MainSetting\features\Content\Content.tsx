import {
  useEffect,
  useImperativeHandle,
  forwardRef,
  version,
  useMemo,
} from "react";
import { Form, Input, message } from "antd";
import {
  useMainData,
  useOnCancel,
  useOnSave,
  useMutateMainData,
  MainPageMainPageData,
  useEditState,
  useCurrentLangValue,
  MainPageData,
} from "../../hooks";
import { OssUpload } from "@/features";
import { useAppCode } from "@/hooks";
import { EditState } from "../../types";

type IValue = MainPageMainPageData;

interface IProps {}

export const Content = forwardRef<{ submit: () => void }, IProps>(
  (props, ref) => {
    const [editState, setEditState] = useEditState();
    const { currentConfigData, currentConfig } = useMainData();

    const [form] = Form.useForm();
    const mutateMainData = useMutateMainData();
    const currentLangValue = useCurrentLangValue();
    const appcode = useAppCode();
    const disabled = useMemo(
      () => editState === EditState.Readonly,
      [editState],
    );

    const formatMainData = (mainPageData?: MainPageData) => {
      const {
        version_url,
        version_md5,
        button_url,
        button_md5,
        button_hover_url,
        button_hover_md5,
        jump_url,
      } = mainPageData?.single_ent_image || {};
      return {
        ...mainPageData,
        single_ent_image: {
          version: {
            url: version_url,
            md5: version_md5,
          },
          button: {
            url: button_url,
            md5: button_md5,
          },
          button_hover: {
            url: button_hover_url,
            md5: button_hover_md5,
          },
          jump_url,
        },
      };
    };

    useOnCancel(() => {
      form.resetFields(["main_bg_image"]);
      form.setFieldsValue(formatMainData(currentConfigData));
    });

    useImperativeHandle(ref, () => ({
      submit: () => {
        form.submit();
      },
    }));

    useEffect(() => {
      if (!currentConfigData) {
        form.resetFields();
        return;
      }
      form.setFieldsValue(formatMainData(currentConfigData));
    }, [currentConfigData]);

    return (
      <Form<IValue>
        form={form}
        wrapperCol={{ span: 12 }}
        labelCol={{ style: { fontWeight: "bold" } }}
        // initialValues={currentConfigData}
        disabled={disabled}
        style={{ paddingLeft: "80px" }}
        onFinish={async (values) => {
          console.log("values", values);
          const { version, button, button_hover } =
            values?.single_ent_image as any;
          const modify_data = {
            ...values,
            single_ent_image: {
              version_url: version?.url,
              version_md5: version?.md5,
              button_url: button?.url,
              button_md5: button?.md5,
              button_hover_url: button_hover?.url,
              button_hover_md5: button_hover?.md5,
              jump_url: values?.single_ent_image?.jump_url,
            },
          };
          console.log(">>>modify_data", modify_data);
          try {
            await mutateMainData({
              id: currentConfig?.id,
              appcode: currentConfig?.appcode || appcode,
              modify_data: modify_data,
              language: currentLangValue,
            });
            setEditState(EditState.Readonly);
            message.success("操作成功");
          } catch (error) {
            message.error("保存失败");
          }
        }}
        // layout="inline"
      >
        <Form.Item
          name={"main_bg_image"}
          label="主背景图"
          tooltip={"支持 png jpeg jpg 格式的图片，分辨率须为 2856 x 1914"}
          rules={[{ required: true }]}
        >
          <OssUpload
            maxCount={1}
            md5
            limit={{ aspectRatio: { width: 952, height: 638 } }}
            disabled={disabled}
          />
        </Form.Item>
        <Form.Item
          label="独立入口"
          tooltip={"支持 png jpeg jpg 格式的图片，分辨率须为 2856 x 1914"}
          labelCol={{ style: { fontWeight: "bold" }, span: 24 }}
        >
          <div
            style={{
              padding: "20px 0",
              // backgroundColor: "rgba(0, 0, 0, 0.05)",
            }}
          >
            <Form.Item
              name={["single_ent_image", "version"]}
              label="版本信息"
              labelCol={{ span: 5 }}
            >
              <OssUpload
                maxCount={1}
                md5
                limit={{ size: 5 }}
                description="图片大小小于5M"
                disabled={disabled}
              />
            </Form.Item>
            <Form.Item
              name={["single_ent_image", "button"]}
              label="按钮默认态"
              labelCol={{ span: 5 }}
            >
              <OssUpload
                maxCount={1}
                md5
                limit={{ size: 5 }}
                description="图片大小小于5M"
                disabled={disabled}
              />
            </Form.Item>
            <Form.Item
              name={["single_ent_image", "button_hover"]}
              label="按钮悬浮态"
              labelCol={{ span: 5 }}
            >
              <OssUpload
                maxCount={1}
                md5
                limit={{ size: 5 }}
                description="图片大小小于5M"
                disabled={disabled}
              />
            </Form.Item>
            <Form.Item
              label="跳转链接"
              name={["single_ent_image", "jump_url"]}
              labelCol={{ span: 5 }}
              rules={[{ type: "url" }]}
            >
              <Input />
            </Form.Item>
          </div>
        </Form.Item>
      </Form>
    );
  },
);

Content.displayName = "Content";
