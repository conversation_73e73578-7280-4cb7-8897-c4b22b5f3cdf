import { useAppCode } from "@/hooks";
import { Popconfirm, Typography } from "antd";
import { ActionProps } from "../types";
import { useCancelPublish } from "@/pages/Announcement/hooks";

export function CancelPublish({ announcement }: ActionProps) {
  const appcode = useAppCode();
  const cancelPublish = useCancelPublish();
  return (
    <Popconfirm
      title="是否确定要下线此公告 ？"
      onConfirm={() =>
        cancelPublish({
          appcode,
          id: announcement.id,
        })
      }
    >
      <Typography.Link type="warning">下线</Typography.Link>
    </Popconfirm>
  );
}
