import path from "path"
import config from "../../config/config.web.json"

const PROJECT_ROOT = process.cwd()

// const publicPath = `${config.publish.cdn ? `${config.publish.cdn}/` : ""}${config.publish.publicPath}`
const publicPath = "./"
export default {
    devServer: {
        open: false,
        allowedHosts: [".hypergryph.com", ".hypergryph.net"],
        port: 3001,
    },
    output: {
        publicPath,
    },
    html: {
        index: {
            template: path.resolve(PROJECT_ROOT, "./src/index.html"),
            filename: "index.html",
            favicon: path.resolve(PROJECT_ROOT, "./src/assets/favicon.ico"),
        },
    },
    definitions: {
        __REQUEST_HOST: "/",
        __SDK_SRC: config.sdk.src,
        __SDK_HOST: config.sdk.host,
        __SDK_DOMAIN: config.sdk.domain,
        __SDK_SUB_DOMAIN: config.sdk.subDomain,
        _SENTRY_DSN: config.sentryDsn,
        __SERVER_URL: config.serverUrl,
    },
    sentry: {
        ...config.sentryWebpackPlugin,
        uploadUrlPrefix: publicPath,
    },
}
