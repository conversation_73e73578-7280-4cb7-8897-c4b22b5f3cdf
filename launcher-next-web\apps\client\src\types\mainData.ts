export interface WebBatchProxyRsp {
    proxy_rsps: WebProxyRsp[]
}

// get_hg_sidebar
export interface GetHgSidebarsRsp {
    sidebars: SidebarData[]
    data_version: string
}

export interface SidebarData {
    // type?: SidebarType; // 类型
    display_type?: SidebarType // 展示类型
    media?: string // 社媒
    jump_url?: string // 跳转链接
    pic?: SideBarPic // Sidebar图片&描述
    sidebar_labels?: SidebarLabel[] // 标签列表
}

export interface SideBarPic {
    url?: string // Sidebar图片链接
    md5?: string // Sidebar图的md5值
    description?: string // sidebar图片描述
}

export interface SidebarLabel {
    content?: string // 标签名称
    jump_url?: string // 标签跳转链接
}

export enum SidebarType {
    DisplayType_RESERVE = "DisplayType_RESERVE",
    DisplayType_LIST = "DisplayType_LIST", // 列表展示
    DisplayType_GRID = "DisplayType_GRID", // 宫格展示
    DisplayType_DEFAULT = "DisplayType_DEFAULT", // 无交互反馈
}

// get_main_bg_image
export interface GetMainPageReq {
    appcode: string
    language: string
    channel: string
    sub_channel: string
    platform: string
}
export interface GetMainPageRsp {
    data_version: string
    main_bg_image: MainBgImage
}
export interface MainBgImage {
    url: string
    md5: string
}

// get_single_ent
export interface GetSingleEntRsp {
    data_version: string
    single_ent: SingleEntImage // 独立入口图片
}

export interface SingleEntImage {
    version_url?: string // 版本信息图片
    version_md5?: string // 版本信息图片的md5值
    jump_url?: string // 跳转链接
    button_url?: string // 按钮默认图片
    button_md5?: string // 按钮默认图片的md5值
    button_hover_url?: string // 按钮悬浮图片
    button_hover_md5?: string // 按钮悬浮图片的md5值
}

// get_banner
export interface GetBannerRsp {
    banners: Banner[]
    data_version: string
    code?: number
}

export interface Banner {
    jump_url?: string
    md5: string
    url: string
}

// get_announcement
export interface GetAnnouncementRsp {
    data_version: string
    tabs: AnnouncementTab[]
    code?: number
}

export interface AnnouncementTab {
    announcements: Announcement[]
    tabName: string
    tab_id: string
}

export interface Announcement {
    content: string
    jump_url: string
    start_ts: string
}

export interface Announcement {
    content: string
    jump_url: string
    start_ts: string
}

export type WebProxyRsp =
    | {
          kind: "get_hg_sidebar"
          get_hg_sidebar_rsp: GetHgSidebarsRsp
      }
    | {
          kind: "get_banner"
          get_banner_rsp: GetBannerRsp
      }
    | {
          kind: "get_announcement"
          get_announcement_rsp: GetAnnouncementRsp
      }
    | {
          kind: "get_main_bg_image"
          get_main_bg_image_rsp: GetMainPageRsp
      }
    | {
          kind: "get_single_ent"
          get_single_ent_rsp: GetSingleEntRsp
      }
