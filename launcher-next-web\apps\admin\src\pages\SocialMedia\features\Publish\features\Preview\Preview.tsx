import { SidebarConfig, SidebarState } from "@/pages/SocialMedia/hooks";
import { Flex, Space, Typography } from "antd";
import { SidebarItem } from "./SidebarItem";
import "./Preview.less";

interface Props {
  sidebars: SidebarConfig[];
}

export function Preview({ sidebars }: Props) {
  // const [showTimingSidebars, setTimingSidebars] = useState(true);
  // const showSidebars = sidebars.filter((sidebar) =>
  //   showTimingSidebars ? true : sidebar.state === SidebarState.Published,
  // );
  return (
    <Space direction="vertical">
      <Typography.Text>效果预览：</Typography.Text>
      <div className="preview-container">
        <div className="sidebar">
          {sidebars.map((item) => {
            return <SidebarItem key={item.id} {...item.data}></SidebarItem>;
          })}
        </div>
      </div>
    </Space>
  );
}
