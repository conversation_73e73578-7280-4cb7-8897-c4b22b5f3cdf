import { jsonStringify } from "./json"

const bridge = {
    open(
        {
            url,
            isNeedToken = false,
            type,
        }: { url?: string; isNeedToken?: boolean; type?: string },
        gameTarget?: string,
        gameEventData?: Record<string, any>,
        launcherEventName?: string,
        launcherEventData?: Record<string, any>
    ) {
        if (!window.jsbridge) {
            url && window.open(url)
            return
        }
        // console.log(jsbridge)
        const eventData = {
            page: "home",
            target: gameTarget,
            url: url,
            ...gameEventData,
        }

        if (gameTarget) this.eventLog("game", "click", eventData)
        if (launcherEventName)
            this.eventLog(
                "launcher",
                launcherEventName,
                launcherEventData || {}
            )
        console.log("SendMsgToNative", "openUrl", url, type)

        window.jsbridge.SendMsgToNative(
            "openUrl",
            jsonStringify({
                type,
                url,
                isNeedToken,
            })
        )
    },

    /**
     * 事件埋点
     * @param eventLogType
     * @param eventName
     * @param eventData
     */
    eventLog(
        eventLogType: string, // 表示事件类型，目前有游戏方提出的埋点需求和启动器方提出的埋点需求，"game":表示游戏方关注的埋点 "launcher":表示启动器方关注的埋点 "all":表示双方关注的埋点
        eventName: string, // 表示事件类型，埋点需求方定义的字符串
        eventData: Record<string, any> // 表示要上报的数据 为json格式)
    ) {
        console.log("===bridge eventLog===", eventLogType, eventName, eventData)
        if (!window.jsbridge) {
            return
        }
        jsbridge.SendMsgToNative(
            "eventLog",
            jsonStringify({
                eventLogType,
                eventName,
                eventData: jsonStringify(eventData),
            })
        )
    },
}

export default bridge
