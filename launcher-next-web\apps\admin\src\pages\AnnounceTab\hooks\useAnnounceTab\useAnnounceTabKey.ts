import { useAppCode } from "@/hooks";
import { DATA_KEY } from "./const";
import { usePageValue } from "../usePage";
import { useSearchValue } from "../useSearch";
import { useSort, useSortValue } from "../useSort";
import { useFilterValue } from "@/pages/Banner/hooks/useFilter";

export function useAnnounceTabKey(filterLanguage?: string) {
  const page = usePageValue();
  const sort = useSortValue();
  const searchVal = useSearchValue();
  const appCode = useAppCode();
  const filter = useFilterValue();

  return [
    DATA_KEY,
    appCode,
    { page, searchVal, sort, filter },
    filterLanguage,
  ] as const;
}
