@mixin loadFont($fontName, $fontFile) {
    @font-face {
        font-family: $fontName;
        font-style: normal;
        font-weight: normal;
        src:
            local($fontFile),
            url("@/assets/fonts/#{$fontFile}.woff2") format("woff2"),
            url("@/assets/fonts/#{$fontFile}.woff") format("woff"),
            url("@/assets/fonts/#{$fontFile}.ttf") format("truetype"),
            url("@/assets/fonts/#{$fontFile}.eot") format("embedded-opentype"),
            url("@/assets/fonts/#{$fontFile}.svg") format("svg");
    }
}

$fontSansRegular: "SourceHanSans-Regular";
$fontSansHeavy: "SourceHanSans-Heavy";
$fontSansBold: "SourceHanSans-Bold";

$default: PingFangSC, "Microsoft YaHei", sans-serif;

// :global {
//     @include loadFont("SourceHanSans-Regular", "SourceHanSans-Regular");
//     @include loadFont("SourceHanSans-Heavy", "SourceHanSans-Heavy");
//     @include loadFont("SourceHanSans-Bold", "SourceHanSans-Bold");
// }
