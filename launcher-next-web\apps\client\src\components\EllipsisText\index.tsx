import React, { useEffect, useRef, useState } from "react"
import { Tooltip } from "../Tooltip"

const isOverflowFn = (el: Element) => {
    const { clientWidth, scrollWidth } = el
    return scrollWidth > clientWidth
}

export const EllipsisText: React.FC<{
    text: string
}> = ({ text }) => {
    const textRef = useRef<HTMLSpanElement>(null)
    const [isOverflow, setIsOverflow] = useState(false)

    const handleMouseEnter = () => {
        if (!textRef.current) return
        setIsOverflow(isOverflowFn(textRef.current))
    }

    const contentNode = (
        <span
            ref={textRef}
            className="inline-block w-full h-full truncate"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={() => setIsOverflow(false)}
        >
            {text}
        </span>
    )

    return isOverflow ? (
        <Tooltip title={text} placement="top-center" offset={16}>
            {contentNode}
        </Tooltip>
    ) : (
        contentNode
    )
}
