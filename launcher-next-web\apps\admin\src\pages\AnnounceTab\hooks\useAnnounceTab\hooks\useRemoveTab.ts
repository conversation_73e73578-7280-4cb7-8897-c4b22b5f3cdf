import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../const";
import { useAnnounceTabKey } from "../useAnnounceTabKey";
import { RemoveBannerRequest } from "../types";

export function useRemoveTab() {
  const key = useAnnounceTabKey();
  const { trigger } = useSWRMutation(key, remove);
  return trigger;
}

async function remove(_: any, { arg }: { arg: RemoveBannerRequest }) {
  await fetcher(`${URL_PREFIX}/delete`, arg);
}
