export enum GameType {
  Game_TYPE_RESERVE = "Game_TYPE_RESERVE",
  Game_TYPE_BUYOUT = "Game_TYPE_BUYOUT", //买断制游戏
  Game_TYPE_NON_BUYOUT = "Game_TYPE_NON_BUYOUT", //非买断制游戏
}

export const GameTypeLabelMap: { [key in GameType]: string } = {
  [GameType.Game_TYPE_BUYOUT]: "买断制游戏",
  [GameType.Game_TYPE_NON_BUYOUT]: "非买断制游戏",
  [GameType.Game_TYPE_RESERVE]: "",
};

export enum AppGameState {
  STATE_RESERVE = "STATE_RESERVE",
  STATE_UNPUBLISH = "STATE_UNPUBLISH", // 未上线
  STATE_APPOINTMENT = "STATE_APPOINTMENT", // 预约期
  STATE_ONLINE_PRESALE = "STATE_ONLINE_PRESALE", // 上线期（预购）
  STATE_ONLINE_PREDOWNLOAD = "STATE_ONLINE_PREDOWNLOAD", // 上线期（预下载）
  STATE_ONLINE = "STATE_ONLINE", // 上线期
  STATE_OFFLINE = "STATE_OFFLINE", // 下线
}

export const AppGameStateTagConfig: {
  [key in AppGameState]: { text: string; color?: string };
} = {
  [AppGameState.STATE_RESERVE]: {
    text: "",
    color: "default",
  },
  [AppGameState.STATE_UNPUBLISH]: {
    text: "未上线",
    color: "default",
  },
  [AppGameState.STATE_APPOINTMENT]: {
    text: "预约期",
    color: "processing",
  },
  [AppGameState.STATE_ONLINE_PRESALE]: {
    text: "上线期（预购）",
    color: "success",
  },
  [AppGameState.STATE_ONLINE_PREDOWNLOAD]: {
    text: "上线期（预下载）",
    color: "success",
  },
  [AppGameState.STATE_ONLINE]: {
    text: "上线期",
    color: "success",
  },
  [AppGameState.STATE_OFFLINE]: {
    text: "下线",
    color: "error",
  },
};

export enum GeneralPlatform {
  mobile = "mobile",
  console = "console",
}

export const GeneralPlatformLabelMap = {
  [GeneralPlatform.mobile]: "手机游戏",
  [GeneralPlatform.console]: "主机游戏",
};

export enum ScheduleState {
  SCHEDULE_RESERVE = "SCHEDULE_RESERVE",
  SCHEDULE_NO_SCHEDULE = "SCHEDULE_NO_SCHEDULE", // 未定时
  SCHEDULE_SCHEDULING = "SCHEDULE_SCHEDULING", // 定时中
  SCHEDULE_CANCEL = "SCHEDULE_CANCEL", // 定时中
}

export interface GrayPublishSetting {}

export interface AppConfig {
  id: string; // uint64, 使用字符串处理大数
  appcode: string;
  name?: string;
  avatar?: string; // omc topic图标
  create_time?: number; // int64, 时间戳
  icon_url?: string; // 游戏icon
  game_type: GameType; // 游戏类型
  general_platform: GeneralPlatform; // 平台: 手机 (mobile), 主机 (console)
  slogan?: string; // 默认宣传语
  selling_point?: string; // 卖点信息
  description?: string; // 游戏简介
  cover_url?: string; // 列表封面图
  detail_header_url?: string; // 游戏详情页头图
  propaganda_urls?: string[]; // 游戏宣传图
  developer_name?: string; // 游戏开发者名称
  privacy_url?: string; // 隐私政策链接
  permission_url?: string; // 应用权限说明链接
  age_classification?: string; // 适龄分级
  android_system_requirement?: string; // android系统配置要求
  purchase_url?: string; // 购买网页跳转链接
  ios_app_store_id?: string; // App Store应用商店ID
  official_website_url?: string; // 官网链接
  state: AppGameState; // 游戏状态
  android_package_name?: string; // 安卓包名
  ios_deeplink?: string; // ios app 唤起链接
  console_system_requirement?: string; // 主机系统配置要求
  schedule_time?: number; // 定时发布时间，单位： ms
  schedule_state?: ScheduleState; // 定时任务状态
  mversion?: string; // 乐观锁，使用字符串处理大数
  gray_publish_setting?: GrayPublishSetting; // 内发配置
}

export enum ReleaseState {
  ReleaseState_RESERVE = "ReleaseState_RESERVE",
  ReleaseState_INNER = "ReleaseState_INNER", // 内部访问
  ReleaseState_RELEASE = "ReleaseState_RELEASE", // 外部可访问
}

export enum AppEnvType {
  AppEnvType_RESERVE = "AppEnvType_RESERVE",
  AppEnvType_QA = "AppEnvType_QA", // 测试应用
  AppEnvType_PROD = "AppEnvType_PROD", // 正式应用
}

export interface LauncherConfig {
  launcher_app_env_type?: AppEnvType; // 应用类型
  launcher_release_state?: ReleaseState; // 是否外部可访问
  disable_launch_game?: boolean; // 禁止启动器启动游戏
  publish_setting: LauncherPublishSetting; // launcher发布配置
}

export interface LauncherPublishSetting {
  cloud_types?: string[]; // 云商列表
  inner_domain?: string; // 内部测试资源域名
  public_cdn_domin?: string; // 非加签公开资源域名
  sign_cdn_domin?: string; // 加签域名
}

export interface ListLauncherAppReply {
  app: AppConfig;
  launcher_config: LauncherConfig;
}

export interface UpdateLauncherSettingRequest {
  appcode: string;
  launcher_config: LauncherConfig;
}
