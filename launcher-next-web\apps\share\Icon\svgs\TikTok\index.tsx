import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M21.855 13.58c-1.353 0-2.67-.44-3.752-1.253v5.676c0 2.9-2.234 5.247-4.99 5.247-2.755 0-4.988-2.348-4.988-5.247 0-2.898 2.233-5.246 4.989-5.246.275 0 .543.024.804.07v3.006a2.14 2.14 0 0 0-.783-.148c-1.23 0-2.227 1.047-2.227 2.341 0 1.292.997 2.341 2.227 2.341 1.228 0 2.225-1.049 2.225-2.34V6.75h2.783c0 2.167 1.67 3.924 3.732 3.924v2.905l-.02.001Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33.749}
        height={36.5}
        x={-1.875}
        y={3.75}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={7} />
        <feGaussianBlur stdDeviation={5} />
        <feColorMatrix values="0 0 0 0 0.839142 0 0 0 0 0.839263 0 0 0 0 0.839115 0 0 0 0.2 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1137_50773"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1137_50773"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
