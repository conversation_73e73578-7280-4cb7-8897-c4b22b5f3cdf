import { useEffect, useRef } from "react"

export function useClickOutside(cb: () => void) {
    const nodeRef = useRef<HTMLDivElement>(null)
    const cbRef = useRef(cb)
    cbRef.current = cb

    useEffect(() => {
        function handleClickOutside(event: any) {
            if (nodeRef.current && !nodeRef.current.contains(event.target)) {
                cbRef.current()
            }
        }

        document.addEventListener("mousedown", handleClickOutside)
        return () => {
            document.removeEventListener("mousedown", handleClickOutside)
        }
    }, [])

    return { nodeRef }
}
