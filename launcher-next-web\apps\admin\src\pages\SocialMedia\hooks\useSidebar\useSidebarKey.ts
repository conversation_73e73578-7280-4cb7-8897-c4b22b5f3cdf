import { useAppCode } from "@/hooks";
import { URL_PREFIX } from "./const";
import { usePageValue } from "../usePage";
import { useSortValue } from "../useSort";
import { useFilterValue } from "../useFilter";

export function useSidebarKey() {
  const url = URL_PREFIX + "/list";
  const page = usePageValue();
  const sort = useSortValue();
  const filter = useFilterValue();
  // const searchVal = useSearchValue();
  const appCode = useAppCode();
  return [url, appCode, { page, sort, filter }] as const;
}
