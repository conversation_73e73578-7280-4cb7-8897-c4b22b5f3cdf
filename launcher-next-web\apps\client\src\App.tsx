import React from "react"
import { StrictMode } from "react"
// import * as Sen<PERSON> from "@sentry/react"
import { SWRConfig } from "swr"
import { HashRouter, Routes, Route, Navigate } from "react-router-dom"
import { SidebarLayoutProvider } from "./components/SidebarLayout"
import Home from "./pages/Home"
import AllGamesPage from "./pages/AllGamesPage"
import Page404 from "./pages/404"

const App: React.FC = () => {
    return (
        // <Sentry.ErrorBoundary>
        // <StrictMode>
        <SWRConfig
            value={{
                focusThrottleInterval: 1000 * 30,
                refreshInterval: 1000 * 60 * 30,
                // focusThrottleInterval: 1000 * 5,
                // refreshInterval: 1000 * 10,
                fetcher: (resource, init) =>
                    fetch(__SERVER_URL + resource, init).then((res) =>
                        res.json()
                    ),
            }}
        >
            <HashRouter>
                <Routes>
                    <Route element={<SidebarLayoutProvider />}>
                        <Route path="/" element={<Home />} />
                        <Route
                            path=":prefix/all_games"
                            element={<AllGamesPage />}
                        />
                        <Route path="/all_games" element={<AllGamesPage />} />
                        <Route path="/404" element={<Page404 />} />
                        <Route path=":prefix/404" element={<Page404 />} />
                        <Route path="*" element={<Navigate to="/" replace />} />
                    </Route>
                </Routes>
            </HashRouter>
        </SWRConfig>
        // </StrictMode>
        // </Sentry.ErrorBoundary>
    )
}

export default App
