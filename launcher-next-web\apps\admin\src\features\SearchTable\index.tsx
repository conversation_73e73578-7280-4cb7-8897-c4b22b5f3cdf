import { Card, Table, Tabs } from "antd";
import type { TableProps, TabPaneProps, TabsProps } from "antd";
import type { FormInstance } from "antd/es/form";
import type { ColumnType } from "antd/es/table";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";

import { SearchForm } from "@/features/SearchForm";
import type { SearchFormProps } from "@/features/SearchForm";
import { useSWRTable } from "@/hooks/useSWRTable";
import type { DataType, ParamsType } from "@/hooks/useSWRTable";

// 页码约定字段名称
const T_CURRENT = "current"; // 分页参数：当前页
const T_SIZE = "size"; // 分页参数：每页的数量
const T_TOTAL = "total"; // 总数
// const T_TOTALS = "totals"; // 用于统计每个tab页面列表数据的总数
const T_RECORDS = "records"; // 列表数据

export interface SearchTableRefProps {
  /** 筛选表单的实例 */
  form?: FormInstance;
  /** 请求列表数据，不会重置筛选项，也不会设置页码 */
  request: () => void;
  /** 请求列表数据，但会重置筛选项，并设置页码到第一页 */
  freshRequest: (extraParams?: Record<string, any>) => void;
  /** 手动触发筛选表单的查询操作 */
  search: () => void;
}

export interface SearchTableProps {
  /** 标题 */
  title?: string;
  /** 用于 SearchForm 组件的 props */
  searchForm?:
    | Omit<SearchFormProps, "onSearch" | "ref">
    | ((data: DataType) => Omit<SearchFormProps, "onSearch" | "ref">);
  /** 用于 Table 组件的 props */
  tableProps: TableProps<any> | ((params: ParamsType) => TableProps<any>);
  /** 请求列表数据的api服务 */
  service: (params?: any) => Promise<any>;
  /** 重新处理接口返回数据 */
  mapData?: (data: Record<string, any>) => DataType;
  /** 用于列表接口的额外参数 */
  serviceParams?: Record<string, any>;
  /** 分页参数，默认 `{ [T_CURRENT]: 1, [T_SIZE]: 10 }` */
  paginationParams?: { [T_CURRENT]?: number; [T_SIZE]?: number };
  /** 分页参数的字段映射 */
  paginationParamsMapping?: Record<string, string>;
  /** 接口返回数据的字段映射 */
  responseParamsMapping?: Record<string, string>;
  /** 筛选表单的初始值 */
  initialValues?: Record<string, any>;
  /** 操作栏 */
  actionBar?: React.ReactNode;
}

export const SearchTable = forwardRef<SearchTableRefProps, SearchTableProps>(
  (props, ref) => {
    const {
      title,
      searchForm,
      tableProps,
      service,
      serviceParams,
      paginationParams,
      initialValues,
      responseParamsMapping,
      paginationParamsMapping,
    } = props;

    const {
      data,
      params,
      isLoading,
      refresh: request,
      resetParams,
      handlePaginationChange,
    } = useSWRTable({
      service,
      initialParams: paginationParams,
      extraParams: serviceParams,
      responseParamsMapping,
      paginationParamsMapping,
    });

    const formRef = useRef<React.ElementRef<typeof SearchForm>>(null);
    const isFirstRender = useRef(true);

    useEffect(() => {
      if (isFirstRender.current && initialValues && initialValues.length > 0) {
        resetParams(initialValues);
        isFirstRender.current = false;
      }
    }, [params]);

    const resolvedTableProps =
      typeof tableProps === "function" ? tableProps(params) : tableProps;

    const resolvedSearchFormProps =
      typeof searchForm === "function" ? searchForm(data) : searchForm;

    const baseColumns = resolvedTableProps.columns || [];
    const columns = baseColumns.map((col) => {
      if (!col.render) col.render = (v) => v ?? "-";
      return col;
    });

    const handleSearch = useCallback(
      (values: any) => {
        console.log("搜索参数:", values);
        const searchParams = {
          ...values,
          [T_SIZE]: params[T_SIZE], // 使用当前的分页大小，重置其他参数
        };
        resetParams(searchParams);
      },
      [params, resetParams],
    );

    useImperativeHandle(ref, () => ({
      form: formRef.current?.form,
      request,
      freshRequest: (extraParams?: Record<string, any>) => {
        console.log(">>>freshRequest");
        formRef.current?.form.resetFields();
        resetParams({
          [T_CURRENT]: 1,
          [T_SIZE]: params[T_SIZE],
          ...extraParams,
        });
      },
      search: () => formRef.current?.form?.submit(),
    }));

    return (
      <div className="search-table">
        <Card title={title}>
          {resolvedSearchFormProps && (
            <SearchForm
              {...resolvedSearchFormProps}
              ref={formRef}
              onSearch={handleSearch}
              initialValues={initialValues}
            />
          )}
        </Card>
        <br />
        <Card>
          {props.actionBar}
          <Table
            {...resolvedTableProps}
            columns={columns}
            dataSource={data[T_RECORDS] || []}
            loading={isLoading}
            pagination={{
              current: params[T_CURRENT],
              pageSize: params[T_SIZE],
              total: data[T_TOTAL] || 0,
              showTotal: (total) => `共${total}条`,
              onChange: handlePaginationChange,
              ...resolvedTableProps.pagination,
            }}
          />
        </Card>
      </div>
    );
  },
);

SearchTable.displayName = "SearchTable";
