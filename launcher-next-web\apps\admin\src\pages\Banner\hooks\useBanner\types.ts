import { Language } from "@/data";

/**
 * bannerListBannerConfigsReq
 */
export interface ListRequest {
  appcode?: string;
  order_bys?: BannerOrderby[];
  filter?: BannerFilter;
  page_num?: number;
  page_size?: number;
  search_keywords?: BannerSearchKeyword[];
  states?: number[];
}

export interface BannerFilter {
  states?: number[];
  languages?: string[];
}

/**
 * bannerOrderby
 */
export interface BannerOrderby {
  field?: string;
  order?: string;
}

/**
 * bannerSearchKeyword
 */
export interface BannerSearchKeyword {
  field?: string;
  keyword?: string;
}

/**
 * bannerListBannerConfigsRsp
 */
export interface ListResponse {
  banner_configs?: BannerConfig[];
  total_count?: string;
}

/**
 * bannerBannerConfig
 */
export interface BannerConfig {
  appcode?: string;
  create_ts?: string;
  data?: BannerData;
  description?: string;
  end_ts?: string;
  id?: string;
  languages?: string[];
  mversion?: string;
  operator?: string;
  start_ts?: string;
  state?: BannerState;
  timer_kind?: TimerKind;
  update_ts?: string;
}

/**
 * bannerBannerData
 */
export interface BannerData {
  jump_url?: string;
  md5?: string;
  url?: string;
}

export enum BannerState {
  Unpublished = 1,
  Timing,
  Published,
  AutoOffLine,
  ManualOffLine,
}

export enum TimerKind {
  ShortTime = 1,
  LongTime,
}

/**
 * bannerCreateBannerConfigReq
 */
export interface CreateBannerRequest {
  appcode?: string;
  data?: BannerData;
  description?: string;
  end_ts?: string;
  languages?: string[];
  start_ts?: string;
  timer_kind?: number;
}

/**
 * bannerUpdateBannerConfigReq
 */
export interface UpdateBannerRequest {
  appcode?: string;
  data?: BannerData;
  description?: string;
  end_ts?: string;
  id?: string;
  languages?: string[];
  start_ts?: string;
  timer_kind?: number;
}

/**
 * bannerDeleteBannerConfigReq
 */
export interface RemoveBannerRequest {
  appcode?: string;
  id?: string;
}

/**
 * bannerGetBannerConfigOrdersRsp
 */
export interface GetBannerOrdersResponse {
  orders?: BannerConfigOrder[];
}

/**
 * bannerBannerConfigOrder
 */
export interface BannerConfigOrder {
  banner_configs?: BannerConfig[];
  language?: Language;
}

/**
 * bannerPublishBannerConfigReq
 */
export interface PublishRequest {
  appcode?: string;
  orders?: BannerConfigIdOrder[];
}

/**
 * bannerBannerConfigIdOrder
 */
export interface BannerConfigIdOrder {
  language?: string;
  order_ids?: string[];
  publish_ids?: string[];
}

/**
 * bannerExpireBannerConfigReq
 */
export interface ExpireBannerRequest {
  appcode?: string;
  id?: string;
}
