import { PlusOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { EditForm } from "../../../EditForm";
import { useAppCode } from "@/hooks";
import { Language } from "@/data";
import { useCreate } from "@/pages/Announcement/hooks";

export function Create() {
  const create = useCreate();
  const appcode = useAppCode();
  return (
    <EditForm
      title="新建公告"
      trigger={
        <Button icon={<PlusOutlined />} type="primary">
          新建
        </Button>
      }
      onFinish={async (values) => {
        await create({ appcode, language: Language.ZhCN, ...values });
      }}
    />
  );
}
