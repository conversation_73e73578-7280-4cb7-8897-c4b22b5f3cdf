import { fetcher } from "@/utils/fetcher";
import {
  ListHgLauncherAppRequest,
  ListHgLauncherAppReply,
  GetHgLauncherAppRequest,
  GetHgLauncherAppReply,
  SaveHgLauncherAppRequest,
} from "../types";

const URL_PREFIX = "/admin/launcher";

export const requestHgLauncherAppList = async (
  params: ListHgLauncherAppRequest,
) => {
  const url = `${URL_PREFIX}/list_hg_launcher`;
  return fetcher<ListHgLauncherAppReply>(url, params);
};

export const requestHgLauncherApp = async (params: GetHgLauncherAppRequest) => {
  const url = `${URL_PREFIX}/get_hg_launcher`;
  return fetcher<GetHgLauncherAppReply>(url, params, { method: "GET" });
};

export const saveHgLauncherApp = async (params: SaveHgLauncherAppRequest) => {
  const url = `${URL_PREFIX}/save_hg_launcher`;
  return fetcher<{ appcode: string }>(url, params);
};
