import { Flex } from "antd";

interface Props {
  list: Array<{ label: string; value: string }>;
}

export const InfoList = ({ list }: Props) => {
  return (
    <div>
      {list?.map((info, index) => {
        return (
          <Flex
            key={info.label}
            gap={"120px"}
            justify="center"
            align="center"
            style={{
              paddingBottom: index === list.length - 1 ? "0" : "1.5rem",
            }}
          >
            <div className="info-label">{info.label}</div>
            <div className="info-value">{info.value}</div>
          </Flex>
        );
      })}
    </div>
  );
};
