{"name": "client", "version": "0.0.24", "description": "", "main": "index.js", "scripts": {"dev": "hg-cli dev", "build": "pnpm run build:zip && pnpm run publish", "build:zip": "pnpm run build:client && pnpm run gen:zip", "build:client": "hg-cli build", "gen:zip": "node src/scripts/gen-version.js && node src/scripts/zip-build.js", "publish": "cmd-publish-oss && pnpm run publish:platform", "publish:platform": "node src/scripts/publish-platform.js", "build:font": "cmd-build-font"}, "keywords": [], "author": "", "license": "ISC", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "prettier": "@hg/prettier-config", "dependencies": {"@hg/adapt-sdk": "^2.0.1", "@hg/cli": "^1.0.14", "@hg/cmd-build-font": "^0.0.13", "@hg/cmd-publish-oss": "^0.2.0", "@hg/hg-web-sdk": "^2.2.1", "@hg/launcher-share": "workspace:*", "@hg/layouts": "^1.2.4", "@hg/tsconfig": "^1.0.1", "@sentry/react": "^8.19.0", "archiver": "^7.0.1", "axios": "^1.6.5", "classnames": "^2.3.2", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "embla-carousel-autoplay": "^8.1.7", "embla-carousel-react": "^8.1.7", "eventemitter3": "^5.0.1", "framer-motion": "^11.3.8", "jotai": "^2.8.4", "lodash": "^4.17.21", "popmotion": "^11.0.5", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "react-transition-group": "^4.4.5", "swr": "^2.2.5", "tailwind-merge": "^2.4.0"}, "devDependencies": {"@hg/eslint-config": "^1.0.1", "@hg/prettier-config": "^1.0.0", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.7", "@types/node": "^20.14.11", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@types/react-transition-group": "^4.4.11", "autoprefixer": "^10.4.19", "browserslist": "^4.23.2", "core-js": "^3.33.2", "husky": "^9.1.1", "lint-staged": "^15.2.7", "node-fetch": "2", "postcss": "^8.4.39", "release-it": "^17.6.0", "tailwindcss": "^3.4.6", "typescript": "^5.5.3"}}