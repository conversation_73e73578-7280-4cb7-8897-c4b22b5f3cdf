import EventEmitter from "eventemitter3"
import { useEffect } from "react"
import useMemoizedFn from "../hooks/useMemoizedFn"
import {
    GameListResponse,
    SwitchGameNotifyResponse,
    SwitchGameResponse,
    MainBtnStateResponse,
} from "../types/games"
import { UserInfo } from "../types/user"

export enum EventName {
    ChangeLang = "changeLang",
    GameListChange = "downloaded_game_list_notify",
    AllGameListChange = "all_game_list_notify",
    SwitchGameNotify = "switch_game_req_notify", // 请求切换游戏通知（仅在打开启动器后从桌面点击其它游戏时会触发）：
    CurGameNotify = "cur_game_notify", // 当前游戏通知（仅在登录登出的时候会主动推送）
    MainBtnStateNotify = "main_btn_state_notify", // 主按钮状态通知
    ShowMarkWndNotify = "show_mark_wnd_notify", // 打开弹窗蒙层
    // UserLoginNotify = "user_login_notify", // 用户登录通知
    UserInfoNotify = "user_info_notify", // 用户信息通知
    UserLogoutNotify = "user_logout_notify", // 用户登出通知
    LangChangedNotify = "lang_changed_notify", // 语言切换通知
}

const emitter = new EventEmitter()

export const eventBus = {
    on: emitter.on.bind(emitter),
    off: emitter.off.bind(emitter),
    once: emitter.once.bind(emitter),
    emit: emitter.emit.bind(emitter),
}

function useOn<T>(eventName: EventName, fn: (data: T) => void | Promise<void>) {
    const memoFn = useMemoizedFn(fn)
    useEffect(() => {
        emitter.on(eventName, memoFn)
        return () => {
            emitter.off(eventName, memoFn)
        }
    }, [])
}

export function useOnGameListChange(fn: (data: GameListResponse) => void) {
    useOn(EventName.GameListChange, fn)
}

export function useOnAllGameListChange(fn: (data: GameListResponse) => void) {
    useOn(EventName.AllGameListChange, fn)
}

export function useOnSwitchGameNotify(
    fn: (data: SwitchGameNotifyResponse) => void
) {
    useOn(EventName.SwitchGameNotify, fn)
}

export function useOnCurGameNotify(fn: (data: SwitchGameResponse) => void) {
    useOn(EventName.CurGameNotify, fn)
}

export function useOnMainBtnStateNotify(
    fn: (data: MainBtnStateResponse) => void
) {
    useOn(EventName.MainBtnStateNotify, fn)
}

export function useOnShowMarkWndNotify(fn: (data: { show: boolean }) => void) {
    useOn(EventName.ShowMarkWndNotify, fn)
}

export function useOnUserInfoNotify(fn: (data: UserInfo) => void) {
    useOn(EventName.UserInfoNotify, fn)
}

export function useOnUserLogoutNotify(fn: () => void) {
    useOn(EventName.UserLogoutNotify, fn)
}

export function useOnLangChangedNotify(
    fn: (data: { lang: string; main_btn_width: number }) => void
) {
    useOn(EventName.LangChangedNotify, fn)
}
