import { Language } from "@/data";
import { useAppCode } from "@/hooks";
import { EditForm } from "@/pages/Announcement/features";
import { Typography } from "antd";
import { ActionProps } from "../types";
import { useCreate } from "@/pages/Announcement/hooks";

export function Copy({ announcement }: ActionProps) {
  const createBanner = useCreate();
  const appcode = useAppCode();
  return (
    <EditForm
      title="复制公告"
      trigger={<Typography.Link>复制</Typography.Link>}
      initialValues={announcement}
      onFinish={async (values) => {
        await createBanner({ appcode, language: Language.ZhCN, ...values });
      }}
    />
  );
}
