import { useEffect, useState } from "react";
import { Button, Form, Input, Flex, DatePicker, Select } from "antd";
import type { FormProps } from "antd";
import {
  useSearch,
  useDropOptions,
  useSetPageValue,
  useLauncherPackageList,
  PackagePublishStateConfig,
} from "../../hooks";
import { getUtcTs } from "@/utils/time";
import dayjs, { utc } from "dayjs";
const { RangePicker } = DatePicker;

type FieldType = {
  states?: string[];
  versions?: string[];
  publish_time?: [string, string];
};

const publishStateOptions = Object.entries(PackagePublishStateConfig)
  .filter(([key, value]) => {
    return !!value.text;
  })
  .map(([key, value]) => {
    return {
      label: value.text,
      value: key,
    };
  });

export function Search() {
  const [form] = Form.useForm();
  const [searchVal, setSearchVal] = useSearch();
  const setPageValue = useSetPageValue();
  const { versionOptions } = useDropOptions();

  const { isLoading } = useLauncherPackageList();

  // 由于useLauncherPackageList的loading是请求就会触发，需要保存一个组件自身loading仅用于查询时
  const [submitLoading, setSubmitLoading] = useState(false);
  useEffect(() => {
    if (!isLoading) setSubmitLoading(false);
  }, [isLoading]);

  const onFinish: FormProps<FieldType>["onFinish"] = (values) => {
    const [publish_start_time, publish_end_time] =
      values.publish_time?.map((item) => getUtcTs({ time: item })) || [];
    const searchValues = {
      versions: values.versions,
      states: values.states,
      publish_start_time,
      publish_end_time,
    };
    console.log("searchValues:", searchValues);
    setPageValue(1);
    setSearchVal(searchValues);
    setSubmitLoading(true);
  };

  const formStyle: React.CSSProperties = {
    gap: "10px",
  };

  return (
    <Flex justify="space-between" style={{ marginBottom: 10 }}>
      <Form form={form} style={formStyle} layout="inline" onFinish={onFinish}>
        <Form.Item name="versions" label="包体版本号">
          <Select
            style={{ width: 200 }}
            mode="multiple"
            allowClear
            placeholder="选择包体版本号"
            options={versionOptions}
          />
        </Form.Item>
        <Form.Item name="states" label="发布状态">
          <Select
            style={{ width: 150 }}
            mode="multiple"
            allowClear
            placeholder="选择发布状态"
            options={publishStateOptions}
          />
        </Form.Item>
        <Form.Item name="publish_time" label="发布时间">
          <RangePicker showTime allowClear style={{ width: 350 }} />
        </Form.Item>
      </Form>
      <Flex wrap="nowrap">
        <Button
          type="primary"
          loading={submitLoading && isLoading}
          onClick={() => form.submit()}
          style={{ marginRight: "10px" }}
        >
          查询
        </Button>
        <Button
          onClick={() => {
            form.resetFields();
          }}
        >
          重置
        </Button>
      </Flex>
    </Flex>
  );
}
