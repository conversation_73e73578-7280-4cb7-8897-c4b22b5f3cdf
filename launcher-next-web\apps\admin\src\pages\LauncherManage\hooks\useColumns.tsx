import { Tag, TableColumnsType } from "antd";
import { tsToDateTimeRender } from "@/features/renders";
import { HgLauncherBriefConfig, AppEnvType } from "../types";
import { overviewRender, actionRender } from "../features/columnRenders";
import { ReleaseStateConfig, AppEnvTypeMap } from "../constants";

const versionRender = (val: string) => {
  return val ? "v" + val : "-";
};

export function useColumns(
  actionCallback: (record: HgLauncherBriefConfig, action: string) => void,
) {
  const columns: TableColumnsType<HgLauncherBriefConfig> = [
    {
      title: "ID",
      dataIndex: "id",
    },
    {
      title: "标识名",
      dataIndex: "caption",
      render: (val: string, record) => val,
    },
    {
      title: "启动器类型",
      dataIndex: "launcher_env",
      render: (val: AppEnvType, record) => AppEnvTypeMap[val] || "-",
    },
    {
      title: "访问环境",
      render: (val: string, record) => {
        const { text, color } = ReleaseStateConfig[
          record.launcher_release_state
        ] || {
          text: "-",
          color: "default",
        };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: "最新灰度版本",
      dataIndex: "gray_latest_version",
      render: versionRender,
    },
    {
      title: "最新全量版本",
      dataIndex: "latest_version",
      render: versionRender,
    },
    {
      title: "最低可玩版本",
      dataIndex: "force_update_version",
      render: versionRender,
    },
    {
      title: "发布概览",
      dataIndex: "publish_overview",
      render: overviewRender,
    },
    {
      title: "操作",
      render: (val, record) => actionRender(val, record, actionCallback),
    },
  ];
  return columns;
}
