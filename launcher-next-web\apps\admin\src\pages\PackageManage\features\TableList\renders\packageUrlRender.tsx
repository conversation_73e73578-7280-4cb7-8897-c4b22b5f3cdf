import { Space, Popover, Button, Flex, Typography } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import {
  PackagePublishStateConfig,
  PackagePublishState,
  LauncherPackageConfig,
} from "@/pages/PackageManage/hooks/useLauncherPackageList/types";

export function packageUrlRender(value: any, record: LauncherPackageConfig) {
  const { zip_package_url, gray_zip_package_url, exe_url, gray_exe_url } =
    record;

  const displayGray = gray_zip_package_url || gray_exe_url;
  const displayFull = zip_package_url || exe_url;
  const grayUrlList = [
    {
      label: "压缩包",
      value: gray_zip_package_url,
    },
    {
      label: "安装包",
      value: gray_exe_url,
    },
  ];
  const fullUrlList = [
    {
      label: "压缩包",
      value: zip_package_url,
    },
    {
      label: "安装包",
      value: exe_url,
    },
  ];

  return (
    <Flex gap={10} justify="center">
      {displayGray && (
        <Popover
          placement="top"
          overlayStyle={{ maxWidth: "600px" }}
          content={
            <UrlList
              list={grayUrlList}
              desc="灰版包体可下载灰度发布状态下的游戏"
            ></UrlList>
          }
        >
          <Button icon={<DownloadOutlined />}>灰版下载</Button>
        </Popover>
      )}
      {displayFull && (
        <Popover
          placement="top"
          overlayStyle={{ maxWidth: "600px" }}
          content={
            <UrlList
              list={fullUrlList}
              desc="全版包体仅可下载全量发布状态下的游戏"
            ></UrlList>
          }
        >
          <Button type="primary" icon={<DownloadOutlined />}>
            全版下载
          </Button>
        </Popover>
      )}
    </Flex>
  );
}

const UrlList = ({
  list,
  desc,
}: {
  list: { label: string; value?: string }[];
  desc: string;
}) => {
  return (
    <Space size={10} direction="vertical">
      <div
        style={{
          fontSize: "12px",
          color: "rgba(0, 0, 0, .45)",
        }}
      >
        {desc}
      </div>
      {list.map((item) => {
        if (!item.value) return null;
        return (
          <Flex gap={10} key={item.label}>
            <span style={{ whiteSpace: "nowrap" }}>{item.label}: </span>
            <Typography.Link copyable href={item.value} target="_blank">
              {item.value}
            </Typography.Link>
          </Flex>
        );
      })}
    </Space>
  );
};
