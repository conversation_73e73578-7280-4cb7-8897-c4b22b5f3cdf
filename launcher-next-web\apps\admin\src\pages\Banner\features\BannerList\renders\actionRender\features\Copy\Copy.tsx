import { Language } from "@/data";
import { useAppCode } from "@/hooks";
import { EditForm } from "@/pages/Banner/features/EditForm";
import { useCreateBanner } from "@/pages/Banner/hooks";
import { Typography } from "antd";
import { BannerActionProps } from "../types";

export function Copy({ bannerConfig }: BannerActionProps) {
  const createBanner = useCreateBanner();
  const appcode = useAppCode();
  return (
    <EditForm
      title="复制 Banner"
      trigger={<Typography.Link>复制</Typography.Link>}
      initialValues={bannerConfig}
      onFinish={async (values) => {
        await createBanner({ appcode, languages: [Language.ZhCN], ...values });
      }}
    />
  );
}
