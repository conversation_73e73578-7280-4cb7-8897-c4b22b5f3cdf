import { Popconfirm, Typography, Modal, message } from "antd";
import { BannerActionProps } from "../types";
import { useRemoveTab } from "@/pages/AnnounceTab/hooks";

export function Remove({ tab }: BannerActionProps) {
  const removeBanner = useRemoveTab();
  const handleRemoveBanner = async () => {
    try {
      await removeBanner({ id: tab.id, appcode: tab.appcode });
      message.success("删除成功");
    } catch (e: any) {
      // console.log("handleRemoveBanner e", e);
      if (
        e?.reason ===
        "LAUNCHER_MANAGER_CONFIG_ANNOUNCEMENT_NOT_DELETE_TAB_WHEN_HAS_ANNOUNCEMENT"
      ) {
        Modal.error({
          title: "删除失败",
          content: (
            <div>
              当前公告列表中存在本页签下 未发布 | 已发布
              的公告，请先手动下线相关公告，再删除本页签。
              <p style={{ color: "gray", fontSize: "12px" }}>
                只有在完成手动下线所有已发布公告后，方可成功删除该页签。
              </p>
            </div>
          ),
        });
      }
    }
  };
  return (
    <Popconfirm
      title="是否确认删除此公告页签 ？"
      onConfirm={() => handleRemoveBanner()}
    >
      <Typography.Link type="danger">删除</Typography.Link>
    </Popconfirm>
  );
}
