import { Select, Space } from "antd";
import { Status } from "./Status";
import {
  useCurrentLang,
  useEmitChangeLang,
  useIsEditing,
  useMainData,
  MainPageState,
} from "../../hooks";
import { useLangList } from "@/hooks";
import { useEffect, useMemo, useRef } from "react";

export function Header() {
  const pageInitRef = useRef(true);
  const langList = useLangList();
  const { configList } = useMainData();
  const [currentLang, setCurrentLang] = useCurrentLang();
  const isEditing = useIsEditing();
  const emitChangeLang = useEmitChangeLang();
  // console.log(configList);

  useEffect(() => {
    if (langList?.length && pageInitRef.current) {
      setCurrentLang(langList[0].value);
      pageInitRef.current = false;
    }
  }, [langList]);
  return (
    <div>
      <Space align="center">
        <span>主界面设置</span>
        <Select
          disabled={isEditing}
          size="small"
          style={{ width: 100 }}
          options={langList}
          value={currentLang}
          onChange={(lang) => {
            emitChangeLang(lang);
            setCurrentLang(lang);
          }}
        />
        <Status />
      </Space>
    </div>
  );
}
