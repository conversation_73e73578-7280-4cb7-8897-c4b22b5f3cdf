import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useBannerKey } from "../../useBannerKey";
import { PublishRequest } from "../../types";
import { useSWRConfig } from "swr";
import { useAppCode } from "@/hooks";

export function usePublishBanner() {
  const key = useBannerKey();
  const appCode = useAppCode();
  const { mutate } = useSWRConfig();
  const { trigger } = useSWRMutation(key, publishBanner, {
    onSuccess() {
      mutate([URL_PREFIX + "/get_orders", appCode]);
    },
  });
  return trigger;
}

async function publishBanner(_: any, { arg }: { arg: PublishRequest }) {
  await fetcher(`${URL_PREFIX}/publish`, arg);
}
