{
  "extends": "../../tsconfig.json", // Extend the config options from the root,
  "compilerOptions": {
    "target": "es2016" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,

    "jsx": "react-jsx" /* Specify what JSX code is generated. */,

    "resolveJsonModule": true /* Enable importing .json files. */,

    "outDir": "./dist" /* Specify an output folder for all emitted files. */,
    "removeComments": true /* Disable emitting comments. */,

    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,

    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,

    "strict": true /* Enable all strict type-checking options. */,
    "strictPropertyInitialization": false,

    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "module": "ESNext",
    "moduleResolution": "node",
    "sourceMap": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
    }
  },
  "include": ["./**/*.ts", "./**/*.tsx", "../common/**/*.ts"],
  "exclude": ["node_modules"]
}
