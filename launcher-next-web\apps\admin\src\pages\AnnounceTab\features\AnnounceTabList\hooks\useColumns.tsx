import { tsToDateTimeRender } from "@/features/renders";
import { BannerConfig, BannerState } from "@/pages/Banner/hooks/useBanner";
import { TableColumnsType } from "antd";
import { actionRender, languageRender } from "../renders";
import { useLangFiltersList } from "@/hooks/useLangList";
import { Language } from "@/data";

export function useColumns() {
  const columns: TableColumnsType<BannerConfig> = [
    { dataIndex: "id", title: "ID" },
    { dataIndex: "name", title: "名称", ellipsis: true },
    {
      dataIndex: "language",
      title: "地区",
      render: languageRender,
      filters: useLangFiltersList(),
      filterMultiple: false,
    },
    {
      dataIndex: "update_ts",
      title: "更新时间",
      render: tsToDateTimeRender,
    },
    { key: "action", title: "操作", render: actionRender },
  ];
  return columns;
}
