import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M18.563 6.75a4.688 4.688 0 0 1 4.687 4.688v7.124a4.688 4.688 0 0 1-4.688 4.688h-7.125a4.688 4.688 0 0 1-4.687-4.688v-7.125a4.688 4.688 0 0 1 4.688-4.687h7.124Zm-.333 6.076a1.016 1.016 0 0 0-1.385-.154c-.093.067-.089.126.025.169a.439.439 0 0 1 .282.329c.044.181.064.368.06.555l.005 2.132v2.28c0 .242.042.282.289.287h.666c.222 0 .268-.043.268-.276v-1.294c0-.066-.012-.141.057-.18.07-.038.125.022.176.06.24.184.546.26.844.208a1.604 1.604 0 0 0 1.332-1.109c.22-.604.22-1.267 0-1.871a1.632 1.632 0 0 0-1.274-1.113c-.34-.085-.7.012-.952.255-.15.143-.204.128-.265-.062a.61.61 0 0 0-.128-.216Zm-2.753.06a.162.162 0 0 0-.164.094c-.034.085-.091.092-.17.077a2.97 2.97 0 0 0-.784-.034 1.51 1.51 0 0 0-1.399.944 2.57 2.57 0 0 0-.087 1.886c.118.442.436.804.858.98.382.185.833.15 1.18-.094.102-.085.193-.183.27-.292.07-.086.13-.079.186.016.02.034.035.072.057.106.128.274.41.443.71.427a1.45 1.45 0 0 0 .695-.212c.1-.056.097-.113-.01-.17a.534.534 0 0 1-.271-.39 1.502 1.502 0 0 1-.028-.318 269.7 269.7 0 0 1-.004-1.334v-1.466c0-.159-.05-.218-.186-.22-.282-.004-.57 0-.853 0Zm-6.14-1.18c-.025-.098-.087-.107-.15-.027a.894.894 0 0 0-.17.372.572.572 0 0 0 .565.749c.07.005.142.004.213.004h.76c.165 0 .225.064.23.225v3.599c0 .307.025.331.339.331h.626c.225 0 .261-.038.262-.268.003-.594.003-1.189.003-1.784v-1.786c0-.266.051-.317.32-.317h.894c.164 0 .213-.052.217-.217.003-.147 0-.294 0-.44 0-.24-.036-.276-.281-.276H9.74c-.066 0-.134 0-.2-.007a.2.2 0 0 1-.202-.159Zm4.652 2.476a.997.997 0 0 1 1.066-.42c.124.02.159.113.169.225.017.246-.031.492.003.686v.717a.516.516 0 0 1-.346.514.662.662 0 0 1-.93-.33 1.421 1.421 0 0 1 .038-1.392Zm4.76-.254a.75.75 0 0 1 .975.222c.267.417.277.948.027 1.375a.839.839 0 0 1-.907.369.36.36 0 0 1-.306-.388c-.002-.205 0-.41 0-.615v-.616a.374.374 0 0 1 .211-.347Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={31.5}
        height={31.5}
        x={-0.75}
        y={1.125}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_52_977" />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_52_977"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
