import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { DATA_KEY, URL_PREFIX } from "../const";
import { CancelPublishRequest } from "../types";
import { useAppCode } from "@/hooks";

export function useCancelPublishMainData() {
  const appCode = useAppCode();
  const { trigger } = useSWRMutation(
    [DATA_KEY, appCode],
    cancelPublishMainData,
  );
  return trigger;
}

async function cancelPublishMainData(
  _: [string, string | undefined],
  { arg }: { arg: CancelPublishRequest },
) {
  await fetcher(`${URL_PREFIX}/cancel_publish`, arg);
}
