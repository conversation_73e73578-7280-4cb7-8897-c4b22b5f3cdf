import { Carousel } from "@/features";
import {
  AnnouncementConfig,
  AnnouncementState,
} from "@/pages/Announcement/hooks";
import { useAnnounceTab } from "@/pages/AnnounceTab/hooks";
import { BannerConfig, BannerState } from "@/pages/Banner/hooks";
import { Checkbox, Space, Tabs, Typography } from "antd";
import { useState } from "react";

interface Props {
  announcement: Record<string, AnnouncementConfig[]>;
  activeKey: string;
  language: string;
  onChange: (v: string) => void;
}

export function Preview({
  announcement,
  activeKey,
  language,
  onChange,
}: Props) {
  const { tabs } = useAnnounceTab(language);
  console.log("Preview", announcement);
  const [showTimingBanners, setTimingBanners] = useState(true);
  return (
    <Space direction="vertical">
      <Typography.Text>效果预览：</Typography.Text>
      <div
        style={{
          marginTop: 10,
          position: "relative",
          overflow: "hidden",
          width: 560,
          height: 316,
        }}
      >
        <Tabs
          activeKey={activeKey}
          onChange={onChange}
          items={tabs?.map((tab) => ({
            key: tab.id!,
            label: tab.name,
            children: (
              <div>
                {announcement[tab.id!]
                  ?.filter((anno) =>
                    showTimingBanners
                      ? true
                      : anno.state === AnnouncementState.Published,
                  )
                  .map((anno) => (
                    <Typography.Paragraph key={anno.id}>
                      {anno.data?.content}
                    </Typography.Paragraph>
                  ))}
              </div>
            ),
          }))}
        />
      </div>
      <div>
        <Checkbox
          checked={showTimingBanners}
          onChange={(e) => setTimingBanners(e.target.checked)}
        >
          显示定时中内容
        </Checkbox>
      </div>
    </Space>
  );
}
