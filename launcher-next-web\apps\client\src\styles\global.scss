// @import "./font.scss";

:global {
    html {
        overflow: hidden;
        // touch-action: pan-x pan-y;
        -webkit-tap-highlight-color: transparent;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-size-adjust: none;
        user-select: none;
    }

    #root {
        user-select: none;
        // font-family: $default;
    }
}

img {
    -webkit-user-drag: none;
}

.clamp-text-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
}

.scrollbar-hidden::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

::-webkit-scrollbar {
    width: 8px;
    height: 4px;
}

/* 滚动条轨道（背景） */
::-webkit-scrollbar-track {
    background: transparent;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.25);
    border-radius: 4px;
}

/* 鼠标悬浮在滚动条上时 */
::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.active-fade {
    animation: activeFade 0.4s ease-in-out forwards;
}

.prev-fade {
    animation: prevFade 0.4s ease-in-out forwards;
}

@keyframes activeFade {
    0% {
        opacity: 0;
        // scale: 1;
    }
    100% {
        opacity: 1;
        // scale: 1.02;
    }
}

@keyframes prevFade {
    0% {
        opacity: 1;
        // scale: 1.02;
    }
    100% {
        opacity: 0.8;
        // scale: 1.02;
    }
}
