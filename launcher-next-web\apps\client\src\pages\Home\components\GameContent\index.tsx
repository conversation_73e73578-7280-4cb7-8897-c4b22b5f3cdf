import clsx from "clsx"
import React, { useRef, useState, useEffect } from "react"
import {
    CSSTransition,
    SwitchTransition,
    TransitionGroup,
} from "react-transition-group"
// import { motion, AnimatePresence } from "framer-motion"
import { useLanguageValue } from "@/store/useLanguage"
import { Game } from "@/types/games"
import {
    GetAnnouncementRsp,
    GetBannerRsp,
    GetSingleEntRsp,
    GetHgSidebarsRsp,
} from "@/types/mainData"
import bridge from "@/utils/bridge"

import { Announcement } from "../Announcement"
import { Banner } from "../Banner"
import { MainButton } from "../MainButton"
import { SidebarList } from "../SidebarList"
import { SingleEnt } from "../SingleEnt"
import { GameProgress } from "../GameProgress"

import "./index.scss"

import { eventBus } from "@/utils/eventBus"
import { MockDownloadBtn } from "./mockDownloadBtn"
import { MockGameInfo } from "./MockGameInfo"

interface ContentData {
    get_announcement_rsp?: GetAnnouncementRsp
    get_banner_rsp?: GetBannerRsp
    get_single_ent_rsp?: GetSingleEntRsp
    get_hg_sidebar_rsp?: GetHgSidebarsRsp
}

interface GameContentProps {
    game: Game | null
    contentData: ContentData
}

export const GameContent: React.FC<GameContentProps> = ({
    game,
    contentData,
}: GameContentProps) => {
    // const [curContentData, setCurContentData] = useState<ContentData | null>(
    //     null
    // )
    // const [curGame, setCurGame] = useState<Game | null>(null)
    const { main_btn_width } = useLanguageValue()
    const { game_state, download_state, theme } = game || {}

    const nodeRef = useRef<HTMLDivElement>(null)
    const [isAnimating, setIsAnimating] = useState(false)
    const isAnimatingRef = useRef(false)
    const isFirstRender = useRef(true)
    const displayGameProgress = !!game?.download_state?.progress

    const updateTheme = () => {
        const root = document.documentElement
        !!game?.theme && root.setAttribute("class", `${game?.theme}`)
    }

    // 游戏侧+启动器侧-游戏主页曝光埋点
    const handlePageViewEvent = () => {
        bridge.eventLog("game", "page_view", {
            game_uuid: game?.uuid,
        })
        bridge.eventLog("launcher", "launcher_page_exposure", {
            type: "game_mainpage",
            appcode: game?.app_code,
        })
    }

    useEffect(() => {
        console.log(">>>game uuid changed", game?.uuid)
        if (!game?.uuid) return
        handlePageViewEvent()
        if (isFirstRender.current) {
            isFirstRender.current = false
            return
        }
        setIsAnimating(true)
        isAnimatingRef.current = true
    }, [game?.uuid])

    useEffect(() => {
        if (isAnimatingRef.current) {
            return
        }
        // 动画结束后更新主题
        // SwitchTransition mode="out-in" 会自动保留上一个组件的状态，所以这里不需要对其它state做处理
        updateTheme()
    }, [isAnimating])

    return (
        <SwitchTransition mode="out-in">
            <CSSTransition
                key={game?.uuid || ""}
                nodeRef={nodeRef}
                appear={true}
                timeout={300}
                classNames={"content-container"}
                onExited={() => {
                    setIsAnimating(false)
                    isAnimatingRef.current = false
                }}
                unmountOnExit
            >
                <div
                    ref={nodeRef}
                    className={clsx(
                        "w-full h-full flex flex-col justify-end items-start relative pb-10"
                    )}
                >
                    {window.isDebug && <MockGameInfo game={game} />}
                    {/* <MockDownloadBtn /> */}
                    <div
                        className={clsx(
                            "single-ent-container",
                            "absolute top-[188px] left-0"
                        )}
                    >
                        <SingleEnt
                            data={contentData?.get_single_ent_rsp?.single_ent}
                            appcode={game?.app_code}
                        ></SingleEnt>
                    </div>

                    <div
                        className={clsx(
                            "right-sidebar-container",
                            "absolute top-[50%] right-0 translate-y-[-50%] z-10"
                        )}
                    >
                        <SidebarList
                            data={contentData?.get_hg_sidebar_rsp?.sidebars}
                            appcode={game?.app_code}
                        ></SidebarList>
                    </div>

                    <div className="footer-container w-[calc(100%-5rem)] flex justify-between items-end mx-10 relative">
                        <div
                            // ref={contentRef}
                            key={game?.uuid}
                            className={clsx(
                                "relative h-[9.25rem] rounded-[16px]",
                                "flex items-center overflow-hidden"
                            )}
                            style={{
                                boxShadow:
                                    "0px 2px 8px 0px rgba(0, 0, 0, 0.65)",
                                transform: displayGameProgress
                                    ? "translateY(-72px)"
                                    : "translateY(0)",
                                transition: "transform 300ms ease-in-out",
                            }}
                        >
                            <Banner
                                data={contentData?.get_banner_rsp?.banners}
                                appcode={game?.app_code}
                            />
                            <Announcement
                                data={contentData?.get_announcement_rsp?.tabs}
                                appcode={game?.app_code}
                                fullRadius={
                                    !contentData?.get_banner_rsp?.banners
                                        ?.length
                                }
                            />
                            <div className="absolute inset-0 border-[0.5px] rounded-[16px] border-white/10 pointer-events-none"></div>
                        </div>

                        <div
                            className="h-[3.75rem]"
                            style={{
                                width: `${main_btn_width || 218}px`,
                            }}
                        >
                            <MainButton
                                game={game}
                                btnWidth={main_btn_width || 218}
                            />
                        </div>

                        <div
                            className="absolute left-[0] right-[188px] bottom-0 z-[-1]"
                            style={{
                                right: `${(main_btn_width || 218) - 218 + 188}px`,
                            }}
                        >
                            <GameProgress
                                gameState={game_state as any}
                                downloadState={download_state as any}
                                displayGameProgress={displayGameProgress}
                            />
                        </div>
                    </div>
                </div>
            </CSSTransition>
        </SwitchTransition>
    )
}
