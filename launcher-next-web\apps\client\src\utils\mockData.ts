const downloaded_game_list = [
    {
        game_name: "rm42",
        app_code: "AmJV7R8NdkGB4dbe",
        channel: 1,
        sub_channel: 1,
        region: "cn",
        uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
        is_selected: false, //是否选中
        is_user_visible: true, //当前用户是否可见
        is_stop_service: false, //是否下架
        is_game: true,
        theme: "theme_2", //游戏主题
        game_state: "pause", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
        download_state: {
            step: "download", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy, 游戏完整性检查:game_complete_check
            progress: {
                from: 0,
                to: 5000000,
                value: 2000999,
                speed: 10000, // （单位字节）
                remaining_time: 1000, // （单位秒）
            },
        },
    },
    {
        game_name: "ic02",
        app_code: "P1xe2sR8DSSnv8ns",
        channel: 1,
        sub_channel: 1,
        region: "cn",
        uuid: "87ca46c7aa024fafb39ffa2f95179f0b",
        is_selected: false, //是否选中
        is_user_visible: true, //当前用户是否可见
        is_stop_service: false, //是否下架
        is_game: true,
        theme: "theme_1", //游戏主题
        game_state: "installed", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
    },
    {
        game_name: "di11",
        app_code: "Vp2jukCCSUGztqli",
        channel: 1,
        sub_channel: 1,
        region: "cn",
        uuid: "di11_uuid",
        is_selected: false, //是否选中
        is_user_visible: true, //当前用户是否可见
        is_stop_service: false, //是否下架
        is_game: true,
        theme: "theme_3", //游戏主题
        game_state: "installed", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
    },
    // {
    //     game_name: "dm01",
    //     app_code: "q7JlfqIOBF0htOGu",
    //     channel: 1,
    //     sub_channel: 1,
    //     region: "cn",
    //     uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
    //     is_selected: true, //是否选中
    //     is_user_visible: true, //当前用户是否可见
    //     is_stop_service: false, //是否下架
    //     theme: "theme_4", //游戏主题
    //     game_state: "download", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
    //     download_state: {
    //         step: "download", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy
    //         progress: {
    //             from: 0,
    //             to: 100,
    //             value: 60,
    //             speed: 100000, // （单位字节）
    //             remaining_time: 1000, // （单位秒）
    //         },
    //     },
    // },
]

export const downloaded_game_list_rsp = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: downloaded_game_list,
}

export const all_game_list_rsp = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        ...downloaded_game_list,
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            is_game: true,
            theme: "theme_4", //游戏主题
            game_state: "uninstalled", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
        },
    ],
}

export const logout_all_game_list_rsp = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        // {
        //     game_name: "ic02",
        //     app_code: "P1xe2sR8DSSnv8ns",
        //     channel: 1,
        //     sub_channel: 1,
        //     region: "cn",
        //     uuid: "87ca46c7aa024fafb39ffa2f95179f0b",
        //     is_selected: false, //是否选中
        //     is_user_visible: true, //当前用户是否可见
        //     is_stop_service: false, //是否下架
        //     is_game: true,
        //     theme: "theme_1", //游戏主题
        //     game_state: "installed", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
        // },
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            is_game: true,
            theme: "theme_4", //游戏主题
            game_state: "uninstalled", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
        },
    ],
}

export const downloaded_game_list_notify = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        ...downloaded_game_list,
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            theme: "theme_4", //游戏主题
            game_state: "download", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
            download_state: {
                step: "download", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy
                progress: {
                    from: 0,
                    to: 100,
                    value: 30,
                    speed: 100000, // （单位字节）
                    remaining_time: 1000, // （单位秒）
                },
            },
        },
    ],
}

export const downloaded_game_list_notify1 = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        {
            game_name: "rm42",
            app_code: "AmJV7R8NdkGB4dbe",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517rm42",
            is_selected: false, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            is_game: true,
            theme: "theme_2", //游戏主题
            game_state: "download", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
            download_state: {
                step: "download", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy
                progress: {
                    from: 0,
                    to: 5000000,
                    value: 3000999,
                    speed: 10000, // （单位字节）
                    remaining_time: 1000, // （单位秒）
                },
            },
        },
        ...downloaded_game_list.slice(1),
    ],
}

export const downloaded_game_list_notify2 = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        ...downloaded_game_list,
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            theme: "theme_4", //游戏主题
            game_state: "download", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
            download_state: {
                step: "download", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy
                progress: {
                    from: 0,
                    to: 100,
                    value: 60,
                    speed: 100000, // （单位字节）
                    remaining_time: 1000, // （单位秒）
                },
            },
        },
    ],
}

export const downloaded_game_list_notify3 = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        ...downloaded_game_list,
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            theme: "theme_4", //游戏主题
            game_state: "pause", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
            download_state: {
                step: "download", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy
                progress: {
                    from: 0,
                    to: 100,
                    value: 60,
                    speed: 100000, // （单位字节）
                    remaining_time: 1000, // （单位秒）
                },
            },
        },
    ],
}

// 校验中
export const downloaded_game_list_notify4 = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        ...downloaded_game_list,
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            theme: "theme_4", //游戏主题
            game_state: "download", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
            download_state: {
                step: "check", //游戏下载进行到哪个阶段 可选值：下载:download, 校验:check, 解压:decompress, 拷贝:copy
                progress: {
                    from: 0,
                    to: 100,
                    value: 10,
                    speed: 100000, // （单位字节）
                    remaining_time: 1000, // （单位秒）
                },
            },
        },
    ],
}

export const downloaded_game_list_notify5 = {
    cmd: "downloaded_game_list_rsp", //已下载游戏列表
    is_single_game: false, //是否为单游戏启动器
    items: [
        ...downloaded_game_list,
        {
            game_name: "dm01",
            app_code: "q7JlfqIOBF0htOGu",
            channel: 1,
            sub_channel: 1,
            region: "cn",
            uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
            is_selected: true, //是否选中
            is_user_visible: true, //当前用户是否可见
            is_stop_service: false, //是否下架
            theme: "theme_4", //游戏主题
            game_state: "installed", //游戏状态  可选值：下载中:download,  排队中:queue,  已安装:installed, 未安装:uninstalled，暂停中:pause
        },
    ],
}

export const main_btn_state_notify = {
    cmd: "main_btn_state_notify",
    uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
    state: "downloading",
    text_normal: "正在下载",
    text_hover: "暂停下载",
    tooltip: "下载中",
}

export const main_btn_state_notify2 = {
    cmd: "main_btn_state_notify",
    uuid: "87ca46c7aa024fafb39ffa2f9517dm01",
    state: "checking_download_zip",
    text_normal: "校验中",
    text_hover: "校验中",
    tooltip: "",
}
