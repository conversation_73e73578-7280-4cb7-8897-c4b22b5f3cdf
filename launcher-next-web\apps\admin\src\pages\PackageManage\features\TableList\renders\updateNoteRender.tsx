import { Tabs } from "antd";
import { curEnvLabelList } from "@/hooks/useLangList";

export function updateNoteRender(record: Record<string, string>) {
  const items = curEnvLabelList.map((item) => {
    return {
      label: item.label,
      key: item.value,
      children: (
        <span style={{ whiteSpace: "pre-line" }}>
          {record[item.value] || "-"}
        </span>
      ),
    };
  });

  return <Tabs type="card" items={items} more={{ icon: null }} />;
}
