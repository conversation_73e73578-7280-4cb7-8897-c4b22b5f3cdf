import React, { useEffect, useRef, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import clsx from "clsx"
import { twMerge } from "tailwind-merge"

interface DropdownProps {
    trigger: JSX.Element
    open?: boolean
    children: React.ReactNode
    placement?: string
    offset?: number
    className?: string
    contentClassName?: string
    onOpenChange?: (open: boolean) => void
}

const DropdownContext = React.createContext<{
    close: () => void
}>({ close: () => {} })

const getPositionStyle = (placement: string, offset: number) => {
    switch (placement) {
        case "top":
            return { bottom: `calc(100% + ${offset}px)`, left: 0 }
        case "top-center":
            return {
                bottom: `calc(100% + ${offset}px)`,
                left: "50%",
                transform: "translateX(-50%)",
            }
        case "left":
            return { right: `calc(100% + ${offset}px)`, top: 0 }
        case "right":
            return { left: `calc(100% + ${offset}px)`, top: 0 }
        case "bottom":
            return { top: `calc(100% + ${offset}px)`, left: 0 }
        case "bottom-center":
            return {
                top: `calc(100% + ${offset}px)`,
                left: "50%",
                transform: "translateX(-50%)",
            }
        default:
            return { top: `calc(100% + ${offset}px)`, left: 0 }
    }
}

export const Dropdown: React.FC<DropdownProps> = ({
    trigger,
    open: openProp,
    children,
    placement = "bottom",
    offset = 4,
    className,
    contentClassName,
    onOpenChange,
}) => {
    const [open, setOpen] = useState(false)
    const ref = useRef<HTMLDivElement>(null)

    const handleToggleOpen = (value: boolean) => {
        if (openProp === undefined) {
            setOpen(value)
        }
        onOpenChange?.(value)
    }

    useEffect(() => {
        if (openProp !== undefined) {
            setOpen(openProp)
        }
    }, [openProp])

    useEffect(() => {
        const handleOutsideClick = (event: MouseEvent) => {
            if (ref.current && !ref.current.contains(event.target as Node)) {
                handleToggleOpen(false)
            }
        }
        document.addEventListener("mousedown", handleOutsideClick)
        return () => {
            document.removeEventListener("mousedown", handleOutsideClick)
        }
    }, [])

    return (
        <div
            className={twMerge(clsx("relative inline-block", className))}
            ref={ref}
        >
            {React.cloneElement(trigger, {
                onClick: (e: React.MouseEvent) => {
                    handleToggleOpen(!open)
                    trigger.props.onClick?.(e)
                },
            })}
            <DropdownContext.Provider
                value={{ close: () => handleToggleOpen(false) }}
            >
                <AnimatePresence>
                    {open && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className={twMerge(
                                clsx(
                                    "absolute z-40 min-w-[120px] p-2 rounded-[8px] bg-[rgb(24,23,26)]/[0.9] border-[0.5px] border-white/[0.1]",
                                    contentClassName
                                )
                            )}
                            style={{
                                backdropFilter: "blur(4px)",
                                boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.65)",
                                ...getPositionStyle(placement, offset),
                            }}
                        >
                            {children}
                        </motion.div>
                    )}
                </AnimatePresence>
            </DropdownContext.Provider>
        </div>
    )
}

export const DropdownItem: React.FC<{
    children: React.ReactNode
    disabled?: boolean
    className?: string
    closeOnClick?: boolean
    onClick?: () => void
}> = ({ children, disabled, className, closeOnClick = true, onClick }) => {
    const { close } = React.useContext(DropdownContext)
    const handleClick = () => {
        if (disabled) return
        onClick?.()
        if (closeOnClick) {
            close()
        }
    }
    return (
        <div
            className={twMerge(
                clsx(
                    "leading-[18px] hover:bg-white/15 rounded-[4px] px-2 py-3 text-[14px] font-sans",
                    disabled
                        ? "text-white/35 cursor-not-allowed hover:bg-transparent"
                        : "text-white cursor-pointer",
                    className
                )
            )}
            onClick={handleClick}
        >
            {children}
        </div>
    )
}

export const DropdownSeparator: React.FC = () => {
    return <div className="h-[1px] bg-white/10 -mx-2 my-2"></div>
}
