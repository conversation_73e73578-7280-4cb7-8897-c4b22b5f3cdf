import { PlusOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { EditForm } from "../../../EditForm";
import { useAppCode } from "@/hooks";
import { Language } from "@/data";
import { useCreateTab } from "@/pages/AnnounceTab/hooks";

export function Create() {
  const createBanner = useCreateTab();
  const appcode = useAppCode();
  return (
    <EditForm
      title="新建公告页签"
      trigger={
        <Button icon={<PlusOutlined />} type="primary">
          新建
        </Button>
      }
      onFinish={async (values) => {
        await createBanner({ appcode, language: Language.ZhCN, ...values });
      }}
    />
  );
}
