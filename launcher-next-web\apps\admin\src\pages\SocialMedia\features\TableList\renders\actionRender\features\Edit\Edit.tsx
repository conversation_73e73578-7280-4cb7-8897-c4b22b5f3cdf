import { EditForm } from "@/pages/SocialMedia/features/EditForm";
import { useUpdateSidebar, SidebarState } from "@/pages/SocialMedia/hooks";
import { Typography } from "antd";
import { SidebarActionProps } from "../types";

export function Edit({ sidebarConfig }: SidebarActionProps) {
  const updateSidebar = useUpdateSidebar();
  return (
    <EditForm
      title="编辑 Sidebar"
      trigger={<Typography.Link>编辑</Typography.Link>}
      initialValues={sidebarConfig}
      disableModify={sidebarConfig.state !== SidebarState.Unpublished}
      onFinish={async (value) => {
        return updateSidebar({ ...sidebarConfig, ...value });
      }}
    />
  );
}
