import useSWR from "swr";
import { useState, useCallback } from "react";
import { message } from "antd";

// 约定的字段名称
const T_CURRENT = "current"; // 分页参数：当前页
const T_SIZE = "size"; // 分页参数：每页的数量
const T_TOTAL = "total"; // 总数
const T_TOTALS = "totals"; // 用于统计每个tab页面列表数据的总数
const T_RECORDS = "records"; // 列表数据标识

// 响应
export interface DataType {
  [T_TOTAL]: number;
  [T_RECORDS]: any[];
  [T_TOTALS]?: number[];
  [key: string]: any;
}

// 入参
export interface ParamsType {
  [T_CURRENT]: number;
  [T_SIZE]: number;
  [key: string]: any;
}

interface UseSWRTableOptions {
  /** 请求列表数据的api服务 */
  service: (params?: any) => Promise<any>;
  /** 初始参数 */
  initialParams?: Partial<ParamsType>;
  /** 额外参数（表格以外的一些参数，比如顶部tab切换） */
  extraParams?: Record<string, any>;
  /** 数据转换函数，用于将API响应数据转换为标准格式*/
  mapData?: (responseData: any) => DataType;
  /** 分页参数的字段映射 */
  paginationParamsMapping?: Record<string, string>;
  /** 接口返回数据的字段映射 */
  responseParamsMapping?: Record<string, string>;
}

export const useSWRTable = (options: UseSWRTableOptions) => {
  const {
    service,
    initialParams = {},
    extraParams = {},
    mapData,
    paginationParamsMapping,
    responseParamsMapping,
  } = options;

  const [params, setParams] = useState<ParamsType>({
    [T_CURRENT]: 1,
    [T_SIZE]: 10,
    ...initialParams,
  });

  const swrKey = [params, extraParams];
  const {
    data: swrData,
    error,
    isLoading,
    isValidating,
    mutate,
  } = useSWR(
    swrKey,
    async ([params, extraParams]) => {
      try {
        const reqParams: Record<string, any> = { ...params, ...extraParams };
        console.log(">>>reqParams1:", reqParams);
        if (paginationParamsMapping) {
          // 如果提供了paginationParamsMapping，转换数据
          Object.entries(paginationParamsMapping).forEach(([key, apiKey]) => {
            if (key === T_CURRENT || key === T_SIZE) {
              reqParams[apiKey] = reqParams[key];
              delete reqParams[key];
            }
          });
        }
        console.log(">>>reqParams2:", reqParams);
        const response = await service(reqParams);

        // 如果提供了mapData函数，转换数据
        if (mapData && response) {
          return mapData(response);
        } else if (responseParamsMapping) {
          // 如果提供了responseParamsMapping，转换数据
          Object.entries(responseParamsMapping).forEach(([apiKey, mapKey]) => {
            if (mapKey === T_TOTAL || mapKey === T_RECORDS) {
              response[mapKey] = response[apiKey];
              delete response[apiKey];
            }
          });
        }
        console.log(">>>response", response);
        return response;
      } catch (error) {
        console.error("获取数据错误:", error);
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      onError(err, key, config) {
        message.error(err?.message || err?.msg || "请求错误");
      },
    },
  );
  const defaultData = {
    [T_TOTAL]: 0,
    [T_RECORDS]: [],
  };
  const data = swrData || defaultData;

  const refresh = useCallback(() => {
    return mutate();
  }, [mutate]);

  const updateParams = useCallback((newParams: Partial<ParamsType>) => {
    setParams((prev) => ({ ...prev, ...newParams }));
  }, []);

  const resetParams = useCallback(
    (newParams: Partial<ParamsType> = {}) => {
      const pageSize = initialParams?.[T_SIZE] || 10;
      setParams({
        [T_CURRENT]: 1,
        [T_SIZE]: pageSize,
        ...newParams,
      });
    },
    [initialParams],
  );

  const handlePaginationChange = useCallback(
    (page: number, pageSize?: number) => {
      setParams((prev) => ({
        ...prev,
        [T_CURRENT]: page,
        [T_SIZE]: pageSize || prev[T_SIZE],
      }));
    },
    [],
  );

  return {
    data,
    params,
    error,
    isLoading,
    isValidating,
    refresh,
    updateParams,
    resetParams,
    handlePaginationChange,
  };
};
