import useSWR from "swr";
import { fetcher } from "@/utils/fetcher";
import { AnnouncementConfigOrder, GetOrdersResponse } from "../../types";
import { URL_PREFIX } from "../../const";
import { useAppCode } from "@/hooks";

export function usePublishedData(options?: {
  onSuccess: (orders: AnnouncementConfigOrder[]) => void;
}) {
  const { onSuccess } = options || {};
  const appCode = useAppCode();
  const { data, isLoading, mutate } = useSWR(
    [URL_PREFIX + "/get_orders", appCode],
    ([url, appCode]) =>
      fetcher<GetOrdersResponse>(
        url + `?appcode=${appCode}`,
        {},
        {
          method: "GET",
        },
      ),
    {
      onSuccess(data) {
        onSuccess?.(data.orders || []);
      },
    },
  );
  const orders = data?.orders;
  return {
    orders,
    mutate,
    isLoading,
  };
}
