import * as React from "react";
import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    fill="currentColor"
    {...props}
  >
    <g filter="url(#a)">
      <path
        fill="currentColor"
        d="M14.459 7.732c.147 0 .513.05.704.535.049.12.089.271.129.476.063.307.114.7.168 1.117l.01.076.053.377c.02.153.041.304.063.45a.734.734 0 0 1 .56-.266l.06.004c.146.013.421.085.586.441.04.085.072.187.107.312.017.064.029.133.043.203l.01.002v-.027a1.669 1.669 0 0 1 3.335-.098 1.273 1.273 0 0 1 2.19 1.073 1.203 1.203 0 1 1 .32 2.36h-2.515c.067.073.132.145.201.226.156.183.352.41.712.775.053.028.17.067.262.097l.028.01c.192.062.392.129.566.227.48.267.89.744 1.148 1.095.24.33.525.797.6 1.126.068.285-.017.486-.102.593-.231.329-.6.347-.868.355-.222.01-.476.023-.757.152-.353.164-.524.308-.71.467l-.033.027c-.089.08-.182.16-.298.245-.49.36-1.434.797-2.814 1.3-.944.343-1.949.663-2.114.686l-.09.013-.077.01c-.322.04-.78.097-1.298.097-1.22 0-2.206-.313-2.936-.93a1.458 1.458 0 0 1-.404.11l-3.896.01a1.509 1.509 0 1 1 .49-2.966 1.612 1.612 0 0 1 2.542-1.002c.018-.167.042-.328.072-.48a28.742 28.742 0 0 1-1.803-.824c-1.7-.85-1.875-1.107-1.964-1.246-.062-.093-.258-.392-.423-1.835-.075-.645-.137-1.49-.026-1.874a.719.719 0 0 1 .396-.467.646.646 0 0 1 .262-.053c.36 0 .584.258.882.65.128.172.28.385.445.616l.254.354.109.154c.313.445.654.926.965 1.329l.079.1c.056.07.107.135.156.193a.825.825 0 0 1 .276-.311.654.654 0 0 1 .365-.111c.365 0 .597.285.797.539a26.193 26.193 0 0 1 .52.69c.66-.553 1.515-1.052 2.276-1.314.09-.031.174-.058.263-.084a16.16 16.16 0 0 1-.553-1.131c-.605-1.367-.591-1.71-.587-1.822.01-.298.134-.94.25-1.393a5.99 5.99 0 0 1 .227-.748l.079-.18c.11-.217.322-.51.708-.51Zm-7.532 3.615c-.218.098.07 2.862.329 3.25.235.362 3.018 1.702 3.998 2.03-.153.447-.25 1.07-.226 1.72.014.048.025.096.034.145h.053a1.483 1.483 0 0 1 1.109 2.467c1.236.94 2.993.717 3.756.62l.038-.006c.628-.111 3.592-1.144 4.63-1.914.096-.07.181-.144.268-.219.21-.18.435-.375.885-.583.395-.182.747-.196.999-.206.191-.007.325-.013.377-.087.097-.144-.673-1.479-1.443-1.906-.123-.066-.29-.12-.452-.175-.218-.072-.429-.142-.518-.234-.396-.399-.61-.647-.775-.84a4.095 4.095 0 0 0-.728-.7l-.052-.04-.074-.053c-1.141-.81-3.055-1.038-4.871-.41-.882.302-1.954.985-2.559 1.662-.124-.154-.282-.37-.444-.592-.357-.486-.734-1-.809-.95-.116.077-.122.27-.128.456-.006.185-.013.363-.126.416-.343.155-1.384-1.331-2.204-2.503-.532-.76-.972-1.388-1.067-1.348Zm6.761 4.413c.357-.236 1.025.138 1.493.837.467.698.56 1.46.209 1.696-.357.236-1.024-.139-1.492-.838-.467-.698-.56-1.454-.21-1.695Zm.753-7.39c-.24 0-.623 1.814-.636 2.22-.017.445 1.107 2.907 1.36 3.04.01.005.017.008.025.006a1.02 1.02 0 0 1 .142-.032c.143-.024.344-.041.537-.054l.603-.026c.014 0 .022-.025.028-.07.01-.076.01-.205.002-.365a8.05 8.05 0 0 0-.044-.522 10.506 10.506 0 0 0-.143-.943c-.01-.054-.02-.104-.032-.151-.05-.201-.1-.334-.15-.34-.111-.008-.238.279-.364.565-.125.283-.249.563-.357.557-.297-.022-.449-1.182-.586-2.229-.114-.867-.217-1.657-.385-1.657Z"
      />
    </g>
    <defs>
      <filter
        id="a"
        width={33}
        height={29.535}
        x={-1.5}
        y={2.107}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1.875} />
        <feGaussianBlur stdDeviation={3.75} />
        <feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.0901961 0 0 0 0 0.101961 0 0 0 0.25 0" />
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_52_978" />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_52_978"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default SvgComponent;
