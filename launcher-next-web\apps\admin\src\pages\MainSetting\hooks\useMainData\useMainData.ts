import { fetcher } from "@/utils/fetcher";
import useS<PERSON> from "swr";
import { Response } from "./types";
import { useCurrentLangValue } from "../useCurrentLang";
import { useCurrentSourceValue } from "../useCurrentSource";
import { DataSource } from "../../types";
import { DATA_KEY } from "./const";
import { useMutateMainData } from "./hooks/useMutateMainData";
import { useAppCode } from "@/hooks";

export function useMainData() {
  const appCode = useAppCode();
  const { data, isLoading } = useSWR([DATA_KEY, appCode], ([url, appCode]) =>
    fetcher<Response>(url, { appcode: appCode }),
  );
  const configList = data?.main_page_configs;
  const mutate = useMutateMainData();
  const currentLang = useCurrentLangValue();
  const currentSource = useCurrentSourceValue();
  const currentConfig = configList?.find(
    (config) => config.language === currentLang,
  );
  const currentConfigData =
    currentSource === DataSource.Draft
      ? currentConfig?.modify_data
      : currentConfig?.data;
  return {
    isLoading,
    configList,
    currentConfig,
    currentConfigData,
    mutate,
  };
}
