import useSWRMutation from "swr/mutation";
import { fetcher } from "@/utils/fetcher";
import { URL_PREFIX } from "../../const";
import { useLauncherPackageListKey } from "../useLauncherPackageList/useLauncherPackageListKey";
import type { PublishRequest } from "./types";

export function usePublish() {
  const key = useLauncherPackageListKey();
  const { trigger, isMutating } = useSWRMutation(key, publish);
  return { trigger, isMutating };
}

async function publish(_: any, { arg }: { arg: PublishRequest }): Promise<any> {
  return fetcher(`${URL_PREFIX}/publish`, arg);
}
