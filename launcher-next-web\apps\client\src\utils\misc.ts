export function randomString(e = 32) {
    const t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    const a = t.length
    let n = ""
    for (let i = 0; i < e; i += 1) n += t.charAt(Math.floor(Math.random() * a))
    return n
}
// TODO: 此处之后可以搞个 router 来取
export function getAppCode() {
    // 使用正则表达式从 URL 中提取最后一个路径部分
    const path = location.href.split("/")
    const appCode = path[path.length - 1]
    return appCode
}

export function getQtPath() {
    const isQtClient = typeof qt !== "undefined"
    const qt_resources_url = isQtClient ? "qrc:///web" : "./qt_resources"
    return qt_resources_url
}
