import useSWR from "swr";
import { fetcher } from "@/utils/fetcher";
import { SidebarConfigOrder, GetSidebarOrdersResponse } from "../../types";
import { URL_PREFIX } from "../../const";
import { useAppCode } from "@/hooks";

export function usePublishedSidebar() {
  const appCode = useAppCode();
  const { data, isLoading, mutate } = useSWR(
    [URL_PREFIX + "/get_orders", appCode],
    ([url, appCode]) =>
      fetcher<GetSidebarOrdersResponse>(
        url + `?appcode=${appCode}`,
        {},
        {
          method: "GET",
        },
      ),
  );
  const orders = data?.orders;
  return {
    orders,
    mutate,
    isLoading,
  };
}
