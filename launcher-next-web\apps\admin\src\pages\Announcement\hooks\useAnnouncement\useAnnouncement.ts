import useS<PERSON> from "swr";
import { fetcher } from "@/utils/fetcher";
import { AnnouncementFilter, ListResponse } from "./types";
import { useAnnouncementKey } from "./hooks";
import { message } from "antd";

export function useAnnouncement() {
  const key = useAnnouncementKey();
  const { data, isLoading } = useSWR(
    key,
    ([url, appCode, { page, searchVal, sort, filter }]) =>
      fetcher<ListResponse>(url, {
        appcode: appCode,
        page_num: page,
        page_size: 10,
        search_keywords: getSearchKeywords({ searchVal }),
        order_bys: sort,
        filter: getFilter(filter),
      }),
    {
      onError(err, key, config) {
        message.error(err?.message || "请求错误");
      },
    },
  );
  const announcements = data?.announcement_configs;
  const total = data?.total_count;

  return {
    announcements,
    isLoading,
    total,
  };
}

function getSearchKeywords({ searchVal }: { searchVal: string }) {
  const keywords = [];
  if (searchVal) {
    keywords.push({ field: "description", keyword: searchVal });
  }
  return keywords;
}

function getFilter(filter?: {
  tab_id?: string[];
  state?: number[];
  language?: string[];
}) {
  const filters: AnnouncementFilter = {};
  if (filter?.tab_id) {
    filters.tab_ids = filter.tab_id;
  }
  if (filter?.state) {
    filters.states = filter.state;
  }
  if (filter?.language) {
    filters.languages = filter.language;
  }
  return filters;
}
