import {
  Modal,
  Button,
  Typo<PERSON>,
  message,
  Flex,
  Space,
  Form,
  Input,
  Radio,
} from "antd";
import { useState, useCallback, useRef } from "react";
import {
  LauncherPackageConfig,
  useGetNote,
  useUpdateNote,
} from "@/pages/PackageManage/hooks/useLauncherPackageList";
import { curEnvLabelList } from "@/hooks/useLangList";

export function UpdateNote({ config }: { config: LauncherPackageConfig }) {
  const { appcode = "", version = "", sub_channel, channel, platform } = config;
  const [form] = Form.useForm();
  const { trigger: getNoteTrigger } = useGetNote();
  const { trigger } = useUpdateNote();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalLoading, setModalLoading] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const commonParams = {
    appcode,
    channel,
    sub_channel,
    platform,
    version,
  };

  const handleOpen = useCallback(async () => {
    setModalOpen(true);
    setModalLoading(true);
    try {
      const res = await getNoteTrigger(commonParams);
      form.setFieldsValue(res || {});
      setModalLoading(false);
    } catch (e) {
      message.error("获取日志失败");
      setModalLoading(false);
    }
  }, [commonParams]);

  const handleOk = useCallback(
    async (formData: any) => {
      // console.log("formData:", formData);
      const params = {
        ...commonParams,
        ...formData,
      };
      try {
        setConfirmLoading(true);
        await trigger(params);
        setModalOpen(false);
        setConfirmLoading(false);
        message.success("提交成功");
      } catch (error: any) {
        setConfirmLoading(false);
        message.error(error.message || "提交失败");
      }
    },
    [commonParams, trigger],
  );

  return (
    <>
      <Typography.Link style={{ whiteSpace: "nowrap" }} onClick={handleOpen}>
        编辑更新日志
      </Typography.Link>
      <Modal
        title="编辑更新日志"
        width={800}
        destroyOnClose
        loading={modalLoading}
        confirmLoading={confirmLoading}
        open={modalOpen}
        style={{ top: "5vh" }}
        styles={{ body: { padding: "10px" } }}
        onCancel={() => setModalOpen(false)}
        onOk={() => form?.submit()}
      >
        <Form
          form={form}
          layout={"horizontal"}
          labelAlign="left"
          labelCol={{ span: 5 }}
          preserve={false}
          onFinish={handleOk}
          // initialValues={initialValues}
        >
          <Form.Item
            label="更新日志"
            required={true}
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
          >
            <div
              style={{
                padding: "20px",
                backgroundColor: "rgba(0, 0, 0, 0.05)",
                maxHeight: "50vh",
                overflow: "auto",
              }}
            >
              {curEnvLabelList.map((item) => {
                return (
                  <Form.Item
                    key={item.value}
                    labelCol={{ span: 3 }}
                    name={["release_notes", item.value]}
                    label={item.label}
                    required={false}
                    rules={[
                      {
                        required: true,
                        message: "请填写内容",
                      },
                      {
                        max: 2000,
                        message: "最多2000字",
                      },
                    ]}
                  >
                    <Input.TextArea
                      rows={3}
                      showCount={true}
                      maxLength={2000}
                      placeholder="请输入"
                    />
                  </Form.Item>
                );
              })}
            </div>
          </Form.Item>
          <Form.Item
            name="display_in_release_list"
            label="日志列表同步展示"
            initialValue={false}
          >
            <Radio.Group
              options={[
                {
                  label: "不展示",
                  value: false,
                },
                {
                  label: "同步展示",
                  value: true,
                },
              ]}
            ></Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
