import { PresetStatusColorType } from "antd/es/_util/colors";
import { AppEnvType, ReleaseState } from "@/pages/LauncherManage/types";
import { UpdateType, PublishType } from "../usePublishPackage/types";

export enum PackagePublishState {
  LauncherState_UNPUBLISH = "LauncherState_UNPUBLISH", // 未发布
  LauncherState_ONLINE = "LauncherState_ONLINE", // 上线
  LauncherState_RESERVE = "LauncherState_RESERVE", // 预留值无意义
  LauncherState_Gray = "LauncherState_Gray", // 灰度发布
  // DELETE = "DELETE", // 删除
  // OFFLINE = "OFFLINE", // 下架
}

export const PackagePublishStateConfig: {
  [key in PackagePublishState]?: {
    text: string;
    color?: string;
    status?: PresetStatusColorType;
  };
} = {
  [PackagePublishState.LauncherState_UNPUBLISH]: {
    text: "未发布",
    status: "default",
  },
  [PackagePublishState.LauncherState_ONLINE]: {
    text: "已发布",
    status: "success",
  },
  [PackagePublishState.LauncherState_Gray]: {
    text: "已灰度发布",
    status: "warning",
  },
  [PackagePublishState.LauncherState_RESERVE]: {
    text: "",
    status: "default",
  },
};

export interface LauncherPackageListRequest {
  appcode: string;
  versions?: string; // 包体版本
  states?: PackagePublishState; // 启动器发布状态
  publish_start_time?: number; // 发布开始日期
  publish_end_time?: number; // 发布束日期
  page_num?: number; // 页码
  page_size?: number; // 页大小
}

export interface LauncherPackageConfig {
  id: string;
  appcode?: string;
  channel?: string; // 渠道
  sub_channel?: string; // 子渠道
  platform?: string; // 平台
  state?: PackagePublishState; // 游戏包发布状态
  version?: string; // 包体版本
  cuser?: string; //  创建人
  muser?: string; // 更新人
  package_size?: number; // 包体大小
  package_size_str?: string; // 包体大小带单位
  zip_package_url?: string; // 全量包下载地址
  gray_zip_package_url?: string; // 灰度包下载地址
  exe_url?: string; // 全量安装包下载地址
  gray_exe_url?: string; // 灰度安装包下载地址
  register_time?: number; // 注册时间
  publish_time?: number; // 发布时间
  modify_time?: number; //  最后更新时间
  latest_version?: string; // 最新线上版本
  force_update_version: string; // 最低可玩版本
  launcher_app_env_type?: AppEnvType; // 应用类型
  launcher_release_state?: ReleaseState; // 是否外部可访问
  launcher_type?: string; // 启动器类型
  gray_latest_version?: string; // 最新线上灰度版本
  gray_ratio?: number; // 灰度发布比例
  update_type: UpdateType; // 包更新类型：1-强更，2-非强更提示，3-非强更不提示
  publish_type: PublishType; // 发布类型
}

export interface ListResponse {
  launchers: LauncherPackageConfig[];
  total: string;
}
